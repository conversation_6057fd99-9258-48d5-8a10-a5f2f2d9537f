import os
import shutil
import json
import sys

# --- 配置 ---
SOURCE_APP_NAME = "weather365"
BASE_APPS_DIR = "packages/apps"
SOURCE_UNIONAD_RELPATH = os.path.join("src", "UnionAd") # UnionAd 相对于 app 根目录的路径
MANIFEST_RELPATH = os.path.join("src", "manifest.json") # manifest.json 相对于 app 根目录的路径

# 要添加到 router.pages 的内容
ROUTER_PAGES_ADDITIONS = {
    "UnionAd/AdLanding": {
        "component": "index"
    },
    "UnionAd/AdReward": {
        "component": "index"
    }
}

# 要添加到 router.pages 的内容
FEATURES_ADDITIONS = [
    { "name": "system.sensor" }
]

FEATURES_DELETIONS = [
    {
        "name": "service.wbaccount",
        "params": {
            "appKey": "xdsdf"
        }
    }
]

CONFIG_ADDITIONS = {
    "requestNotificationPermission": False
}

# 要添加到 display.pages 的内容
DISPLAY_PAGES_ADDITIONS = {
    "UnionAd/AdReward": {
        "fullScreen": True,
        "titleBar": False,
        "statusBarImmersive": True,
        "menu": False,
        "menuBarData": {
            "menuBar": False
        }
    },
    "UnionAd/AdLanding": {
        "fullScreen": True,
        "titleBar": False,
        "statusBarImmersive": True,
        "menu": False,
        "menuBarData": {
            "menuBar": False
        }
    }
}
# --- 配置结束 ---

# --- 脚本逻辑 ---
script_dir = os.path.dirname(os.path.abspath(__file__))
# 假设脚本放在项目根目录，如果不是，需要调整 base_dir 的计算方式
base_dir = os.path.join(script_dir, "../")
# 或者如果你总是从项目根目录运行脚本，可以直接用:
# base_dir = os.getcwd()

apps_dir_abs = os.path.join(base_dir, BASE_APPS_DIR)
source_app_dir_abs = os.path.join(apps_dir_abs, SOURCE_APP_NAME)
source_unionad_dir_abs = os.path.join(source_app_dir_abs, SOURCE_UNIONAD_RELPATH)

if not os.path.isdir(apps_dir_abs):
    print(f"错误：基础应用目录不存在: {apps_dir_abs}")
    sys.exit(1)

if not os.path.isdir(source_unionad_dir_abs):
    print(f"错误：源 UnionAd 目录不存在: {source_unionad_dir_abs}")
    sys.exit(1)

print(f"源 UnionAd 目录: {source_unionad_dir_abs}")
print("-" * 30)

# 遍历 packages/apps/ 下的所有项
for item_name in os.listdir(apps_dir_abs):
    app_dir_abs = os.path.join(apps_dir_abs, item_name)

    # 确保是目录并且不是源 app
    if os.path.isdir(app_dir_abs) and item_name != SOURCE_APP_NAME:
        target_app_name = item_name
        print(f"处理应用: {target_app_name}")

        target_src_dir_abs = os.path.join(app_dir_abs, "src")
        target_unionad_dir_abs = os.path.join(target_src_dir_abs, "UnionAd")
        manifest_path_abs = os.path.join(app_dir_abs, MANIFEST_RELPATH)

        # 检查目标 src 目录是否存在
        if not os.path.isdir(target_src_dir_abs):
            print(f"  跳过: 'src' 目录不存在于 {target_app_name}")
            continue

        # 1. 同步 UnionAd 目录
        try:
            # 如果目标已存在，先删除，确保是完全同步
            if os.path.exists(target_unionad_dir_abs):
                print(f"  删除旧的 UnionAd 目录: {target_unionad_dir_abs}")
                shutil.rmtree(target_unionad_dir_abs)

            print(f"  复制 UnionAd 到: {target_unionad_dir_abs}")
            shutil.copytree(source_unionad_dir_abs, target_unionad_dir_abs)
            print(f"  UnionAd 目录同步成功.")

        except Exception as e:
            print(f"  错误: 同步 UnionAd 目录时出错: {e}")
            continue # 出错则跳过此应用的后续步骤

        # 2. 修改 manifest.json
        if os.path.isfile(manifest_path_abs):
            print(f"  修改 manifest.json: {manifest_path_abs}")
            try:
                # 读取 JSON 文件，指定编码防止中文乱码
                with open(manifest_path_abs, 'r', encoding='utf-8') as f:
                    manifest_data = json.load(f)

                # --- 修改 router.pages ---
                # 使用 setdefault 安全地创建嵌套字典（如果不存在）
                router = manifest_data.setdefault('router', {})
                pages = router.setdefault('pages', {})
                # 添加或更新条目
                for key, value in ROUTER_PAGES_ADDITIONS.items():
                    pages[key] = value
                print("    router.pages 更新成功.")

                # --- 修改 router.pages ---
                # 使用 setdefault 安全地创建嵌套字典（如果不存在）
                features = manifest_data.setdefault('features', {})
                # 添加或更新条目
                for item in FEATURES_ADDITIONS:
                    if item not in features:
                        features.append(item)
                print("    features 更新成功.")

                for item in FEATURES_DELETIONS:
                    if item in features:
                        features.remove(item)
                print("    features 更新成功.")

                config = manifest_data.setdefault('config', {})
                # 添加或更新条目
                for key, value in CONFIG_ADDITIONS.items():
                    config[key] = value
                print("    features 更新成功.")

                # --- 修改 display.pages ---
                display = manifest_data.setdefault('display', {})
                display_pages = display.setdefault('pages', {})
                # 添加或更新条目
                for key, value in DISPLAY_PAGES_ADDITIONS.items():
                    display_pages[key] = value
                print("    display.pages 更新成功.")

                # 写回 JSON 文件，保持缩进格式，确保非 ASCII 字符正常显示
                with open(manifest_path_abs, 'w', encoding='utf-8') as f:
                    json.dump(manifest_data, f, indent=2, ensure_ascii=False) # 使用 indent=2 保留原格式，可改为4
                    f.write('\n') # JSON 标准通常不在末尾加换行，但很多编辑器会，这里加上以保持一致性

                print(f"  manifest.json 修改成功.")

            except json.JSONDecodeError as e:
                print(f"  错误: 解析 manifest.json 文件失败: {e}")
            except Exception as e:
                print(f"  错误: 处理 manifest.json 时发生未知错误: {e}")
        else:
            print(f"  警告: manifest.json 文件未找到: {manifest_path_abs}")

        print("-" * 30)

print("脚本执行完毕.")