#!/usr/bin/env node

const fs = require('fs-extra')
const path = require('path')
const { execSync } = require('child_process')

const packages = ['weather365', 'ocr-shsm', 'xydj', 'wnbbx', 'ocr-qnds']

const distDir = path.resolve(__dirname, '../dist')
async function main() {
  await fs.remove(distDir)
  await fs.mkdirs(distDir)
  for (const pkg of packages) {
    const srcDir = path.resolve(__dirname, `../packages/apps/${pkg}/dist`)
    await fs.remove(srcDir)
    execSync(`lerna run release --scope ${pkg} --stream`, { stdio: 'inherit' })
    await fs.copy(srcDir, distDir)
  }
}

// Run the script
main().catch(error => {
  console.error(error)
  process.exit(1)
})
