<template>
  <div class="wrap {{bgColor}}" onclick="handleClick">
    <stack>
      <div class="box"></div>
      <div class="circle-box {{activeClass}}">
        <div class="circle"></div>
      </div>
      <text class="open-text font-icon" if="{{checked&&open}}">{{
        open || "开启"
      }}</text>
      <text class="close-text font-icon" if="{{!checked&&close}}">{{
        close || "关闭"
      }}</text>
    </stack>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";
@font-face {
  font-family: iconfont;
  src: url("../icon/iconfonts.ttf");
}

.font-icon {
  font-family: iconfont;
  text-align: center;
}

.wrap {
  width: @SwitchWidth;
  height: @SwitchHeight;
  border-radius: @SwitchCircleSize;
  .open-text {
    color: #ffffff;
    font-size: 20px * @ratio;
    padding-left: 10px * @ratio;
  }
  .close-text {
    color: #ffffff;
    font-size: 20px * @ratio;
    padding-left: 45px * @ratio;
  }
}

stack {
  align-items: center;
}

.box {
  width: 100%;
  height: 100%;
  border-radius: @SwitchHeight + 0px;
}

.switch-on {
  background-color: @SwitchOnColor;
}

.switch-off {
  background-color: @SwitchOffColor;
}

.switch-disabled-on {
  .box {
    background-color: @SwitchDisabledOnColor;
  }
  .circle {
  }
}

.switch-disabled-off {
  .box {
    background-color: @SwitchDisabledOffColor;
  }
  .circle {
    background-color: #cccccc;
  }
}

.circle-box {
  padding-left: @SwitchPadding + 0px;
  padding-right: @SwitchPadding + 0px;
  width: 100%;
}

.circle-checked {
  animation-name: checked;
  animation-duration: 100ms;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transform: translateX(@SwitchCircleTranslateX);
}

.circle-dischecked {
  animation-name: dischecked;
  animation-duration: 100ms;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transform: translateX(0);
}

.circle {
  width: @SwitchCircleSize;
  height: @SwitchCircleSize;
  border-radius: @SwitchCircleSize;
  background-color: @SwitchCircleColor;
}

@keyframes checked {
  0% {
    transform: translateX(0px);
  }
  100% {
    transform: translateX(@SwitchCircleTranslateX + 0px);
  }
}

@keyframes dischecked {
  0% {
    transform: translateX(@SwitchCircleTranslateX + 0px);
  }
  100% {
    transform: translateX(0px);
  }
}
</style>

<script>
export default {
  data() {
    return {
      activeClass: "circle-checked",
      bgColor: "circle-checked",
      checked: this.value
    };
  },
  props: {
    value: {
      default: false
    },
    open: {
      default: ""
    },
    close: {
      default: ""
    },
    disabled: {
      default: false
    },
    name: {
      default: ""
    }
  },

  onInit() {
    this.isChecked();
    this.$watch("value", "checkedChange");
    this.$watch("disabled", "configChange");
  },
  checkedChange() {
    this.checked = this.value;
    this.isChecked();
  },
  configChange() {
    this.isChecked();
  },
  handleClick() {
    let disabled = this.disabled;
    if (!disabled) {
      this.checked = !this.checked;
      this.isChecked();
      this.$emit("change", { checked: this.checked, name: this.name });
    }
  },
  isChecked() {
    if (this.disabled) {
      if (this.checked) {
        this.activeClass = "circle-checked";
        this.bgColor = "switch-disabled-on";
      } else {
        this.activeClass = "circle-dischecked";
        this.bgColor = "switch-disabled-off";
      }
    } else {
      if (this.checked) {
        this.activeClass = "circle-checked";
        this.bgColor = "switch-on";
      } else {
        this.activeClass = "circle-dischecked";
        this.bgColor = "switch-off";
      }
    }
  }
};
</script>
