<template>
  <div class="badge-wrap">
    <stack if="{{dot}}" class="badge-wrap-dot">
      <div class="badge-wrap-dot-slot">
        <slot></slot>
      </div>
      <div class="dot-class" show="{{hasCount}}"></div>
    </stack>
    <stack else class="badge-wrap-count">
      <div class="badge-wrap-count-slot" style="margin-right: {{offset}}px;">
        <slot></slot>
      </div>
      <div
        class="{{ moreThenTen  ? 'count-class-more-then-ten' : 'count-class' }}"
        show="{{hasCount}}"
      >
        <text>{{ finalCount }}</text>
      </div>
    </stack>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";

.badge-wrap {
  flex-direction: column;

  &-dot {
    flex-direction: column;
    &-slot {
      margin-top: 10px * @ratio;
      margin-right: 10px * @ratio;
    }
  }

  &-count {
    flex-direction: column;
    &-slot {
      margin-top: 23px * @ratio;
    }
  }
}

.dot-class {
  background-color: @error-color;
  border-radius: 50px * @ratio;
  width: 18px * @ratio;
  height: 18px * @ratio;
  align-self: flex-end;
  border: 2px * @ratio solid #ffffff;
}

.count-class-more-then-ten {
  background-color: @error-color;
  justify-content: center;
  border-radius: 20px * @ratio;
  align-self: flex-end;
  height: 45px * @ratio;
  border: 2px * @ratio solid #ffffff;
  text {
    color: #ffffff;
    font-size: 23px * @ratio;
    margin: 10px * @ratio 20px * @ratio;
  }
}

.count-class {
  background-color: @error-color;
  justify-content: center;
  border-radius: 50px * @ratio;
  align-self: flex-end;
  height: 45px * @ratio;
  width: 45px * @ratio;
  border: 2px * @ratio solid #ffffff;
  text {
    color: #ffffff;
    font-size: 25px * @ratio;
    margin: 10px * @ratio;
  }
}
</style>

<script>
export default {
  props: {
    dot: {
      type: Boolean,
      default: false
    },
    overflowCount: {
      type: Number,
      default: 99
    },
    count: {
      type: Number,
      default: 0
    }
  },
  computed: {
    finalCount() {
      if (!this.count || parseInt(this.count) <= 0) return "";
      return parseInt(this.count) > parseInt(this.overflowCount)
        ? `${this.overflowCount}+`
        : this.count;
    },
    hasCount() {
      let result = false;
      if (this.count && parseInt(this.count) > 0) {
        result = true;
      }
      return result;
    },
    moreThenTen() {
      return parseInt(this.count) >= 10 ? true : false;
    },
    offset() {
      let l =
        parseInt(this.count) > parseInt(this.overflowCount)
          ? this.overflowCount.toString().length
          : this.count.toString().length;
      return 20 + 10 * (l - 1);
    }
  }
};
</script>
