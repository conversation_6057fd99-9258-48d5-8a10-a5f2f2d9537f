{"version": 3, "file": "qacharts-min.js", "sources": ["../src/util/event.js", "../src/config.js", "../src/util/util.js", "../node_modules/lodash.clonedeep/index.js", "../src/calculate/calOptions.js", "../src/calculate/calCurrentIndex.js", "../src/calculate/calTooltipData.js", "../src/calculate/calSeriesMap.js", "../src/calculate/calSeriesColor.js", "../src/calculate/calLegendData.js", "../src/calculate/calAxisData.js", "../src/calculate/calAxisRadarData.js", "../src/calculate/calChartBarData.js", "../src/calculate/calChartLineData.js", "../src/calculate/calChartPieData.js", "../src/calculate/calChartRadarData.js", "../src/calculate/calChartScatterData.js", "../src/calculate/calChartFunnelData.js", "../src/calculate/calChartCandlestickData.js", "../src/calculate/calChartHeatmapData.js", "../node_modules/d3-hierarchy/src/cluster.js", "../node_modules/d3-hierarchy/src/hierarchy/count.js", "../node_modules/d3-hierarchy/src/hierarchy/index.js", "../node_modules/d3-hierarchy/src/hierarchy/each.js", "../node_modules/d3-hierarchy/src/hierarchy/eachAfter.js", "../node_modules/d3-hierarchy/src/hierarchy/eachBefore.js", "../node_modules/d3-hierarchy/src/hierarchy/sum.js", "../node_modules/d3-hierarchy/src/hierarchy/sort.js", "../node_modules/d3-hierarchy/src/hierarchy/path.js", "../node_modules/d3-hierarchy/src/hierarchy/ancestors.js", "../node_modules/d3-hierarchy/src/hierarchy/descendants.js", "../node_modules/d3-hierarchy/src/hierarchy/leaves.js", "../node_modules/d3-hierarchy/src/hierarchy/links.js", "../node_modules/d3-hierarchy/src/array.js", "../node_modules/d3-hierarchy/src/pack/enclose.js", "../node_modules/d3-hierarchy/src/pack/siblings.js", "../node_modules/d3-hierarchy/src/accessors.js", "../node_modules/d3-hierarchy/src/constant.js", "../node_modules/d3-hierarchy/src/pack/index.js", "../node_modules/d3-hierarchy/src/treemap/round.js", "../node_modules/d3-hierarchy/src/treemap/dice.js", "../node_modules/d3-hierarchy/src/stratify.js", "../node_modules/d3-hierarchy/src/tree.js", "../node_modules/d3-hierarchy/src/treemap/slice.js", "../node_modules/d3-hierarchy/src/treemap/squarify.js", "../node_modules/d3-hierarchy/src/treemap/index.js", "../node_modules/d3-hierarchy/src/treemap/resquarify.js", "../node_modules/d3-hierarchy/src/partition.js", "../node_modules/d3-hierarchy/src/treemap/binary.js", "../node_modules/d3-hierarchy/src/treemap/sliceDice.js", "../src/calculate/calChartTreemapData.js", "../src/util/tag-cloud.js", "../src/calculate/calChartTagCloudData.js", "../src/calculate/calChartsData.js", "../src/util/timing.js", "../src/util/animation.js", "../src/draw/drawTooltip.js", "../src/draw/drawBackground.js", "../src/draw/drawLegend.js", "../src/draw/drawAxis.js", "../src/draw/drawAxisRadar.js", "../src/draw/drawCharts/drawChartBar.js", "../src/draw/drawCharts/drawChartLine.js", "../src/draw/drawCharts/drawChartPie.js", "../src/draw/drawCharts/drawChartRadar.js", "../src/draw/drawCharts/drawChartScatter.js", "../src/draw/drawCharts/drawChartFunnel.js", "../src/draw/drawCharts/drawChartCandlestick.js", "../src/draw/drawCharts/drawChartHeatmap.js", "../src/draw/drawCharts/drawChartTreemap.js", "../src/draw/drawCharts/drawChartTagCloud.js", "../src/draw/drawCharts/drawCharts.js", "../src/app.js"], "sourcesContent": ["export default class Event {\n  constructor() {\n    this.events = {}\n  }\n\n  /**\n   * 事件监听\n   * @param {String} type\n   * @param {String} listener\n   */\n  addEventListener(type, listener = function () {}) {\n    this.events[type] = this.events[type] || []\n    this.events[type].push(listener)\n  }\n\n  /**\n   * 触发事件\n   * @param {String} type\n   * @param  {...Any} params\n   */\n  trigger(type, ...params) {\n    if (!!this.events[type]) {\n      this.events[type].forEach(listener => {\n        try {\n          listener.apply(null, params)\n        } catch (e) {\n          console.error(e)\n        }\n      })\n    }\n  }\n}\n", "/**\n * 默认参数配置\n */\n\nexport default {\n  animation: true,\n  animationDuration: 1000,\n  animationTiming: 'easeInOut', // easeIn, easeOut, easeInOut, linear\n  backgroundColor: '#ffffff',\n  colors: ['#7cb5ec', '#f7a35c', '#434348', '#90ed7d', '#f15c80', '#8085e9'], // wxcharts调色盘\n  tooltip: {\n    show: false,\n    data: [],\n    maxTextWidth: 0,\n    backgroundColor: '#000000',\n    backgroundRadius: 5,\n    backgroundOpacity: 0.7,\n    padding: 10,\n    itemGap: 5,\n    iconRadius: 5,\n    iconGap: 5,\n    textStyle: {\n      fontSize: 15,\n      color: '#ffffff',\n      lineHeight: 15,\n    },\n    axisPointer: {\n      type: 'line', // line shadow\n      lineStyle: {\n        lineWidth: 1,\n        color: '#808080',\n        opacity: 1,\n      },\n      shadowStyle: {\n        color: '#969696',\n        opacity: 0.3,\n      },\n      cross: {\n        show: true,\n        lineWidth: 1,\n        lineColor: '#808080',\n        lineDash: [5, 10],\n        lineOpacity: 1,\n        backgroundColor: '#999999',\n        backgroundOpacity: 1,\n        fontColor: '#ffffff',\n        fontPadding: 5,\n      },\n    },\n  },\n  label: {\n    show: true,\n    fontSize: 10,\n    color: 'auto',\n    margin: 5,\n  },\n  legend: {\n    show: true,\n    type: 'default', // default, rect, circle, line\n    marginTop: 15,\n    itemGap: 15,\n    shapeRadius: 7.5,\n    shapeWidth: 30,\n    shapeHeight: 15,\n    textStyle: {\n      fontSize: 15,\n      color: '#333333',\n      padding: 5,\n    },\n  },\n  padding: [20, 20, 20, 20],\n  yAxisCategory: {\n    show: true,\n    type: 'category', // category, value\n    boundaryGap: true,\n    axisName: {\n      show: true,\n      text: '轴线名称',\n      gap: 10,\n      textStyle: {\n        color: '#666666',\n        fontSize: 15,\n        align: 'center',\n      },\n    },\n    axisLabel: {\n      show: true,\n      gap: 5,\n      textStyle: {\n        color: '#666666',\n        fontSize: 12,\n      },\n    },\n    axisTick: {\n      show: true,\n      alignWithLabel: false, // alignWithLabel为true时，刻度线与标签对齐\n      length: 5,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#666666',\n      },\n    },\n    axisLine: {\n      show: true,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#666666',\n      },\n    },\n    axisSplitLine: {\n      show: true,\n      alignWithLabel: false, // alignWithLabel为true时，网格线与标签对齐\n      lineStyle: {\n        lineWidth: 1,\n        color: '#dddddd',\n      },\n    },\n  },\n  yAxisValue: {\n    show: true,\n    type: 'value', // category, value\n    max: 'auto',\n    min: 'auto',\n    splitNumber: 4,\n    axisName: {\n      show: true,\n      text: '轴线名称',\n      gap: 10,\n      textStyle: {\n        color: '#666666',\n        fontSize: 15,\n        align: 'center',\n      },\n    },\n    axisLabel: {\n      show: true,\n      gap: 5,\n      textStyle: {\n        color: '#666666',\n        fontSize: 12,\n      },\n    },\n    axisTick: {\n      show: true,\n      length: 5,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#666666',\n      },\n    },\n    axisLine: {\n      show: true,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#666666',\n      },\n    },\n    axisSplitLine: {\n      show: true,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#dddddd',\n      },\n    },\n  },\n  xAxisCategory: {\n    show: true,\n    type: 'category', // category, value\n    boundaryGap: true, // boundaryGap为true时, 这时候刻度只是作为分隔线，标签和数据点都会在两个刻度之间的带(band)中间\n    axisName: {\n      show: true,\n      text: '轴线名称',\n      gap: 10,\n      textStyle: {\n        color: '#666666',\n        fontSize: 15,\n      },\n    },\n    axisLabel: {\n      show: true,\n      rotate: 0,\n      gap: 5,\n      textStyle: {\n        color: '#666666',\n        fontSize: 12,\n      },\n    },\n    axisTick: {\n      show: true,\n      alignWithLabel: false, // alignWithLabel为true时，刻度线与标签对齐\n      length: 5,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#666666',\n      },\n    },\n    axisLine: {\n      show: true,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#666666',\n      },\n    },\n    axisSplitLine: {\n      show: true,\n      alignWithLabel: false, // alignWithLabel为true时，网格线与标签对齐\n      lineStyle: {\n        lineWidth: 1,\n        color: '#dddddd',\n      },\n    },\n  },\n  xAxisValue: {\n    show: true,\n    type: 'value', // category, value\n    max: 'auto',\n    min: 'auto',\n    splitNumber: 4,\n    axisName: {\n      show: true,\n      text: '轴线名称',\n      gap: 10,\n      textStyle: {\n        color: '#666666',\n        fontSize: 15,\n        align: 'center',\n      },\n    },\n    axisLabel: {\n      show: true,\n      rotate: 0,\n      gap: 5,\n      textStyle: {\n        color: '#666666',\n        fontSize: 12,\n      },\n    },\n    axisTick: {\n      show: true,\n      length: 5,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#666666',\n      },\n    },\n    axisLine: {\n      show: true,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#666666',\n      },\n    },\n    axisSplitLine: {\n      show: true,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#dddddd',\n      },\n    },\n  },\n  radarAxis: {\n    shape: 'polygon', // polygon, circle\n    center: ['50%', '50%'],\n    radius: '80%',\n    max: 'auto',\n    splitNumber: 4,\n    axisName: {\n      show: true,\n      textStyle: {\n        fontSize: 15,\n        color: '#666666',\n        margin: 10,\n      },\n    },\n    axisLine: {\n      show: true,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#cccccc',\n        opacity: 1,\n      },\n    },\n    splitLine: {\n      show: true,\n      lineStyle: {\n        lineWidth: 1,\n        color: '#cccccc',\n        opacity: 1,\n      },\n    },\n    splitArea: {\n      odd: {\n        show: true,\n        color: '#f5f5f5',\n        opacity: 1,\n      },\n      even: {\n        show: true,\n        color: '#e6e6e6',\n        opacity: 1,\n      },\n    },\n  },\n  bar: {\n    barMaxWidth: 20,\n    barMinWidth: 1,\n    barWidth: 'auto',\n    barGap: 5,\n  },\n  line: {\n    smooth: false,\n    connectNulls: false,\n    line: {\n      show: true,\n      lineWidth: 2,\n      color: 'auto',\n      opacity: 1,\n    },\n    symbol: {\n      show: true,\n      type: 'circle', // circle\n      size: 7,\n      color: 'auto',\n    },\n    area: {\n      show: false,\n      color: 'auto',\n      opacity: 0.5,\n    },\n  },\n  pie: {\n    center: ['50%', '50%'],\n    radius: [0, '80%'],\n    roseType: false, // false radius area\n    offsetAngle: 0,\n    disablePieStroke: true,\n    labelLine: {\n      lineDotRadius: 3,\n      lineWidth: 1,\n      length1: 25,\n      length2: 15,\n    },\n    title: {\n      show: false,\n      text: '主标题',\n      textStyle: {\n        fontSize: 30,\n        color: '#666666',\n        lineHeight: 30,\n      },\n      subtext: '副标题',\n      subtextStyle: {\n        fontSize: 20,\n        color: '#999999',\n        lineHeight: 20,\n      },\n      itemGap: 5,\n    },\n  },\n  radar: {\n    line: {\n      show: true,\n      lineWidth: 1,\n      color: 'auto',\n      opacity: 1,\n    },\n    area: {\n      show: false,\n      color: 'auto',\n      opacity: 0.5,\n    },\n    symbol: {\n      show: true,\n      type: 'circle', // circle\n      size: 7,\n      color: 'auto',\n    },\n  },\n  scatter: {\n    radius: 10,\n    opacity: 1,\n    lineWidth: 0,\n    strokeColor: 'auto',\n  },\n  funnel: {\n    width: 'auto',\n    height: 'auto',\n    top: '0%',\n    left: '0%',\n    right: '0%',\n    bottom: '0%',\n    max: 100,\n    min: 0,\n    gap: 5,\n    shape: 'funnel', // funnel, pyramid\n    sort: 'descending', // descending, ascending\n    funnelAlign: 'center', // left, center, right\n    label: {\n      position: 'inside', // inside, outside\n    },\n    itemStyle: {\n      borderColor: '#ffffff',\n      borderWidth: 1,\n    },\n  },\n  candlestick: {\n    barMaxWidth: 20,\n    barMinWidth: 1,\n    barWidth: 'auto',\n    itemStyle: {\n      color: '#ec0000',\n      bordercolor: '#ec0000',\n      opacity: 1,\n      color0: '#00da3c',\n      bordercolor0: '#00da3c',\n      opacity0: 1,\n      borderWidth: 1,\n    },\n    highLine: {\n      show: false,\n      lineStyle: {\n        color: '#ec0000',\n        lineWidth: 1,\n        lineDash: [10, 15],\n        opacity: 1,\n      },\n    },\n    lowLine: {\n      show: false,\n      lineStyle: {\n        color: '#ec0000',\n        lineWidth: 1,\n        lineDash: [10, 15],\n        opacity: 1,\n      },\n    },\n    bar: {\n      show: false,\n      height: 50,\n      margin: 15,\n      itemStyle: {\n        color: 'auto',\n        opacity: 1,\n      },\n      lineStyle: {\n        lineWidth: 1,\n        lineColor: '#666666',\n      },\n    },\n  },\n  heatmap: {\n    itemStyle: {\n      color: ['#BAE7FF', '#0050B3'],\n      useSplit: false,\n    },\n  },\n  treemap: {\n    splitLine: {\n      show: true,\n      lineWidth: 5,\n      color: '#ffffff',\n    },\n  },\n  tagCloud: {\n    padding: 1,\n    timeInterval: 500,\n    font: 'serif',\n    fontSize: 15,\n    rotate: 0,\n    spiral: 'archimedean', // archimedean rectangular {function}\n  },\n}\n", "/**\n * getColor 颜色参数为对象则返回渐变色\n * @param {Object} color\n * @param {Object} context\n * @param {Number} xStart\n * @param {Number} yStart\n * @param {Number} xEnd\n * @param {Number} yEnd\n */\nexport function getColor(color, context, xStart, yStart, xEnd, yEnd) {\n  if (isObject(color)) {\n    const { linearGradient, colors } = color\n    const [x0, y0, x1, y1] = linearGradient\n\n    const xSpacing = xEnd - xStart\n    const ySpacing = yEnd - yStart\n\n    const gradientColor = context.createLinearGradient(xStart + xSpacing * x0, yStart + ySpacing * y0, xStart + xSpacing * x1, yStart + ySpacing * y1)\n    colors.forEach(item => {\n      const { offset, color } = item\n      gradientColor.addColorStop(offset, color)\n    })\n\n    return gradientColor\n  }\n\n  return color\n}\n\n/**\n * HEX to HSL\n * @param {String} HEX\n * @return {Array} HSL\n */\nexport function HEX2HSL(hex) {\n  let result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex)\n\n  let r = parseInt(result[1], 16)\n  let g = parseInt(result[2], 16)\n  let b = parseInt(result[3], 16)\n\n  ;(r /= 255), (g /= 255), (b /= 255)\n  let max = Math.max(r, g, b),\n    min = Math.min(r, g, b)\n  let h,\n    s,\n    l = (max + min) / 2\n\n  if (max == min) {\n    h = s = 0 // achromatic\n  } else {\n    let d = max - min\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0)\n        break\n      case g:\n        h = (b - r) / d + 2\n        break\n      case b:\n        h = (r - g) / d + 4\n        break\n    }\n    h /= 6\n  }\n\n  s = s * 100\n  s = Math.round(s)\n  l = l * 100\n  l = Math.round(l)\n  h = Math.round(360 * h)\n\n  return [h, s, l]\n}\n\n/**\n * HSL to HEX\n * @param {Array} HSL\n * @return {String} HEX\n */\nexport function HSL2HEX(hsl) {\n  let [h, s, l] = hsl\n  let r, g, b\n\n  h /= 360\n  s /= 100\n  l /= 100\n\n  if (s === 0) {\n    r = g = b = l // achromatic\n  } else {\n    const hue2rgb = (p, q, t) => {\n      if (t < 0) t += 1\n      if (t > 1) t -= 1\n      if (t < 1 / 6) return p + (q - p) * 6 * t\n      if (t < 1 / 2) return q\n      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6\n      return p\n    }\n    const q = l < 0.5 ? l * (1 + s) : l + s - l * s\n    const p = 2 * l - q\n    r = hue2rgb(p, q, h + 1 / 3)\n    g = hue2rgb(p, q, h)\n    b = hue2rgb(p, q, h - 1 / 3)\n  }\n  const toHex = x => {\n    const hex = Math.round(x * 255).toString(16)\n    return hex.length === 1 ? '0' + hex : hex\n  }\n\n  return `#${toHex(r)}${toHex(g)}${toHex(b)}`\n}\n\n/**\n * 转化坐标\n * @param {Object} origin\n * @param {Array} center\n */\nexport function convertCoordinateOrigin(origin, center) {\n  let [centerX, centerY] = center\n\n  return {\n    x: centerX + origin.x,\n    y: centerY - origin.y,\n  }\n}\n\n/**\n * 判断是否有冲突\n * @param {Object} souce // 待检查对象\n * @param {Object} target // 比较对象\n * @param {Object} distance // 避免冲突的间隙\n */\nexport function isCollision(souce, target, distance) {\n  let isCollision = false\n  if (souce.x > 0 && target.x > 0) {\n    isCollision = souce.y + distance > target.y\n  } else if (souce.x < 0 && target.x < 0) {\n    isCollision = souce.y - distance < target.y\n  }\n\n  return isCollision\n}\n\n/**\n * 检查并避免冲突\n * @param {Object} souce // 待检查对象\n * @param {Object} target // 比较对象\n * @param {Object} distance // 避免冲突的间隙\n */\nexport function avoidCollision(souce, target, distance) {\n  if (target) {\n    while (isCollision(souce, target, distance)) {\n      if (souce.x > 0) {\n        souce.y--\n      } else if (souce.x < 0) {\n        souce.y++\n      }\n    }\n  }\n  return souce\n}\n\n/**\n * 10% => 0.1\n * @param {String} percent\n */\nexport function percentToNum(percent) {\n  if (!!percent) {\n    percent = String(percent).replace('%', '')\n    percent = Number(percent) / 100\n  } else {\n    percent = 0\n  }\n  return percent\n}\n\n/**\n * 判断一个数据是否为对象\n * @param {Any} data\n */\nexport function isObject(data) {\n  return Object.prototype.toString.call(data) === '[object Object]'\n}\n\n/**\n * 判断一个数据是否为数组\n * @param {Any} data\n */\nexport function isArray(data) {\n  return Object.prototype.toString.call(data) === '[object Array]'\n}\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    objectTag = '[object Object]',\n    promiseTag = '[object Promise]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]',\n    weakMapTag = '[object WeakMap]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/** Used to identify `toStringTag` values supported by `_.clone`. */\nvar cloneableTags = {};\ncloneableTags[argsTag] = cloneableTags[arrayTag] =\ncloneableTags[arrayBufferTag] = cloneableTags[dataViewTag] =\ncloneableTags[boolTag] = cloneableTags[dateTag] =\ncloneableTags[float32Tag] = cloneableTags[float64Tag] =\ncloneableTags[int8Tag] = cloneableTags[int16Tag] =\ncloneableTags[int32Tag] = cloneableTags[mapTag] =\ncloneableTags[numberTag] = cloneableTags[objectTag] =\ncloneableTags[regexpTag] = cloneableTags[setTag] =\ncloneableTags[stringTag] = cloneableTags[symbolTag] =\ncloneableTags[uint8Tag] = cloneableTags[uint8ClampedTag] =\ncloneableTags[uint16Tag] = cloneableTags[uint32Tag] = true;\ncloneableTags[errorTag] = cloneableTags[funcTag] =\ncloneableTags[weakMapTag] = false;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/**\n * Adds the key-value `pair` to `map`.\n *\n * @private\n * @param {Object} map The map to modify.\n * @param {Array} pair The key-value pair to add.\n * @returns {Object} Returns `map`.\n */\nfunction addMapEntry(map, pair) {\n  // Don't return `map.set` because it's not chainable in IE 11.\n  map.set(pair[0], pair[1]);\n  return map;\n}\n\n/**\n * Adds `value` to `set`.\n *\n * @private\n * @param {Object} set The set to modify.\n * @param {*} value The value to add.\n * @returns {Object} Returns `set`.\n */\nfunction addSetEntry(set, value) {\n  // Don't return `set.add` because it's not chainable in IE 11.\n  set.add(value);\n  return set;\n}\n\n/**\n * A specialized version of `_.forEach` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns `array`.\n */\nfunction arrayEach(array, iteratee) {\n  var index = -1,\n      length = array ? array.length : 0;\n\n  while (++index < length) {\n    if (iteratee(array[index], index, array) === false) {\n      break;\n    }\n  }\n  return array;\n}\n\n/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n      length = values.length,\n      offset = array.length;\n\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\n\n/**\n * A specialized version of `_.reduce` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the first element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduce(array, iteratee, accumulator, initAccum) {\n  var index = -1,\n      length = array ? array.length : 0;\n\n  if (initAccum && length) {\n    accumulator = array[++index];\n  }\n  while (++index < length) {\n    accumulator = iteratee(accumulator, array[index], index, array);\n  }\n  return accumulator;\n}\n\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\n/**\n * Checks if `value` is a host object in IE < 9.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a host object, else `false`.\n */\nfunction isHostObject(value) {\n  // Many host objects are `Object` objects that can coerce to strings\n  // despite having improperly defined `toString` methods.\n  var result = false;\n  if (value != null && typeof value.toString != 'function') {\n    try {\n      result = !!(value + '');\n    } catch (e) {}\n  }\n  return result;\n}\n\n/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n      result = Array(map.size);\n\n  map.forEach(function(value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype,\n    funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    Symbol = root.Symbol,\n    Uint8Array = root.Uint8Array,\n    getPrototype = overArg(Object.getPrototypeOf, Object),\n    objectCreate = Object.create,\n    propertyIsEnumerable = objectProto.propertyIsEnumerable,\n    splice = arrayProto.splice;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols,\n    nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined,\n    nativeKeys = overArg(Object.keys, Object);\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView'),\n    Map = getNative(root, 'Map'),\n    Promise = getNative(root, 'Promise'),\n    Set = getNative(root, 'Set'),\n    WeakMap = getNative(root, 'WeakMap'),\n    nativeCreate = getNative(Object, 'create');\n\n/** Used to detect maps, sets, and weakmaps. */\nvar dataViewCtorString = toSource(DataView),\n    mapCtorString = toSource(Map),\n    promiseCtorString = toSource(Promise),\n    setCtorString = toSource(Set),\n    weakMapCtorString = toSource(WeakMap);\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n}\n\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  return this.has(key) && delete this.__data__[key];\n}\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n}\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  return true;\n}\n\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  return index < 0 ? undefined : data[index][1];\n}\n\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction listCacheHas(key) {\n  return assocIndexOf(this.__data__, key) > -1;\n}\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  return getMapData(this, key)['delete'](key);\n}\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  getMapData(this, key).set(key, value);\n  return this;\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  this.__data__ = new ListCache(entries);\n}\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache;\n}\n\n/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  return this.__data__['delete'](key);\n}\n\n/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\n/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction stackHas(key) {\n  return this.__data__.has(key);\n}\n\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\nfunction stackSet(key, value) {\n  var cache = this.__data__;\n  if (cache instanceof ListCache) {\n    var pairs = cache.__data__;\n    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {\n      pairs.push([key, value]);\n      return this;\n    }\n    cache = this.__data__ = new MapCache(pairs);\n  }\n  cache.set(key, value);\n  return this;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  // Safari 9 makes `arguments.length` enumerable in strict mode.\n  var result = (isArray(value) || isArguments(value))\n    ? baseTimes(value.length, String)\n    : [];\n\n  var length = result.length,\n      skipIndexes = !!length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (key == 'length' || isIndex(key, length)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    object[key] = value;\n  }\n}\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssign(object, source) {\n  return object && copyObject(source, keys(source), object);\n}\n\n/**\n * The base implementation of `_.clone` and `_.cloneDeep` which tracks\n * traversed objects.\n *\n * @private\n * @param {*} value The value to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @param {boolean} [isFull] Specify a clone including symbols.\n * @param {Function} [customizer] The function to customize cloning.\n * @param {string} [key] The key of `value`.\n * @param {Object} [object] The parent object of `value`.\n * @param {Object} [stack] Tracks traversed objects and their clone counterparts.\n * @returns {*} Returns the cloned value.\n */\nfunction baseClone(value, isDeep, isFull, customizer, key, object, stack) {\n  var result;\n  if (customizer) {\n    result = object ? customizer(value, key, object, stack) : customizer(value);\n  }\n  if (result !== undefined) {\n    return result;\n  }\n  if (!isObject(value)) {\n    return value;\n  }\n  var isArr = isArray(value);\n  if (isArr) {\n    result = initCloneArray(value);\n    if (!isDeep) {\n      return copyArray(value, result);\n    }\n  } else {\n    var tag = getTag(value),\n        isFunc = tag == funcTag || tag == genTag;\n\n    if (isBuffer(value)) {\n      return cloneBuffer(value, isDeep);\n    }\n    if (tag == objectTag || tag == argsTag || (isFunc && !object)) {\n      if (isHostObject(value)) {\n        return object ? value : {};\n      }\n      result = initCloneObject(isFunc ? {} : value);\n      if (!isDeep) {\n        return copySymbols(value, baseAssign(result, value));\n      }\n    } else {\n      if (!cloneableTags[tag]) {\n        return object ? value : {};\n      }\n      result = initCloneByTag(value, tag, baseClone, isDeep);\n    }\n  }\n  // Check for circular references and return its corresponding clone.\n  stack || (stack = new Stack);\n  var stacked = stack.get(value);\n  if (stacked) {\n    return stacked;\n  }\n  stack.set(value, result);\n\n  if (!isArr) {\n    var props = isFull ? getAllKeys(value) : keys(value);\n  }\n  arrayEach(props || value, function(subValue, key) {\n    if (props) {\n      key = subValue;\n      subValue = value[key];\n    }\n    // Recursively populate clone (susceptible to call stack limits).\n    assignValue(result, key, baseClone(subValue, isDeep, isFull, customizer, key, value, stack));\n  });\n  return result;\n}\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} prototype The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nfunction baseCreate(proto) {\n  return isObject(proto) ? objectCreate(proto) : {};\n}\n\n/**\n * The base implementation of `getAllKeys` and `getAllKeysIn` which uses\n * `keysFunc` and `symbolsFunc` to get the enumerable property names and\n * symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @param {Function} symbolsFunc The function to get the symbols of `object`.\n * @returns {Array} Returns the array of property names and symbols.\n */\nfunction baseGetAllKeys(object, keysFunc, symbolsFunc) {\n  var result = keysFunc(object);\n  return isArray(object) ? result : arrayPush(result, symbolsFunc(object));\n}\n\n/**\n * The base implementation of `getTag`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  return objectToString.call(value);\n}\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = (isFunction(value) || isHostObject(value)) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {Buffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var result = new buffer.constructor(buffer.length);\n  buffer.copy(result);\n  return result;\n}\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */\nfunction cloneDataView(dataView, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\n\n/**\n * Creates a clone of `map`.\n *\n * @private\n * @param {Object} map The map to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned map.\n */\nfunction cloneMap(map, isDeep, cloneFunc) {\n  var array = isDeep ? cloneFunc(mapToArray(map), true) : mapToArray(map);\n  return arrayReduce(array, addMapEntry, new map.constructor);\n}\n\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\n\n/**\n * Creates a clone of `set`.\n *\n * @private\n * @param {Object} set The set to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned set.\n */\nfunction cloneSet(set, isDeep, cloneFunc) {\n  var array = isDeep ? cloneFunc(setToArray(set), true) : setToArray(set);\n  return arrayReduce(array, addSetEntry, new set.constructor);\n}\n\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */\nfunction cloneSymbol(symbol) {\n  return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\n/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n      length = source.length;\n\n  array || (array = Array(length));\n  while (++index < length) {\n    array[index] = source[index];\n  }\n  return array;\n}\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    assignValue(object, key, newValue === undefined ? source[key] : newValue);\n  }\n  return object;\n}\n\n/**\n * Copies own symbol properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbols(source, object) {\n  return copyObject(source, getSymbols(source), object);\n}\n\n/**\n * Creates an array of own enumerable property names and symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names and symbols.\n */\nfunction getAllKeys(object) {\n  return baseGetAllKeys(object, keys, getSymbols);\n}\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\n/**\n * Creates an array of the own enumerable symbol properties of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbols = nativeGetSymbols ? overArg(nativeGetSymbols, Object) : stubArray;\n\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nvar getTag = baseGetTag;\n\n// Fallback for data views, maps, sets, and weak maps in IE 11,\n// for data views in Edge < 14, and promises in Node.js.\nif ((DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag) ||\n    (Map && getTag(new Map) != mapTag) ||\n    (Promise && getTag(Promise.resolve()) != promiseTag) ||\n    (Set && getTag(new Set) != setTag) ||\n    (WeakMap && getTag(new WeakMap) != weakMapTag)) {\n  getTag = function(value) {\n    var result = objectToString.call(value),\n        Ctor = result == objectTag ? value.constructor : undefined,\n        ctorString = Ctor ? toSource(Ctor) : undefined;\n\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString: return dataViewTag;\n        case mapCtorString: return mapTag;\n        case promiseCtorString: return promiseTag;\n        case setCtorString: return setTag;\n        case weakMapCtorString: return weakMapTag;\n      }\n    }\n    return result;\n  };\n}\n\n/**\n * Initializes an array clone.\n *\n * @private\n * @param {Array} array The array to clone.\n * @returns {Array} Returns the initialized clone.\n */\nfunction initCloneArray(array) {\n  var length = array.length,\n      result = array.constructor(length);\n\n  // Add properties assigned by `RegExp#exec`.\n  if (length && typeof array[0] == 'string' && hasOwnProperty.call(array, 'index')) {\n    result.index = array.index;\n    result.input = array.input;\n  }\n  return result;\n}\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\n/**\n * Initializes an object clone based on its `toStringTag`.\n *\n * **Note:** This function only supports cloning values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to clone.\n * @param {string} tag The `toStringTag` of the object to clone.\n * @param {Function} cloneFunc The function to clone values.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneByTag(object, tag, cloneFunc, isDeep) {\n  var Ctor = object.constructor;\n  switch (tag) {\n    case arrayBufferTag:\n      return cloneArrayBuffer(object);\n\n    case boolTag:\n    case dateTag:\n      return new Ctor(+object);\n\n    case dataViewTag:\n      return cloneDataView(object, isDeep);\n\n    case float32Tag: case float64Tag:\n    case int8Tag: case int16Tag: case int32Tag:\n    case uint8Tag: case uint8ClampedTag: case uint16Tag: case uint32Tag:\n      return cloneTypedArray(object, isDeep);\n\n    case mapTag:\n      return cloneMap(object, isDeep, cloneFunc);\n\n    case numberTag:\n    case stringTag:\n      return new Ctor(object);\n\n    case regexpTag:\n      return cloneRegExp(object);\n\n    case setTag:\n      return cloneSet(object, isDeep, cloneFunc);\n\n    case symbolTag:\n      return cloneSymbol(object);\n  }\n}\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  length = length == null ? MAX_SAFE_INTEGER : length;\n  return !!length &&\n    (typeof value == 'number' || reIsUint.test(value)) &&\n    (value > -1 && value % 1 == 0 && value < length);\n}\n\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to process.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */\nfunction cloneDeep(value) {\n  return baseClone(value, true, true);\n}\n\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nfunction isArguments(value) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  return isArrayLikeObject(value) && hasOwnProperty.call(value, 'callee') &&\n    (!propertyIsEnumerable.call(value, 'callee') || objectToString.call(value) == argsTag);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\nvar isBuffer = nativeIsBuffer || stubFalse;\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */\nfunction keys(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n\n/**\n * This method returns a new empty array.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {Array} Returns the new empty array.\n * @example\n *\n * var arrays = _.times(2, _.stubArray);\n *\n * console.log(arrays);\n * // => [[], []]\n *\n * console.log(arrays[0] === arrays[1]);\n * // => false\n */\nfunction stubArray() {\n  return [];\n}\n\n/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */\nfunction stubFalse() {\n  return false;\n}\n\nmodule.exports = cloneDeep;\n", "import { isObject, isArray } from '../util/util'\nimport cloneDeep from 'lodash.clonedeep'\n\n/**\n * 将来源对象数据补充到目标对象中\n * @param {Object} sources // 来源对象\n * @param {Object} sourcesKey // 来源对象属性\n * @param {Object} target  // 目标对象\n * @param {Object} targetKey  // 目标对象属性\n * @param {Boolean} isCover  // 是否强制来源对象数据覆盖目标对象数据\n */\nexport function replenishData(sources, sourcesKey, target, targetKey, isCover = false) {\n  if (!target[targetKey] && target[targetKey] !== 0 && target[targetKey] !== '' && typeof target[targetKey] !== 'boolean') {\n    // (目标对象为空时赋值)\n    target[targetKey] = cloneDeep(sources[sourcesKey])\n  } else {\n    if (isCover) {\n      if (isObject(sources[sourcesKey])) {\n        Object.keys(sources[sourcesKey]).forEach(_key => {\n          replenishData(sources[sourcesKey], _key, target[targetKey], _key, isCover)\n        })\n      } else if (isArray(sources[sourcesKey])) {\n        if (sourcesKey == 'series') {\n          sources[sourcesKey].forEach((sourcesItem, sourcesIndex) => {\n            Object.keys(sourcesItem).forEach(_key => {\n              replenishData(sources[_key][sourcesIndex], _key, target[_key][sourcesIndex], _key, true)\n            })\n          })\n        } else {\n          target[targetKey] = [].concat(sources[sourcesKey])\n        }\n      } else {\n        target[targetKey] = sources[sourcesKey]\n      }\n    } else {\n      // (目标对象不为为空时，若数据为对象时递归)\n      if (isObject(target[targetKey]) && isObject(sources[sourcesKey])) {\n        Object.keys(sources[sourcesKey]).forEach(_key => {\n          replenishData(sources[sourcesKey], _key, target[targetKey], _key, isCover)\n        })\n      }\n    }\n  }\n}\n\n/**\n * 将opts的数据补充完整\n */\nexport function calOptions() {\n  let { config, opts } = this\n  replenishData(config, 'animation', opts, 'animation')\n  replenishData(config, 'animationDuration', opts, 'animationDuration')\n  replenishData(config, 'animationTiming', opts, 'animationTiming')\n  replenishData(config, 'backgroundColor', opts, 'backgroundColor')\n  replenishData(config, 'colors', opts, 'colors')\n  replenishData(config, 'padding', opts, 'padding')\n  replenishData(config, 'legend', opts, 'legend')\n  replenishData(config, 'tooltip', opts, 'tooltip')\n\n  opts.series.forEach(seriesItem => {\n    switch (seriesItem.type) {\n      case 'bar':\n      case 'line':\n      case 'scatter':\n      case 'candlestick':\n      case 'k':\n      case 'heatmap':\n        if (opts.yAxis && opts.yAxis.type == 'category') {\n          replenishData(config, 'yAxisCategory', opts, 'yAxis')\n        } else {\n          replenishData(config, 'yAxisValue', opts, 'yAxis')\n        }\n\n        if (opts.xAxis && opts.xAxis.type == 'value') {\n          replenishData(config, 'xAxisValue', opts, 'xAxis')\n        } else {\n          replenishData(config, 'xAxisCategory', opts, 'xAxis')\n        }\n      case 'radar':\n        replenishData(config, 'radarAxis', opts, 'radarAxis')\n        break\n    }\n  })\n\n  calSeries.call(this)\n\n  console.log('complete calOptions', this)\n}\n\n/**\n * 将series的数据补充完整\n */\nexport function calSeries() {\n  let { config, opts } = this\n\n  opts.series.forEach((seriesItem, seriesIndex) => {\n    replenishData(config, 'label', seriesItem, 'label')\n    switch (seriesItem.type) {\n      case 'bar':\n      case 'line':\n      case 'pie':\n      case 'radar':\n      case 'scatter':\n      case 'funnel':\n      case 'heatmap':\n      case 'treemap':\n      case 'tagCloud':\n        replenishData(config, seriesItem.type, opts.series, seriesIndex)\n        break\n      case 'candlestick':\n      case 'k':\n        replenishData(config, 'candlestick', opts.series, seriesIndex)\n        break\n    }\n  })\n}\n", "/**\n * 获取存在xy轴图表的点击位置下标\n * @param {Object} offset\n */\nexport function getAxisChartCurrentIndex(offset) {\n  const { x, y } = offset\n  const { opts, chartData } = this\n  const { yAxis, xAxis } = opts\n  const { xStart, xEnd, yStart, yEnd, xAxisLabelPoint, yAxisLabelPoint } = chartData.axisData\n  const isInExactChartArea = x < xEnd && x > xStart && y > yEnd && y < yStart\n  let currentIndex = -1\n\n  if (isInExactChartArea) {\n    // 点击有效范围\n    if (xAxis.type == 'category' && yAxis.type == 'value') {\n      xAxisLabelPoint.forEach((item, index) => {\n        if (x > item.x) {\n          currentIndex = index\n        }\n      })\n\n      if (x < xAxisLabelPoint[0].x) {\n        currentIndex = 0\n      }\n    } else if (xAxis.type == 'value' && yAxis.type == 'category') {\n      yAxisLabelPoint.forEach((item, index) => {\n        if (y < item.y) {\n          currentIndex = index\n        }\n      })\n\n      if (y > yAxisLabelPoint[0].y) {\n        currentIndex = 0\n      }\n    } else if (xAxis.type == 'value' && yAxis.type == 'value') {\n    }\n  }\n\n  console.log('complete getAxisChartCurrentIndex')\n\n  return currentIndex\n}\n\n/**\n * 获取饼图的点击位置下标\n * @param {Object} offset\n */\nexport function getPieChartCurrentIndex(offset) {\n  const { x, y } = offset\n  const { data, center, radius, offsetAngle } = this.chartData.chartPie\n  const [centerX, centerY] = center\n  const [raidusMin, raidusMax] = radius\n  const length = Math.hypot(Math.abs(x - centerX), Math.abs(y - centerY)) // 点击点距离圆心的长度\n  const isInExactChartArea = length >= raidusMin && length <= raidusMax\n  let currentIndex = -1\n\n  if (isInExactChartArea) {\n    // 点击有效范围\n    let currentRadian // 点击点的弧度\n    if (Math.atan2(x - centerX, centerY - y) > 0) {\n      currentRadian = Math.atan2(x - centerX, centerY - y)\n    } else {\n      currentRadian = Math.PI + (Math.PI - Math.abs(Math.atan2(x - centerX, centerY - y)))\n    }\n    if (currentRadian > ((90 + offsetAngle) * Math.PI) / 180) {\n      currentRadian -= ((90 + offsetAngle) * Math.PI) / 180\n    } else {\n      currentRadian = 2 * Math.PI + (currentRadian - ((90 + offsetAngle) * Math.PI) / 180)\n    }\n\n    data.forEach((item, index) => {\n      if (currentRadian > item._start_) {\n        currentIndex = index\n      }\n    })\n  }\n\n  console.log('complete getPieChartCurrentIndex')\n\n  return currentIndex\n}\n\n/**\n * 获取雷达图的点击位置下标\n * @param {Object} offset\n */\nexport function getRadarChartCurrentIndex(offset) {\n  const { x, y } = offset\n  const { chartRadar, radarAxis } = this.chartData\n  const { center, radius } = radarAxis\n  const [centerX, centerY] = center\n  const length = Math.hypot(Math.abs(x - centerX), Math.abs(y - centerY)) // 点击点距离圆心的长度\n  const isInExactChartArea = length <= radius\n  let currentIndex = -1\n\n  if (isInExactChartArea) {\n    // 点击有效范围\n    let currentRadian // 点击点的弧度\n    if (Math.atan2(x - centerX, centerY - y) > 0) {\n      currentRadian = Math.atan2(x - centerX, centerY - y)\n    } else {\n      currentRadian = Math.PI + (Math.PI - Math.abs(Math.atan2(x - centerX, centerY - y)))\n    }\n\n    chartRadar[0].dataPosition.forEach((item, index, arr) => {\n      const { spacingRadian, _start_ } = item\n      if (index == 0) {\n        if (currentRadian >= 2 * Math.PI - spacingRadian / 2 || currentRadian < _start_ + spacingRadian / 2) {\n          currentIndex = 0\n        }\n      } else {\n        if (currentRadian > _start_ - spacingRadian / 2 && currentRadian <= _start_ + spacingRadian / 2) {\n          currentIndex = arr.length - index\n        }\n      }\n    })\n  }\n\n  console.log('complete getRadarChartCurrentIndex')\n\n  return currentIndex\n}\n", "/**\n * 计算 tooltip 容器数据\n */\nexport function calTooltipContainerData() {\n  const { opts, tooltipData } = this\n  const { width, height, tooltip } = opts\n  const { padding, itemGap, iconRadius, iconGap, textStyle } = tooltip\n  const { offset, data, maxTextWidth, tooltipTitle } = tooltipData\n  const { x: offsetX, y: offsetY } = offset\n\n  let tooltipWidth = padding * 2 + iconRadius * 2 + iconGap + maxTextWidth\n  let tooltipHeight = padding * 2\n  let tooltipContainerOffset = 10 // 偏移量\n  let tooltipX, tooltipY\n\n  if (tooltipTitle) {\n    tooltipHeight += textStyle.lineHeight + itemGap\n  }\n\n  data.forEach((item, index, arr) => {\n    if (item.type == 'candlestick' || item.type == 'k') {\n      const contentLength = item.volumn ? 6 : 5\n      tooltipHeight += textStyle.lineHeight * contentLength + itemGap * contentLength - 1\n    } else {\n      if (index + 1 == arr.length) {\n        tooltipHeight += textStyle.lineHeight\n      } else {\n        tooltipHeight += textStyle.lineHeight + itemGap\n      }\n    }\n  })\n\n  if (offsetX + tooltipContainerOffset + tooltipWidth <= width) {\n    // 优先显示右边\n    tooltipX = offsetX + tooltipContainerOffset\n  } else if (offsetX - tooltipContainerOffset - tooltipWidth >= 0) {\n    // 左边\n    tooltipX = offsetX - tooltipContainerOffset - tooltipWidth\n  } else {\n    // 左右都放不下\n    if (offsetX <= width / 2) {\n      // 边界靠右边\n      tooltipX = width - tooltipWidth\n    } else {\n      // 边界靠左边\n      tooltipX = 0\n    }\n  }\n\n  if (offsetY + tooltipContainerOffset + tooltipHeight <= height) {\n    // 优先显示下边\n    tooltipY = offsetY + tooltipContainerOffset\n  } else if (offsetY - tooltipContainerOffset - tooltipHeight >= 0) {\n    // 上边\n    tooltipY = offsetY - tooltipContainerOffset - tooltipHeight\n  } else {\n    // 上下都放不下\n    if (offsetY <= height / 2) {\n      // 边界靠下边\n      tooltipY = height - tooltipHeight\n    } else {\n      // 边界靠上边\n      tooltipY = 0\n    }\n  }\n\n  this.tooltipData = {\n    ...this.tooltipData,\n    tooltipX,\n    tooltipY,\n    tooltipWidth,\n    tooltipHeight,\n    tooltipTitle,\n  }\n  console.log('complete calTooltipContainerData')\n}\n\n/**\n * 计算轴线指示器数据\n * @param {Number} currentIndex\n * @param {Object} offset\n */\n\nexport function calAxisPointerData(currentIndex) {\n  if (currentIndex == -1) return\n\n  const { context, opts, chartData, tooltipData } = this\n  const { xAxis, yAxis, tooltip } = opts\n  const { x: offsetX, y: offsetY } = tooltipData.offset\n  const { type, cross } = tooltip.axisPointer\n  const { fontPadding: crossFontPadding } = cross\n  const {\n    xStart,\n    xEnd,\n    xEachSpacing,\n    xSpacing,\n    xDataRange,\n    xMinData,\n    yStart,\n    yEnd,\n    yEachSpacing,\n    ySpacing,\n    yDataRange,\n    yMinData,\n    xAxisLabelPoint,\n    yAxisLabelPoint,\n  } = chartData.axisData\n  const isExistCandlestick = tooltipData.data.some(item => item.type == 'candlestick' || item.type == 'k')\n\n  let curerntAxisLabel, currentAxisLabelX, currentAxisLabelY\n\n  if (xAxis.type == 'category' && yAxis.type == 'value') {\n    curerntAxisLabel = xAxisLabelPoint[currentIndex].text\n    currentAxisLabelX = xAxisLabelPoint[currentIndex].x\n    currentAxisLabelY = xAxisLabelPoint[currentIndex].y\n  } else if (xAxis.type == 'value' && yAxis.type == 'category') {\n    curerntAxisLabel = yAxisLabelPoint[currentIndex].text\n    currentAxisLabelX = yAxisLabelPoint[currentIndex].x\n    currentAxisLabelY = yAxisLabelPoint[currentIndex].y\n  } else if (xAxis.type == 'value' && yAxis.type == 'value') {\n  }\n\n  let crossPointer, xAxisPointer, yAxisPointer\n\n  if (xAxis.type == 'category' && yAxis.type == 'value') {\n    const yAxisLabel = (yMinData + ((yStart - offsetY) * yDataRange) / ySpacing).toFixed(2)\n    const yAxisLabelX = yAxisLabelPoint[0].x\n    const yAxisLabelFontSize = yAxis.axisLabel.textStyle.fontSize\n    const xAxisLabelFontSize = xAxis.axisLabel.textStyle.fontSize\n\n    context.font = `${yAxisLabelFontSize}px`\n    const yAxisLabelWidth = context.measureText(yAxisLabel).width + 2 * crossFontPadding\n    const yAxisLabelHeight = yAxisLabelFontSize + 2 * crossFontPadding\n\n    context.font = `${xAxisLabelFontSize}px`\n    const xAxisLabelWidth = context.measureText(curerntAxisLabel).width + 2 * crossFontPadding\n    const xAxisLabelHeight = xAxisLabelFontSize + 2 * crossFontPadding\n\n    this.tooltipData.offset = { x: currentAxisLabelX, y: offsetY } // 修改 offset 为 cross 交点位置\n\n    context.font = `${tooltip.textStyle.fontSize}px`\n    this.tooltipData.maxTextWidth = Math.max(this.tooltipData.maxTextWidth, context.measureText(curerntAxisLabel).width)\n    this.tooltipData.tooltipTitle = curerntAxisLabel\n\n    crossPointer = {\n      yAxisLabel,\n      yAxisLabelWidth,\n      yAxisLabelHeight,\n      yAxisLabelX,\n      yAxisLabelY: offsetY,\n      yAxisLineX0: xStart,\n      yAxisLineY0: offsetY,\n      yAxisLineX1: xEnd,\n      yAxisLineY1: offsetY,\n      xAxisLabel: curerntAxisLabel,\n      xAxisLabelWidth,\n      xAxisLabelHeight,\n      xAxisLabelX: currentAxisLabelX,\n      xAxisLabelY: currentAxisLabelY,\n      xAxisLineX0: currentAxisLabelX,\n      xAxisLineY0: yStart,\n      xAxisLineX1: currentAxisLabelX,\n      xAxisLineY1: yEnd,\n    }\n\n    if (type == 'line') {\n      xAxisPointer = {\n        x0: currentAxisLabelX,\n        y0: yStart,\n        x1: currentAxisLabelX,\n        y1: yEnd,\n      }\n    } else if (type == 'shadow') {\n      xAxisPointer = {\n        x: xStart + xEachSpacing * currentIndex,\n        y: yEnd,\n        width: xEachSpacing,\n        height: ySpacing,\n      }\n    }\n  } else if (xAxis.type == 'value' && yAxis.type == 'category') {\n    const xAxisLabel = (xMinData + ((offsetX - xStart) * xDataRange) / xSpacing).toFixed(2)\n    const xAxisLabelY = xAxisLabelPoint[0].y\n    const yAxisLabelFontSize = yAxis.axisLabel.textStyle.fontSize\n    const xAxisLabelFontSize = xAxis.axisLabel.textStyle.fontSize\n\n    context.font = `${yAxisLabelFontSize}px`\n    const yAxisLabelWidth = context.measureText(curerntAxisLabel).width + 2 * crossFontPadding\n    const yAxisLabelHeight = yAxisLabelFontSize + 2 * crossFontPadding\n\n    context.font = `${xAxisLabelFontSize}px`\n    const xAxisLabelWidth = context.measureText(xAxisLabel).width + 2 * crossFontPadding\n    const xAxisLabelHeight = xAxisLabelFontSize + 2 * crossFontPadding\n\n    this.tooltipData.offset = { x: offsetX, y: currentAxisLabelY } // 修改 offset 为 cross 交点位置\n\n    context.font = `${tooltip.textStyle.fontSize}px`\n    this.tooltipData.maxTextWidth = Math.max(this.tooltipData.maxTextWidth, context.measureText(curerntAxisLabel).width)\n    this.tooltipData.tooltipTitle = curerntAxisLabel\n\n    crossPointer = {\n      yAxisLabel: curerntAxisLabel,\n      yAxisLabelWidth,\n      yAxisLabelHeight,\n      yAxisLabelX: currentAxisLabelX,\n      yAxisLabelY: currentAxisLabelY,\n      yAxisLineX0: xStart,\n      yAxisLineY0: currentAxisLabelY,\n      yAxisLineX1: xEnd,\n      yAxisLineY1: currentAxisLabelY,\n      xAxisLabel,\n      xAxisLabelWidth,\n      xAxisLabelHeight,\n      xAxisLabelX: offsetX,\n      xAxisLabelY,\n      xAxisLineX0: offsetX,\n      xAxisLineY0: yStart,\n      xAxisLineX1: offsetX,\n      xAxisLineY1: yEnd,\n    }\n\n    if (type == 'line') {\n      yAxisPointer = {\n        x0: xStart,\n        y0: currentAxisLabelY,\n        x1: xEnd,\n        y1: currentAxisLabelY,\n      }\n    } else if (type == 'shadow') {\n      yAxisPointer = {\n        x: xStart,\n        y: yEnd - yEachSpacing * currentIndex,\n        width: xSpacing,\n        height: yEachSpacing,\n      }\n    }\n  } else if (xAxis.type == 'value' && yAxis.type == 'value') {\n  }\n\n  if (isExistCandlestick) {\n    if (chartData.chartCandlestick.bar) {\n      crossPointer.xAxisLineY0 = chartData.chartCandlestick.bar.lineStartY\n    }\n  }\n\n  tooltipData.axisPointerData = {\n    crossPointer,\n    xAxisPointer,\n    yAxisPointer,\n  }\n\n  console.log('complete calAxisPointerData')\n}\n\n/**\n * 计算线图 tooltipData\n * @param {Number} currentIndex\n */\nexport function calLineChartTooltipData(currentIndex) {\n  if (currentIndex == -1) return\n\n  const { context, opts, chartData, tooltipData } = this\n  let maxTextWidth = tooltipData.maxTextWidth\n\n  context.font = `${opts.tooltip.textStyle.fontSize}px`\n  chartData.chartLine.forEach(item => {\n    const { type, name, data, itemStyle, label, symbol } = item\n    const { type: symbolType, size: symbolSize, color: symbolColor } = symbol\n    const { x, y, data: value } = data[currentIndex]\n\n    if (typeof value !== 'number') return\n\n    const text = `${name}: ${label.format ? label.format(value) : value}`\n    const textWidth = context.measureText(text).width\n    maxTextWidth = Math.max(maxTextWidth, textWidth)\n\n    tooltipData.data.push({\n      type,\n      name,\n      text,\n      color: itemStyle.color,\n      x,\n      y,\n      symbolType,\n      symbolSize,\n      symbolColor,\n    })\n  })\n\n  this.tooltipData.maxTextWidth = maxTextWidth\n\n  console.log('complete calLineChartTooltipData')\n}\n\n/**\n * 计算柱状图 tooltipData\n * @param {Number} currentIndex\n */\nexport function calBarChartTooltipData(currentIndex) {\n  if (currentIndex == -1) return\n\n  const { context, opts, chartData, tooltipData } = this\n  let maxTextWidth = tooltipData.maxTextWidth\n\n  context.font = `${opts.tooltip.textStyle.fontSize}px`\n  chartData.chartBar[currentIndex].forEach(barItem => {\n    barItem.forEach(seriesItem => {\n      const { type, name, data, itemStyle, label } = seriesItem\n      const text = `${name}: ${label.format ? label.format(data) : data}`\n      const textWidth = context.measureText(text).width\n      maxTextWidth = Math.max(maxTextWidth, textWidth)\n\n      tooltipData.data.push({\n        type,\n        name,\n        text,\n        color: itemStyle.color,\n      })\n    })\n  })\n\n  this.tooltipData.maxTextWidth = maxTextWidth\n\n  console.log('complete calBarChartTooltipData')\n}\n\n/**\n * 计算饼图 tooltipData\n * @param {Number} currentIndex\n */\nexport function calPieChartTooltipData(currentIndex) {\n  if (currentIndex == -1) return\n\n  const { context, opts, chartData, tooltipData } = this\n  const { name, type, data, center, radius, label } = chartData.chartPie\n  const { name: itemName, value, itemStyle, _proportion_, _start_, _end_ } = data[currentIndex]\n  const text = `${itemName}: ${label.format ? label.format(value) : value}`\n\n  context.font = `${opts.tooltip.textStyle.fontSize}px`\n  const textWidth = context.measureText(text).width\n  const maxTextWidth = Math.max(tooltipData.maxTextWidth, textWidth)\n  this.tooltipData.maxTextWidth = maxTextWidth\n  this.tooltipData.tooltipTitle = name\n\n  tooltipData.data.push({\n    type,\n    name: itemName,\n    text,\n    color: itemStyle.color,\n    center,\n    radius,\n    _proportion_,\n    _start_,\n    _end_,\n  })\n\n  console.log('complete calPieChartTooltipData')\n}\n\n/**\n * 计算雷达图 tooltipData\n * @param {Number} currentIndex\n */\nexport function calRadarChartTooltipData(currentIndex) {\n  if (currentIndex == -1) return\n\n  const { context, opts, chartData, tooltipData } = this\n  let maxTextWidth = tooltipData.maxTextWidth\n\n  context.font = `${opts.tooltip.textStyle.fontSize}px`\n  chartData.chartRadar.forEach(item => {\n    const { type, name, data, dataPosition, itemStyle, label, symbol } = item\n    const { type: symbolType, size: symbolSize, color: symbolColor } = symbol\n    const value = data[currentIndex]\n    const { x, y } = dataPosition[currentIndex].position\n\n    if (typeof value !== 'number') return\n\n    const text = `${name}: ${label.format ? label.format(value) : value}`\n    const textWidth = context.measureText(text).width\n    maxTextWidth = Math.max(maxTextWidth, textWidth)\n\n    tooltipData.data.push({\n      type,\n      name,\n      text,\n      color: itemStyle.color,\n      x,\n      y,\n      symbolType,\n      symbolSize,\n      symbolColor,\n    })\n  })\n\n  this.tooltipData.maxTextWidth = maxTextWidth\n  this.tooltipData.tooltipTitle = chartData.radarAxis.namePosition[currentIndex].text\n}\n\n/**\n * 计算蜡烛图 tooltipData\n * @param {Number} currentIndex\n */\nexport function calCandlestickChartTooltipData(currentIndex) {\n  if (currentIndex == -1) return\n\n  const { context, opts, chartData, tooltipData } = this\n  const { type, name, rect } = chartData.chartCandlestick\n  const { start, end, low, high, volumn, color } = rect[currentIndex]\n  const _start = `开盘价：${start}`,\n    _end = `收盘价：${end}`,\n    _low = `最低价：${low}`,\n    _high = `最高价：${high}`,\n    _volumn = `成交量：${volumn}`\n  let textWidth,\n    maxTextWidth = tooltipData.maxTextWidth\n\n  context.font = `${opts.tooltip.textStyle.fontSize}px`\n\n  textWidth = context.measureText(_start).width\n  maxTextWidth = Math.max(maxTextWidth, textWidth)\n\n  textWidth = context.measureText(_end).width\n  maxTextWidth = Math.max(maxTextWidth, textWidth)\n\n  textWidth = context.measureText(_low).width\n  maxTextWidth = Math.max(maxTextWidth, textWidth)\n\n  textWidth = context.measureText(_high).width\n  maxTextWidth = Math.max(maxTextWidth, textWidth)\n\n  if (volumn) {\n    textWidth = context.measureText(_volumn).width\n    maxTextWidth = Math.max(maxTextWidth, textWidth)\n  }\n\n  tooltipData.data.push({\n    type,\n    name,\n    start: _start,\n    end: _end,\n    low: _low,\n    high: _high,\n    volumn: _volumn,\n    color,\n  })\n\n  this.tooltipData.maxTextWidth = maxTextWidth\n\n  console.log('complete calCandlestickChartTooltipData')\n}\n", "export default function calSeriesMap() {\n  let seriesMap = {}\n  this.opts.series.forEach(seriesItem => {\n    if (seriesItem.type == 'candlestick' || seriesItem.type == 'k') {\n      if (!seriesMap['candlestick']) {\n        seriesMap['candlestick'] = []\n      }\n      seriesMap['candlestick'].push(seriesItem)\n    } else if (seriesItem.type == 'funnel') {\n      if (!seriesMap['funnel']) {\n        seriesMap['funnel'] = []\n      }\n      seriesItem.data.sort((a, b) => {\n        if (seriesItem.sort == 'ascending') {\n          return a.value - b.value\n        } else {\n          return b.value - a.value\n        }\n      })\n      seriesMap['funnel'].push(seriesItem)\n    } else {\n      if (!seriesMap[seriesItem.type]) {\n        seriesMap[seriesItem.type] = []\n      }\n      seriesMap[seriesItem.type].push(seriesItem)\n    }\n  })\n  this.seriesMap = seriesMap\n\n  console.log('complete calSeriesMap', this.seriesMap)\n}\n", "export default function calSeriesColor() {\n  let { colors, series } = this.opts\n\n  series.forEach((seriesItem, seriesIndex) => {\n    if (seriesItem.type == 'pie' || seriesItem.type == 'funnel' || seriesItem.type == 'treemap' || seriesItem.type == 'tagCloud') {\n      seriesItem.data.forEach((dataItem, dataIndex) => {\n        dataItem.itemStyle = dataItem.itemStyle || {}\n        if (!dataItem.itemStyle.color) {\n          dataItem.itemStyle.color = colors[dataIndex % colors.length]\n        }\n      })\n    } else {\n      seriesItem.itemStyle = seriesItem.itemStyle || {}\n      if (!seriesItem.itemStyle.color) {\n        seriesItem.itemStyle.color = colors[seriesIndex % colors.length]\n      }\n    }\n  })\n\n  console.log('complete calSeriesColor', series)\n}\n", "export default function calLegendData() {\n  if (this.opts.legend.show) {\n    let { context, opts } = this\n    let { width, padding, legend, series } = opts\n    let { type: legendType, data: legendData, shapeWidth, shapeHeight, shapeRadius, itemGap, marginTop, textStyle } = legend\n    let { fontSize, padding: textPadding } = textStyle\n    let legendWidth = 0\n    let legendWidthNum = 0\n    let legendList = []\n    let currentRow = []\n    let _shapeWidth = shapeWidth\n    let _legendData = []\n    let containerWidth = width - padding[1] - padding[3]\n\n    let dataSeriesItem = []\n    let isDataName = series.some(seriesItem => {\n      dataSeriesItem = seriesItem\n      return seriesItem.type == 'pie' || seriesItem.type == 'funnel' || seriesItem.type == 'treemap' || seriesItem.type == 'tagCloud'\n    })\n\n    if (!legendData) {\n      if (isDataName) {\n        _legendData = dataSeriesItem.data.map(dataItem => {\n          return dataItem.name\n        })\n      } else {\n        _legendData = series.map(seriesItem => {\n          return seriesItem.name\n        })\n      }\n    } else {\n      _legendData = legendData\n    }\n\n    context.font = `${fontSize}px`\n    if (isDataName) {\n      _legendData.forEach(name => {\n        dataSeriesItem.data.forEach(dataItem => {\n          if (name == dataItem.name) {\n            let _legendType\n            if (legendType == 'default') {\n              switch (dataSeriesItem.type) {\n                case 'pie':\n                  _legendType = 'circle'\n                  _shapeWidth = shapeRadius * 2\n                  break\n                case 'funnel':\n                case 'treemap':\n                case 'tagCloud':\n                  _legendType = 'rect'\n                  break\n              }\n            }\n\n            let { name, itemStyle } = dataItem\n            let measureText = this.context.measureText(name || '').width\n            let itemWidth = _shapeWidth + textPadding + itemGap + measureText\n\n            let obj = {\n              legendType: _legendType,\n              name,\n              measureText,\n              color: itemStyle.color,\n            }\n\n            if (legendWidthNum + itemWidth > containerWidth) {\n              legendList.push(currentRow)\n              legendWidthNum = itemWidth\n              currentRow = [obj]\n            } else {\n              legendWidthNum += itemWidth\n              legendWidth = Math.max(legendWidth, legendWidthNum)\n              currentRow.push(obj)\n            }\n          }\n        })\n      })\n    } else {\n      _legendData.forEach(name => {\n        series.forEach(seriesItem => {\n          let _legendType\n          if (legendType == 'default') {\n            switch (seriesItem.type) {\n              case 'bar':\n              case 'radar':\n              case 'candlestick':\n              case 'k':\n              case 'heatmap':\n                _legendType = 'rect'\n                break\n              case 'line':\n                _legendType = 'line'\n                break\n              case 'scatter':\n                _legendType = 'circle'\n                _shapeWidth = shapeRadius * 2\n                break\n            }\n          }\n\n          if (name == seriesItem.name) {\n            let { name, itemStyle } = seriesItem\n            let measureText = this.context.measureText(name || '').width\n            let itemWidth = _shapeWidth + textPadding + itemGap + measureText\n            let obj = {\n              legendType: _legendType,\n              name,\n              measureText,\n              color: itemStyle.color,\n            }\n\n            if (legendWidthNum + itemWidth > containerWidth) {\n              legendList.push(currentRow)\n              legendWidthNum = itemWidth\n              currentRow = [obj]\n            } else {\n              legendWidthNum += itemWidth\n              legendWidth = Math.max(legendWidth, legendWidthNum)\n              currentRow.push(obj)\n            }\n          }\n        })\n      })\n    }\n\n    if (currentRow.length) {\n      legendList.push(currentRow)\n    }\n\n    this.legendData = {\n      legendList,\n      legendWidth: legendWidth - itemGap,\n      legendHeight: legendList.length * Math.max(shapeHeight, fontSize) + (legendList.length - 1) * itemGap + marginTop,\n    }\n  } else {\n    this.legendData = {\n      legendList: [],\n      legendWidth: 0,\n      legendHeight: 0,\n    }\n  }\n\n  console.log('complete calLegendData', this.legendData)\n}\n", "import cloneDeep from 'lodash.clonedeep'\n\nexport default function calAxisData() {\n  let seriesMap = cloneDeep(this.seriesMap)\n  let { context, opts, legendData, chartData } = this\n  let { width, height, padding, xAxis, yAxis } = opts\n  let {\n    show: xAxisShow,\n    type: xAxisType,\n    data: xAxisData,\n    boundaryGap: xAxisBoundaryGap,\n    max: xAxisMax,\n    min: xAxisMin,\n    splitNumber: xAxisSplitNumber,\n    axisName: xAxisName,\n    axisLabel: xAxisLabel,\n    axisTick: xAxisTick,\n    axisLine: xAxisLine,\n    axisSplitLine: xAxisSplitLine,\n  } = xAxis\n\n  let {\n    show: yAxisShow,\n    type: yAxisType,\n    data: yAxisData,\n    boundaryGap: yAxisBoundaryGap,\n    max: yAxisMax,\n    min: yAxisMin,\n    splitNumber: yAxisSplitNumber,\n    axisName: yAxisName,\n    axisLabel: yAxisLabel,\n    axisTick: yAxisTick,\n    axisLine: yAxisLine,\n    axisSplitLine: yAxisSplitLine,\n  } = yAxis\n\n  let { show: xAxisNameShow, textStyle: xAxisNameTextStyle, gap: xAxisNameGap, text: xAxisNameText } = xAxisName\n  let {\n    show: xAxisLabelShow,\n    textStyle: xAxisLabelTextStyle,\n    gap: xAxisLabelGap,\n    rotate: xAxisLabelRotate,\n    showIndex: xAxisLabelShowIndex,\n    format: xAxisLabelFormat,\n  } = xAxisLabel\n  let { show: xAxisTickShow, lineStyle: xAxisTickStyle, length: xAxisTickLength, alignWithLabel: xAxisTickAlign, showIndex: xAxisTickShowIndex } = xAxisTick\n  let { show: xAxisLineShow, lineStyle: xAxisLineStyle } = xAxisLine\n  let { show: xAxisSplitLineShow, lineStyle: xAxisSplitLineStyle, alignWithLabel: xAxisSplitLineAlign, showIndex: xAxisSplitLineShowIndex } = xAxisSplitLine\n\n  let { show: yAxisNameShow, textStyle: yAxisNameTextStyle, gap: yAxisNameGap, text: yAxisNameText } = yAxisName\n  let { show: yAxisLabelShow, textStyle: yAxisLabelTextStyle, gap: yAxisLabelGap, showIndex: yAxisLabelShowIndex, format: yAxisLabelFormat } = yAxisLabel\n  let { show: yAxisTickShow, lineStyle: yAxisTickStyle, length: yAxisTickLength, alignWithLabel: yAxisTickAlign, showIndex: yAxisTickShowIndex } = yAxisTick\n  let { show: yAxisLineShow, lineStyle: yAxisLineStyle } = yAxisLine\n  let { show: yAxisSplitLineShow, lineStyle: yAxisSplitLineStyle, alignWithLabel: yAxisSplitLineAlign, showIndex: yAxisSplitLineShowIndex } = yAxisSplitLine\n\n  let { fontSize: xAxisNameFontSize } = xAxisNameTextStyle\n  let { fontSize: xAxisLabelFontSize } = xAxisLabelTextStyle\n  let { lineWidth: xAxisTickLineWidth } = xAxisTickStyle\n  let { lineWidth: xAxisLineWidth } = xAxisLineStyle\n  let { lineWidth: xAxisSplitLineWidth } = xAxisSplitLineStyle\n\n  let { fontSize: yAxisNameFontSize } = yAxisNameTextStyle\n  let { fontSize: yAxisLabelFontSize } = yAxisLabelTextStyle\n  let { lineWidth: yAxisTickLineWidth } = yAxisTickStyle\n  let { lineWidth: yAxisLineWidth } = yAxisLineStyle\n  let { lineWidth: yAxisSplitLineWidth } = yAxisSplitLineStyle\n\n  // 以x,y轴交叉点为原点\n  let xStart = padding[3] // x方向起点\n  let xEnd = width - padding[1] // x方向终点\n  let yStart = height - padding[2] - legendData.legendHeight // y方向起点\n  let yEnd = padding[0] // y方向终点\n\n  if (seriesMap.candlestick && seriesMap.candlestick.length) {\n    let { show, height, margin, lineStyle } = seriesMap['candlestick'][0].bar\n    let { lineWidth } = lineStyle\n\n    if (show) {\n      yStart -= height + margin + lineWidth // 蜡烛图存在成交量柱状图配置则修正yStart\n    }\n  }\n\n  let xStartInit = xStart\n  let yStartInit = yStart\n\n  let yIsSamePart = true, // y轴是否同时为正数或负数，为false时同时存在正负数\n    xIsSamePart = true, // x轴是否同时为正数或负数，为false时同时存在正负数\n    yZero, // y轴零线的y坐标\n    yPlusSpacing,\n    yMinusSpacing,\n    ySpacing,\n    yEachSpacing,\n    xZero, // x轴零线的x坐标\n    xPlusSpacing,\n    xMinusSpacing,\n    xSpacing,\n    xEachSpacing,\n    yMaxData,\n    yMinData,\n    yDataRange,\n    xMaxData,\n    xMinData,\n    xDataRange\n\n  chartData.axisData = {\n    xStart: null,\n    xEnd: null,\n    yStart: null,\n    yEnd: null,\n\n    yIsSamePart: null,\n    xIsSamePart: null,\n\n    yZero: null,\n    yPlusSpacing: null,\n    yMinusSpacing: null,\n    ySpacing: null,\n    yEachSpacing: null,\n    xZero: null,\n    xPlusSpacing: null,\n    xMinusSpacing: null,\n    xSpacing: null,\n    xEachSpacing: null,\n\n    yMaxData: null,\n    yMinData: null,\n    yDataRange: null,\n    xMaxData: null,\n    xMinData: null,\n    xDataRange: null,\n\n    xAxisLabelPoint: [],\n    xAxisTickPoint: [],\n    xAxisLinePoint: {},\n    xAxisSplitLinePoint: [],\n    xAxisNamePoint: {},\n\n    yAxisLabelPoint: [],\n    yAxisTickPoint: [],\n    yAxisLinePoint: {},\n    yAxisSplitLinePoint: [],\n    yAxisNamePoint: {},\n  }\n\n  function calAxisValue(axis = 'x') {\n    let barDataObject = {} // 因为层叠柱状图，柱状图数据单独计算\n    let allDataObject = {}\n    let allDataArr = []\n\n    Object.keys(seriesMap).forEach(type => {\n      if (type == 'bar') {\n        seriesMap['bar'].forEach(seriesItem => {\n          if (seriesItem.stack) {\n            if (!barDataObject[seriesItem.stack]) {\n              barDataObject[seriesItem.stack] = []\n            }\n\n            barDataObject[seriesItem.stack].push(seriesItem.data)\n          } else {\n            if (!barDataObject[seriesItem.name]) {\n              barDataObject[seriesItem.name] = []\n            }\n\n            barDataObject[seriesItem.name].push(seriesItem.data)\n          }\n        })\n\n        allDataObject[type] = barDataObject\n      }\n\n      if (type == 'line' || type == 'scatter' || type == 'candlestick') {\n        seriesMap[type].forEach(seriesItem => {\n          if (!allDataObject[type]) {\n            allDataObject[type] = []\n          }\n\n          allDataObject[type].push(seriesItem.data)\n        })\n      }\n    })\n\n    Object.keys(allDataObject).forEach(type => {\n      if (type == 'bar') {\n        Object.keys(allDataObject[type]).forEach(key => {\n          if (barDataObject[key].length > 1) {\n            let stackDataArr = barDataObject[key].reduce((stackDataArr, dataArr) => {\n              if (stackDataArr.length == 0) {\n                stackDataArr = dataArr\n              } else {\n                dataArr.forEach((dataItem, dataIndex) => {\n                  stackDataArr[dataIndex] += dataItem\n                })\n              }\n              return stackDataArr\n            }, [])\n            allDataArr = allDataArr.concat(stackDataArr)\n          } else {\n            let dataArr = barDataObject[key][0]\n            allDataArr = allDataArr.concat(dataArr)\n          }\n        })\n      }\n\n      if (type == 'line') {\n        allDataObject[type].forEach(dataArr => {\n          let filterData = dataArr.reduce((data, dataItem) => {\n            dataItem = Number(dataItem)\n            if (!isNaN(dataItem) && typeof dataItem == 'number') {\n              data.push(dataItem)\n            }\n            return data\n          }, [])\n\n          allDataArr = allDataArr.concat(filterData)\n        })\n      }\n\n      if (type == 'scatter') {\n        allDataObject[type].forEach(dataArr => {\n          let filterData = dataArr.reduce((data, dataItem) => {\n            dataItem = Number(dataItem[axis])\n            if (!isNaN(dataItem) && typeof dataItem == 'number') {\n              data.push(dataItem)\n            }\n            return data\n          }, [])\n\n          allDataArr = allDataArr.concat(filterData)\n        })\n      }\n\n      if (type == 'candlestick') {\n        allDataObject[type].forEach(dataArr => {\n          let filterData = dataArr.reduce((data, dataItem) => {\n            dataItem = Number(dataItem[2])\n            if (!isNaN(dataItem) && typeof dataItem == 'number') {\n              data.push(dataItem)\n            }\n            return data\n          }, [])\n\n          allDataArr = allDataArr.concat(filterData)\n        })\n      }\n    })\n\n    let axisLabelDataArr = []\n    let splitNumber = axis == 'x' ? xAxisSplitNumber : yAxisSplitNumber\n    let max = axis == 'x' ? xAxisMax : yAxisMax\n    let min = axis == 'x' ? xAxisMin : yAxisMin\n    let maxData = Math.max(...allDataArr)\n    let minData = Math.min(...allDataArr)\n    let dataRange = 0\n    let dataEachRange = 0\n    let limit = 1\n    let multiple = 1\n    console.log(`首次获取${axis}轴数据, maxData: ${maxData}, minData: ${minData}`, allDataArr)\n\n    max = max == 'auto' ? max : Number(max)\n    min = min == 'auto' ? min : Number(min)\n\n    // 判断是否传入max,min\n    if (max == 'auto' || min == 'auto') {\n      if (max == 'auto') {\n        maxData = maxData <= 0 && minData <= 0 ? 0 : maxData\n      } else {\n        maxData = max\n      }\n      if (min == 'auto') {\n        minData = maxData >= 0 && minData >= 0 ? 0 : minData\n      } else {\n        minData = min\n      }\n      dataRange = maxData - minData\n      console.log(`修正数据, max: ${max}, min: ${min}, maxData = ${maxData}, minData = ${minData}`)\n      console.log(`修正数据范围, dataRange = ${dataRange}`)\n    } else {\n      maxData = max\n      minData = min\n      dataRange = maxData - minData\n      console.log(`修正数据, max: ${max}, min: ${min}, maxData: ${maxData}, minData: ${minData}`)\n      console.log(`固定数据范围, dataRange = ${dataRange}`)\n    }\n\n    // 是否同时为正数或负数，为false时同时存在正负数\n    let isSamePart = maxData > 0 && minData < 0 ? false : true\n\n    if (dataRange >= 10000) {\n      limit = 1000\n      console.log(`dataRange>=10000`)\n    } else if (dataRange >= 1000) {\n      limit = 100\n      console.log(`dataRange>=1000`)\n    } else if (dataRange >= 100) {\n      limit = 10\n      console.log(`dataRange>=100`)\n    } else if (dataRange >= 10) {\n      limit = 5\n      console.log(`dataRange>=10`)\n    } else if (dataRange >= 1) {\n      limit = 0.1\n      console.log(`dataRange>=1`)\n    } else if (dataRange >= 0.1) {\n      limit = 0.01\n      console.log(`dataRange>=0.1`)\n    } else if (dataRange >= 0.01) {\n      limit = 0.001\n      console.log(`dataRange>=0.01`)\n    } else if (dataRange >= 0.001) {\n      limit = 0.0001\n      console.log(`dataRange>=0.001`)\n    } else {\n      limit = 0.00001\n      console.log(`dataRange<0.0001`)\n    }\n\n    while (limit < 1) {\n      limit *= 10\n      multiple *= 10\n      console.log(`limit<1, limit: ${limit}, multiple: ${multiple}`)\n    }\n    console.log(`limit = ${limit}, multiple = ${multiple}`)\n\n    if (max == 'auto' && min == 'auto') {\n      if (maxData >= 0 && minData >= 0) {\n        dataRange = dataRange * multiple\n        dataEachRange = Math.ceil(dataRange / splitNumber)\n        while (dataEachRange % limit !== 0) {\n          dataEachRange += 1\n        }\n        dataEachRange = dataEachRange / multiple\n        dataRange = dataEachRange * splitNumber\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        maxData = minData + dataRange\n        console.log(`同为正数且 max: auto, min: auto, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      } else if (maxData <= 0 && minData <= 0) {\n        dataRange = dataRange * multiple\n        dataEachRange = Math.floor(dataRange / splitNumber)\n        while (dataEachRange % limit !== 0) {\n          dataEachRange += 1\n        }\n        dataEachRange = dataEachRange / multiple\n        dataRange = dataEachRange * splitNumber\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        minData = maxData - dataRange\n        console.log(`同为负数且 max: auto, min: auto, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      } else {\n        dataRange = dataRange * multiple\n        dataEachRange = Math.ceil(dataRange / splitNumber)\n        while (dataEachRange % limit !== 0) {\n          dataEachRange += 1\n        }\n        dataEachRange = dataEachRange / multiple\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n\n        axisLabelDataArr.push(0)\n\n        let data = 0\n        while (data < maxData) {\n          data += dataEachRange\n          axisLabelDataArr.push(data)\n        }\n        maxData = data\n\n        data = 0\n        while (data > minData) {\n          data -= dataEachRange\n          axisLabelDataArr.unshift(data)\n        }\n        minData = data\n        dataRange = maxData - minData\n        console.log(`修正数据, maxData = ${maxData}, minData = ${minData}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        console.log(`正负数且 max: auto, min: auto, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      }\n    }\n\n    if (max == 'auto' && typeof min == 'number') {\n      if (maxData >= 0 && minData >= 0) {\n        dataRange = dataRange * multiple\n        dataEachRange = Math.ceil(dataRange / splitNumber)\n        while (dataEachRange % limit !== 0) {\n          dataEachRange += 1\n        }\n        dataEachRange = dataEachRange / multiple\n        dataRange = dataEachRange * splitNumber\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        maxData = minData + dataRange\n        console.log(`同为正数且 max: auto, min: ${min}, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      } else if (maxData <= 0 && minData <= 0) {\n        dataRange = dataRange * multiple\n        dataEachRange = Number((dataRange / splitNumber).toFixed(2))\n        dataEachRange = dataEachRange / multiple\n        dataRange = dataEachRange * splitNumber\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        console.log(`同为负数且 max: auto, min: ${min}, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      } else {\n        dataRange = dataRange * multiple\n        dataEachRange = Math.ceil(dataRange / splitNumber)\n        while (dataEachRange % limit !== 0) {\n          dataEachRange += 1\n        }\n        dataEachRange = dataEachRange / multiple\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n\n        axisLabelDataArr.push(0)\n\n        let data = 0\n        while (data < maxData) {\n          data += dataEachRange\n          axisLabelDataArr.push(data)\n        }\n        maxData = data\n\n        data = 0\n        while (data - dataEachRange > minData) {\n          data -= dataEachRange\n          axisLabelDataArr.unshift(data)\n        }\n        axisLabelDataArr.unshift(minData)\n\n        dataRange = maxData - minData\n        console.log(`修正数据, maxData = ${maxData}, minData = ${minData}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        console.log(`正负数且 max: auto, min: ${min}, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      }\n    }\n\n    if (typeof max == 'number' && min == 'auto') {\n      console.log(`max: ${max}, min: auto`)\n\n      if (maxData >= 0 && minData >= 0) {\n        dataRange = dataRange * multiple\n        dataEachRange = Number((dataRange / splitNumber).toFixed(2))\n        dataEachRange = dataEachRange / multiple\n        dataRange = dataEachRange * splitNumber\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        console.log(`同为正数且 max: ${max}, min: auto, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      } else if (maxData <= 0 && minData <= 0) {\n        dataRange = dataRange * multiple\n        dataEachRange = Math.floor(dataRange / splitNumber)\n        while (dataEachRange % limit !== 0) {\n          dataEachRange += 1\n        }\n        dataEachRange = dataEachRange / multiple\n        dataRange = dataEachRange * splitNumber\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        minData = maxData - dataRange\n        console.log(`同为负数且 max: ${max}, min: auto, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      } else {\n        dataRange = dataRange * multiple\n        dataEachRange = Math.ceil(dataRange / splitNumber)\n        while (dataEachRange % limit !== 0) {\n          dataEachRange += 1\n        }\n        dataEachRange = dataEachRange / multiple\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n\n        axisLabelDataArr.push(0)\n\n        let data = 0\n        while (data + dataEachRange < maxData) {\n          data += dataEachRange\n          axisLabelDataArr.push(data)\n        }\n        axisLabelDataArr.push(maxData)\n\n        data = 0\n        while (data > minData) {\n          data -= dataEachRange\n          axisLabelDataArr.unshift(data)\n        }\n        minData = data\n\n        dataRange = maxData - minData\n        console.log(`修正数据, maxData = ${maxData}, minData = ${minData}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        console.log(`正负数且 max: ${max}, min: auto, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      }\n    }\n\n    if (typeof max == 'number' && typeof min == 'number') {\n      console.log(`max: ${max}, min: ${min}`)\n\n      if (maxData >= 0 && minData >= 0) {\n        dataEachRange = Number((dataRange / splitNumber).toFixed(2))\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n        console.log(`同为正数且 max: ${max}, min: ${min}, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      } else if (maxData <= 0 && minData <= 0) {\n        dataEachRange = Number((dataRange / splitNumber).toFixed(2))\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n        console.log(`同为负数且 max: ${max}, min: ${min}, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      } else {\n        dataRange = dataRange * multiple\n        dataEachRange = Math.ceil(dataRange / splitNumber)\n\n        while (dataEachRange % limit !== 0) {\n          dataEachRange += 1\n        }\n        dataEachRange = dataEachRange / multiple\n        console.log(`修正数据间隔, dataEachRange = ${dataEachRange}, splitNumber = ${splitNumber}`)\n\n        axisLabelDataArr.push(0)\n\n        let data = 0\n        while (data + dataEachRange < maxData) {\n          data += dataEachRange\n          axisLabelDataArr.push(data)\n        }\n        axisLabelDataArr.push(maxData)\n\n        data = 0\n        while (data - dataEachRange > minData) {\n          data -= dataEachRange\n          axisLabelDataArr.unshift(data)\n        }\n        axisLabelDataArr.unshift(minData)\n\n        dataRange = maxData - minData\n        console.log(`修正数据, maxData = ${maxData}, minData = ${minData}`)\n        console.log(`修正数据范围, dataRange = ${dataRange}`)\n        console.log(`正负数且 max: ${max}, min: ${min}, dataRange = ${dataRange}, dataEachRange = ${dataEachRange}, maxData: ${maxData}, minData: ${minData}`)\n      }\n    }\n\n    if (isSamePart) {\n      for (let i = 0; i <= splitNumber; i++) {\n        let data = minData + dataEachRange * i\n        data = data.toFixed(multiple.toString().length - 1)\n        axisLabelDataArr.push(Number(data))\n      }\n    }\n\n    if (axis == 'x') {\n      xDataRange = dataRange\n      xMaxData = maxData\n      xMinData = minData\n      xIsSamePart = isSamePart\n    } else {\n      yDataRange = dataRange\n      yMaxData = maxData\n      yMinData = minData\n      yIsSamePart = isSamePart\n    }\n\n    console.log(`complete calAxisValue ${axis}`, axisLabelDataArr)\n\n    return axisLabelDataArr\n  }\n\n  // 计算xAxisLabelDataArr, yAxisLabelDataArr\n  let xAxisLabelDataArr = xAxisType == 'category' ? xAxisData : calAxisValue('x')\n  let yAxisLabelDataArr = yAxisType == 'category' ? yAxisData : calAxisValue('y')\n\n  // 计算 xAxisLabelTextArr, xAxisLabelMaxHeight\n  context.font = `${xAxisLabelFontSize}px`\n  let xAxisLabelMaxWidth = 0\n  let xAxisLabelMaxHeight = 0\n  let xAxisLabelTextArr = xAxisLabelDataArr.reduce((xAxisLabelTextArr, dataItem, dataIndex) => {\n    let text = xAxisLabelFormat ? xAxisLabelFormat(dataItem) : dataItem\n    xAxisLabelMaxWidth = Math.max(context.measureText(text).width, xAxisLabelMaxWidth)\n    xAxisLabelTextArr.push(text)\n    return xAxisLabelTextArr\n  }, [])\n\n  xAxisLabelRotate = Number(xAxisLabelRotate)\n\n  if (xAxisLabelRotate == 0) {\n    xAxisLabelMaxHeight = xAxisLabelFontSize\n  } else {\n    xAxisLabelMaxHeight =\n      Math.abs(xAxisLabelMaxWidth * Math.sin((xAxisLabelRotate * Math.PI) / 180)) + Math.abs(xAxisLabelFontSize * Math.cos((xAxisLabelRotate * Math.PI) / 180))\n  }\n\n  // 计算yAxisLabelTextArr, yAxisLabelMaxWidth\n  context.font = `${yAxisLabelFontSize}px`\n  let yAxisLabelMaxWidth = 0\n  let yAxisLabelTextArr = yAxisLabelDataArr.reduce((yAxisLabelTextArr, dataItem, dataIndex) => {\n    let text = yAxisLabelFormat ? yAxisLabelFormat(dataItem) : dataItem\n    yAxisLabelMaxWidth = Math.max(context.measureText(text).width, yAxisLabelMaxWidth)\n    yAxisLabelTextArr.push(text)\n    return yAxisLabelTextArr\n  }, [])\n\n  let xSpacingNumber = 0\n  if (xAxisType == 'category') {\n    xSpacingNumber = xAxisBoundaryGap ? xAxisLabelDataArr.length : xAxisLabelDataArr.length - 1\n  } else {\n    xSpacingNumber = xAxisLabelDataArr.length - 1\n  }\n\n  let ySpacingNumber = 0\n  if (yAxisType == 'category') {\n    ySpacingNumber = yAxisBoundaryGap ? yAxisLabelDataArr.length : yAxisLabelDataArr.length - 1\n  } else {\n    ySpacingNumber = yAxisLabelDataArr.length - 1\n  }\n\n  // 修正yStart\n  if (xAxisShow && xAxisLabelShow) {\n    yStart -= xAxisLabelMaxHeight + xAxisLabelGap\n  }\n  if (xAxisShow && xAxisTickShow) {\n    if ((yAxisType == 'value' && yIsSamePart) || yAxisType == 'category') {\n      yStart -= xAxisTickLength\n    }\n  }\n  if (yIsSamePart) {\n    if (xAxisShow && xAxisLineShow) {\n      yStart -= xAxisLineWidth / 2\n    }\n  } else {\n    if (xAxisShow && xAxisSplitLineShow) {\n      yStart -= xAxisSplitLineWidth / 2\n    }\n  }\n\n  // 修正yEnd\n  if (yAxisShow && yAxisNameShow) {\n    yEnd += yAxisNameFontSize + yAxisNameGap\n  }\n  ySpacing = yStart - yEnd\n  console.log(`初始ySpacing数据, yStart = ${yStart}, yEnd = ${yEnd}, ySpacing = ${ySpacing}`)\n  yEachSpacing = Math.floor(ySpacing / ySpacingNumber)\n  yEnd = yStart - yEachSpacing * ySpacingNumber\n  ySpacing = yStart - yEnd\n  console.log(`修正ySpacing数据, yStart = ${yStart}, yEnd = ${yEnd}, ySpacing = ${ySpacing}`)\n\n  // 修正xStart\n  if (yAxisLabelShow) {\n    xStart += yAxisLabelMaxWidth + yAxisLabelGap\n  }\n  if (yAxisShow && yAxisTickShow) {\n    if ((xAxisType == 'value' && xIsSamePart) || xAxisType == 'category') {\n      xStart += yAxisTickLength\n    }\n  }\n  if (xIsSamePart) {\n    if (yAxisShow && yAxisLineShow) {\n      xStart += yAxisLineWidth / 2\n    }\n  } else {\n    if (yAxisShow && yAxisSplitLineShow) {\n      xStart += yAxisSplitLineWidth / 2\n    }\n  }\n\n  // 修正xEnd\n  if (xAxisShow && xAxisNameShow) {\n    context.font = `${xAxisNameFontSize}px`\n    let xAxisNameTextWidth = context.measureText(xAxisNameText).width\n    xEnd -= xAxisNameTextWidth + xAxisNameGap\n  }\n  xSpacing = xEnd - xStart\n  console.log(`初始xSpacing数据, xStart = ${xStart}, xEnd = ${xEnd}, xSpacing = ${xSpacing}`)\n  xEachSpacing = Math.floor(xSpacing / xSpacingNumber)\n  xEnd = xStart + xEachSpacing * xSpacingNumber\n  xSpacing = xEnd - xStart\n  console.log(`修正xSpacing数据, xStart = ${xStart}, xEnd = ${xEnd}, xSpacing = ${xSpacing}`)\n\n  // 计算yZero\n  if (yAxisType == 'value' && !yIsSamePart) {\n    yAxisLabelDataArr.reduce((arr, item, index) => {\n      if (index == 0) {\n        arr.push({\n          y: yStart,\n        })\n      } else {\n        let spacing = (Math.abs(yAxisLabelDataArr[index - 1] - yAxisLabelDataArr[index]) * ySpacing) / yDataRange\n\n        arr.push({\n          y: arr[index - 1].y - spacing,\n        })\n      }\n\n      if (item == 0) {\n        yZero = arr[index].y // 存在正负值时计算0线的y坐标\n        console.log(`yZero = ${yZero}`)\n      }\n\n      if (index + 1 == yAxisLabelDataArr.length) {\n        yEnd = arr[index].y\n        ySpacing = yStart - yEnd\n        console.log(`修正yEnd, yEnd = ${yEnd}`)\n        console.log(`修正ySpacing, ySpacing = ${ySpacing}`)\n        yPlusSpacing = yZero - yEnd\n        yMinusSpacing = yStart - yZero\n      }\n\n      return arr\n    }, [])\n  }\n\n  // 计算xZero\n  if (xAxisType == 'value' && !xIsSamePart) {\n    xAxisLabelDataArr.reduce((arr, item, index) => {\n      if (index == 0) {\n        arr.push({\n          x: xStart,\n        })\n      } else {\n        let spacing = (Math.abs(xAxisLabelDataArr[index] - xAxisLabelDataArr[index - 1]) * xSpacing) / xDataRange\n        arr.push({\n          x: arr[index - 1].x + spacing,\n        })\n      }\n\n      if (item == 0) {\n        xZero = arr[index].x // 存在正负值时计算0线的x坐标\n        console.log(`xZero = ${xZero}`)\n      }\n\n      if (index + 1 == xAxisLabelDataArr.length) {\n        xEnd = arr[index].x\n        xSpacing = xEnd - xStart\n        console.log(`修正xEnd, xEnd = ${xEnd}`)\n        console.log(`修正xSpacing, xSpacing = ${xSpacing}`)\n        xPlusSpacing = xEnd - xZero\n        xMinusSpacing = xZero - xStart\n      }\n\n      return arr\n    }, [])\n  }\n\n  // 计算 yAxis 各项数据\n  if (yAxisType == 'value') {\n    let _xStart = xStartInit\n    if (yAxisShow && yAxisLabelShow) {\n      _xStart += yAxisLabelMaxWidth // 更新_xStart数据\n    }\n\n    // 计算 yAxisLabelPoint\n    chartData.axisData.yAxisLabelPoint = yAxisLabelTextArr.reduce((yAxisLabelPoint, item, index) => {\n      if (index == 0) {\n        yAxisLabelPoint.push({\n          text: item,\n          x: _xStart,\n          y: yStart,\n        })\n      } else {\n        let spacing = (Math.abs(yAxisLabelDataArr[index - 1] - yAxisLabelDataArr[index]) * ySpacing) / yDataRange\n\n        yAxisLabelPoint.push({\n          text: item,\n          x: _xStart,\n          y: yAxisLabelPoint[index - 1].y - spacing,\n        })\n      }\n\n      return yAxisLabelPoint\n    }, [])\n\n    // 计算 yAxisSplitLinePoint\n    if (yAxisShow && yAxisSplitLineShow) {\n      let _xStart = xStartInit\n      if (yAxisShow && yAxisLabelShow) {\n        _xStart += yAxisLabelMaxWidth + yAxisLabelGap // 更新_xStart数据\n      }\n      if (yAxisShow && yAxisTickShow) {\n        if (xIsSamePart) {\n          _xStart += yAxisTickLength // 更新_xStart数据\n        }\n      }\n\n      chartData.axisData.yAxisSplitLinePoint = chartData.axisData.yAxisLabelPoint.reduce((yAxisSplitLinePoint, item, index) => {\n        yAxisSplitLinePoint.push({\n          startX: _xStart, // 起点x坐标\n          startY: item.y, // 起点y坐标\n          endX: xEnd, // 终点x坐标\n          endY: item.y, // 终点y坐标\n        })\n        return yAxisSplitLinePoint\n      }, [])\n    }\n\n    // 计算 yAxisTickPoint\n    if (yAxisShow && yAxisTickShow) {\n      let _xStart = xStartInit\n\n      if (xIsSamePart) {\n        if (yAxisShow && yAxisLabelShow) {\n          _xStart += yAxisLabelMaxWidth + yAxisLabelGap // 更新_xStart数据\n        }\n      } else {\n        _xStart = xZero - xAxisLineWidth / 2 - yAxisTickLength\n      }\n\n      chartData.axisData.yAxisTickPoint = chartData.axisData.yAxisLabelPoint.reduce((yAxisTickPoint, item, index) => {\n        yAxisTickPoint.push({\n          startX: _xStart, // 起点x坐标\n          startY: item.y, // 起点y坐标\n          endX: _xStart + yAxisTickLength, // 终点x坐标\n          endY: item.y, // 终点y坐标\n        })\n        return yAxisTickPoint\n      }, [])\n    }\n\n    // 计算 yAxisLinePoint\n    if (yAxisShow && yAxisLineShow) {\n      let _xStart = xStartInit\n      if (xIsSamePart) {\n        if (yAxisShow && yAxisLabelShow) {\n          _xStart += yAxisLabelMaxWidth + yAxisLabelGap // 更新_xStart数据\n        }\n        if (yAxisShow && yAxisTickShow) {\n          _xStart += yAxisTickLength // 更新_xStart数据\n        }\n        _xStart += yAxisLineWidth / 2\n      } else {\n        _xStart = xZero\n      }\n\n      chartData.axisData.yAxisLinePoint = {\n        startX: _xStart, // 起点x坐标\n        startY: yStart, // 起点y坐标\n        endX: _xStart, // 终点x坐标\n        endY: yEnd - yAxisTickLineWidth / 2, // 终点y坐标\n      }\n    }\n\n    // 计算 yAxisNamePoint\n    if (yAxisShow && yAxisNameShow) {\n      let _xStart = xStartInit\n      if (xIsSamePart) {\n        if (yAxisShow && yAxisLabelShow) {\n          _xStart += yAxisLabelMaxWidth + yAxisLabelGap // 更新_xStart数据\n        }\n        if (yAxisShow && yAxisTickShow) {\n          _xStart += yAxisTickLength // 更新_xStart数据\n        }\n        if (yAxisShow && yAxisLineShow) {\n          _xStart += yAxisLineWidth / 2 // 更新_xStart数据\n        }\n      } else {\n        _xStart = xZero\n      }\n\n      chartData.axisData.yAxisNamePoint = {\n        text: yAxisNameText,\n        x: _xStart,\n        y: yEnd - yAxisNameGap,\n      }\n    }\n  }\n\n  // 计算 xAxis 各项数据\n  if (xAxisType == 'value') {\n    let _yStart = yStartInit\n    if (xAxisShow && xAxisLabelShow) {\n      _yStart -= xAxisLabelMaxHeight // 更新_yStart数据\n    }\n\n    // 计算 xAxisLabelPoint\n    chartData.axisData.xAxisLabelPoint = xAxisLabelTextArr.reduce((xAxisLabelPoint, item, index) => {\n      if (index == 0) {\n        xAxisLabelPoint.push({\n          text: item,\n          x: xStart,\n          y: _yStart,\n        })\n      } else {\n        let spacing = (Math.abs(xAxisLabelDataArr[index] - xAxisLabelDataArr[index - 1]) * xSpacing) / xDataRange\n\n        xAxisLabelPoint.push({\n          text: item,\n          x: xAxisLabelPoint[index - 1].x + spacing,\n          y: _yStart,\n        })\n      }\n\n      return xAxisLabelPoint\n    }, [])\n\n    // 计算 xAxisSplitLinePoint\n    if (xAxisShow && xAxisSplitLineShow) {\n      let _yStart = yStartInit\n      if (xAxisShow && xAxisLabelShow) {\n        _yStart -= xAxisLabelMaxHeight + xAxisLabelGap // 更新_yStart数据\n      }\n      if (xAxisShow && xAxisTickShow) {\n        if (yIsSamePart) {\n          _yStart -= xAxisTickLength // 更新_yStart数据\n        }\n      }\n\n      chartData.axisData.xAxisSplitLinePoint = chartData.axisData.xAxisLabelPoint.reduce((xAxisSplitLinePoint, item, index) => {\n        xAxisSplitLinePoint.push({\n          startX: item.x, // 起点x坐标\n          startY: _yStart, // 起点y坐标\n          endX: item.x, // 终点x坐标\n          endY: yEnd, // 终点y坐标\n        })\n        return xAxisSplitLinePoint\n      }, [])\n    }\n\n    // 计算 xAxisTickPoint\n    if (xAxisShow && xAxisTickShow) {\n      let _yStart = yStartInit\n      if (yIsSamePart) {\n        if (yAxisShow && yAxisLabelShow) {\n          _yStart -= xAxisLabelMaxHeight + xAxisLabelGap // 更新_yStart数据\n        }\n      } else {\n        _yStart = yZero + xAxisLineWidth / 2 + yAxisTickLength\n      }\n\n      chartData.axisData.xAxisTickPoint = chartData.axisData.xAxisLabelPoint.reduce((xAxisTickPoint, item, index) => {\n        xAxisTickPoint.push({\n          startX: item.x, // 起点x坐标\n          startY: _yStart, // 起点y坐标\n          endX: item.x, // 终点x坐标\n          endY: _yStart - xAxisTickLength, // 终点y坐标\n        })\n        return xAxisTickPoint\n      }, [])\n    }\n\n    // 计算 xAxisLinePoint\n    if (xAxisShow && xAxisLineShow) {\n      let _yStart = yStartInit\n      if (yIsSamePart) {\n        if (xAxisShow && xAxisLabelShow) {\n          _yStart -= xAxisLabelMaxHeight + xAxisLabelGap // 更新_yStart数据\n        }\n        if (xAxisShow && xAxisTickShow) {\n          _yStart -= xAxisTickLength // 更新_yStart数据\n        }\n        _yStart -= xAxisLineWidth / 2\n      } else {\n        _yStart = yZero\n      }\n\n      chartData.axisData.xAxisLinePoint = {\n        startX: xStart, // 起点x坐标\n        startY: _yStart, // 起点y坐标\n        endX: xEnd + xAxisTickLineWidth / 2, // 终点x坐标\n        endY: _yStart, // 终点y坐标\n      }\n    }\n\n    // 计算 xAxisNamePoint\n    if (xAxisShow && xAxisNameShow) {\n      let _yStart = yStartInit\n      if (xIsSamePart) {\n        if (xAxisShow && xAxisLabelShow) {\n          _yStart -= xAxisLabelMaxHeight + xAxisLabelGap // 更新_yStart数据\n        }\n        if (xAxisShow && xAxisTickShow) {\n          _yStart -= xAxisTickLength // 更新_yStart数据\n        }\n        if (xAxisShow && xAxisLineShow) {\n          _yStart -= xAxisLineWidth / 2 // 更新_yStart数据\n        }\n      } else {\n        _yStart = yZero\n      }\n\n      chartData.axisData.xAxisNamePoint = {\n        text: xAxisNameText,\n        x: xEnd + xAxisNameGap,\n        y: _yStart,\n      }\n    }\n  }\n\n  // 计算 yAxis 各项数据\n  if (yAxisType == 'category') {\n    // 计算 yAxisLabelPoint\n    chartData.axisData.yAxisLabelPoint = yAxisLabelTextArr.reduce((yAxisLabelPoint, item, index) => {\n      let _xStart = xStartInit\n      if (yAxisShow && yAxisLabelShow) {\n        _xStart += yAxisLabelMaxWidth // 更新_xStart数据\n      }\n\n      if (yAxisBoundaryGap) {\n        yAxisLabelPoint.push({\n          show: true,\n          text: item,\n          x: _xStart,\n          y: yStart - yEachSpacing * (index + 1) + yEachSpacing / 2,\n        })\n      } else {\n        yAxisLabelPoint.push({\n          show: true,\n          text: item,\n          x: _xStart,\n          y: yStart - yEachSpacing * index,\n        })\n      }\n\n      return yAxisLabelPoint\n    }, [])\n\n    if (yAxisLabelShowIndex && yAxisLabelShowIndex.length) {\n      chartData.axisData.yAxisLabelPoint = chartData.axisData.yAxisLabelPoint.map((item, index) => {\n        let isShow = yAxisLabelShowIndex.some(showIndex => {\n          return showIndex === index\n        })\n\n        if (isShow) {\n          item.show = true\n        } else {\n          item.show = false\n        }\n\n        return item\n      })\n    }\n\n    // 计算 yAxisSplitLinePoint\n    if (yAxisShow && yAxisSplitLineShow) {\n      let _xStart = xStartInit\n      if (yAxisShow && yAxisLabelShow) {\n        _xStart += yAxisLabelMaxWidth + yAxisLabelGap // 更新_xStart数据\n      }\n      if (yAxisShow && yAxisTickShow) {\n        if (xIsSamePart) {\n          _xStart += yAxisTickLength // 更新_xStart数据\n        }\n      }\n\n      // yAxisSplitLineAlign为true时，刻度线与标签对齐\n      let yAxisSplitLineNumber = 0\n      if (yAxisBoundaryGap) {\n        yAxisSplitLineNumber = yAxisSplitLineAlign ? yAxisLabelDataArr.length : yAxisLabelDataArr.length + 1\n      } else {\n        yAxisSplitLineNumber = yAxisLabelDataArr.length\n      }\n\n      for (let index = 0; index < yAxisSplitLineNumber; index++) {\n        if (yAxisBoundaryGap && yAxisSplitLineAlign) {\n          chartData.axisData.yAxisSplitLinePoint.push({\n            show: true,\n            startX: _xStart,\n            startY: yStart - yEachSpacing * index - yEachSpacing / 2,\n            endX: xEnd,\n            endY: yStart - yEachSpacing * index - yEachSpacing / 2,\n          })\n        } else {\n          chartData.axisData.yAxisSplitLinePoint.push({\n            show: true,\n            startX: _xStart,\n            startY: yStart - yEachSpacing * index,\n            endX: xEnd,\n            endY: yStart - yEachSpacing * index,\n          })\n        }\n      }\n    }\n\n    if (yAxisSplitLineShowIndex && yAxisSplitLineShowIndex.length) {\n      chartData.axisData.yAxisSplitLinePoint = chartData.axisData.yAxisSplitLinePoint.map((item, index) => {\n        let isShow = yAxisSplitLineShowIndex.some(showIndex => {\n          return showIndex === index\n        })\n\n        if (isShow) {\n          item.show = true\n        } else {\n          item.show = false\n        }\n\n        return item\n      })\n    }\n\n    // 计算 yAxisTickPoint\n    if (yAxisShow && yAxisTickShow) {\n      let _xStart = xStartInit\n\n      if (xIsSamePart) {\n        if (yAxisShow && yAxisLabelShow) {\n          _xStart += yAxisLabelMaxWidth + yAxisLabelGap // 更新_xStart数据\n        }\n      } else {\n        _xStart = xZero - yAxisLineWidth / 2 - yAxisTickLength\n      }\n\n      // yAxisTickAlign为true时，刻度线与标签对齐\n      let yAxisTickNumber = 0\n      if (yAxisBoundaryGap) {\n        yAxisTickNumber = yAxisTickAlign ? yAxisLabelDataArr.length : yAxisLabelDataArr.length + 1\n      } else {\n        yAxisTickNumber = yAxisLabelDataArr.length\n      }\n\n      for (let index = 0; index < yAxisTickNumber; index++) {\n        if (yAxisBoundaryGap && yAxisTickAlign) {\n          chartData.axisData.yAxisTickPoint.push({\n            show: true,\n            startX: _xStart,\n            startY: yStart - yEachSpacing * index - yEachSpacing / 2,\n            endX: _xStart + yAxisTickLength,\n            endY: yStart - yEachSpacing * index - yEachSpacing / 2,\n          })\n        } else {\n          chartData.axisData.yAxisTickPoint.push({\n            show: true,\n            startX: _xStart,\n            startY: yStart - yEachSpacing * index,\n            endX: _xStart + yAxisTickLength,\n            endY: yStart - yEachSpacing * index,\n          })\n        }\n      }\n    }\n\n    if (yAxisTickShowIndex && yAxisTickShowIndex.length) {\n      chartData.axisData.yAxisTickPoint = chartData.axisData.yAxisTickPoint.map((item, index) => {\n        let isShow = yAxisTickShowIndex.some(showIndex => {\n          return showIndex === index\n        })\n\n        if (isShow) {\n          item.show = true\n        } else {\n          item.show = false\n        }\n\n        return item\n      })\n    }\n\n    // 计算 yAxisLinePoint\n    if (yAxisShow && yAxisLineShow) {\n      let _xStart = xStartInit\n      if (xIsSamePart) {\n        if (yAxisShow && yAxisLabelShow) {\n          _xStart += yAxisLabelMaxWidth + yAxisLabelGap // 更新_xStart数据\n        }\n        if (yAxisShow && yAxisTickShow) {\n          _xStart += yAxisTickLength // 更新_xStart数据\n        }\n        _xStart += yAxisLineWidth / 2\n      } else {\n        _xStart = xZero\n      }\n\n      chartData.axisData.yAxisLinePoint = {\n        startX: _xStart,\n        startY: yStart,\n        endX: _xStart,\n        endY: yEnd - yAxisTickLineWidth / 2,\n      }\n    }\n\n    // 计算 yAxisNamePoint\n    if (yAxisShow && yAxisNameShow) {\n      let _xStart = xStartInit\n      if (xIsSamePart) {\n        if (yAxisShow && yAxisLabelShow) {\n          _xStart += yAxisLabelMaxWidth + yAxisLabelGap // 更新_xStart数据\n        }\n        if (yAxisShow && yAxisTickShow) {\n          _xStart += yAxisTickLength // 更新_xStart数据\n        }\n        if (yAxisShow && yAxisLineShow) {\n          _xStart += yAxisLineWidth / 2 // 更新_xStart数据\n        }\n      } else {\n        _xStart = xZero\n      }\n\n      chartData.axisData.yAxisNamePoint = {\n        text: yAxisNameText,\n        x: _xStart,\n        y: yEnd - yAxisNameGap,\n      }\n    }\n  }\n\n  // 计算 xAxis 各项数据\n  if (xAxisType == 'category') {\n    // 计算 xAxisLabelPoint\n    chartData.axisData.xAxisLabelPoint = xAxisLabelTextArr.reduce((xAxisLabelPoint, item, index) => {\n      let _yStart = yStartInit\n      if (xAxisShow && xAxisLabelShow) {\n        _yStart -= xAxisLabelMaxHeight // 更新_yStart数据\n      }\n\n      if (xAxisBoundaryGap) {\n        xAxisLabelPoint.push({\n          show: true,\n          text: item,\n          x: xStart + xEachSpacing * (index + 1) - xEachSpacing / 2,\n          y: _yStart,\n        })\n      } else {\n        xAxisLabelPoint.push({\n          show: true,\n          text: item,\n          x: xStart + xEachSpacing * index,\n          y: _yStart,\n        })\n      }\n      return xAxisLabelPoint\n    }, [])\n\n    if (xAxisLabelShowIndex && xAxisLabelShowIndex.length) {\n      chartData.axisData.xAxisLabelPoint = chartData.axisData.xAxisLabelPoint.map((item, index) => {\n        let isShow = xAxisLabelShowIndex.some(showIndex => {\n          return showIndex === index\n        })\n\n        if (isShow) {\n          item.show = true\n        } else {\n          item.show = false\n        }\n\n        return item\n      })\n    }\n\n    // 计算 xAxisSplitLinePoint\n    if (xAxisShow && xAxisSplitLineShow) {\n      let _yStart = yStartInit\n      if (xAxisShow && xAxisLabelShow) {\n        _yStart -= xAxisLabelMaxHeight + xAxisLabelGap // 更新_yStart数据\n      }\n      if (xAxisShow && xAxisTickShow) {\n        if (yIsSamePart) {\n          _yStart -= xAxisTickLength // 更新_yStart数据\n        }\n      }\n\n      // xAxisSplitLineAlign为true时，网格线与标签对齐\n      let xAxisSplitLineNumber = 0\n      if (xAxisBoundaryGap) {\n        xAxisSplitLineNumber = xAxisSplitLineAlign ? xAxisLabelDataArr.length : xAxisLabelDataArr.length + 1\n      } else {\n        xAxisSplitLineNumber = xAxisLabelDataArr.length\n      }\n\n      for (let index = 0; index < xAxisSplitLineNumber; index++) {\n        if (xAxisBoundaryGap && xAxisSplitLineAlign) {\n          chartData.axisData.xAxisSplitLinePoint.push({\n            show: true,\n            startX: xStart + xEachSpacing * index + xEachSpacing / 2,\n            startY: _yStart,\n            endX: xStart + xEachSpacing * index + xEachSpacing / 2,\n            endY: yEnd,\n          })\n        } else {\n          chartData.axisData.xAxisSplitLinePoint.push({\n            show: true,\n            startX: xStart + xEachSpacing * index,\n            startY: _yStart,\n            endX: xStart + xEachSpacing * index,\n            endY: yEnd,\n          })\n        }\n      }\n    }\n\n    if (xAxisSplitLineShowIndex && xAxisSplitLineShowIndex.length) {\n      chartData.axisData.xAxisSplitLinePoint = chartData.axisData.xAxisSplitLinePoint.map((item, index) => {\n        let isShow = xAxisSplitLineShowIndex.some(showIndex => {\n          return showIndex === index\n        })\n\n        if (isShow) {\n          item.show = true\n        } else {\n          item.show = false\n        }\n\n        return item\n      })\n    }\n\n    // 计算 xAxisTickPoint\n    if (xAxisShow && xAxisTickShow) {\n      let _yStart = yStartInit\n\n      if (yIsSamePart) {\n        if (xAxisShow && xAxisLabelShow) {\n          _yStart -= xAxisLabelMaxHeight + xAxisLabelGap // 更新_yStart数据\n        }\n        if (xAxisShow && xAxisTickShow) {\n          _yStart -= xAxisTickLength // 更新_yStart数据\n        }\n      } else {\n        _yStart = yZero - xAxisLineWidth / 2\n      }\n\n      // xAxisTickAlign为true时，刻度线与标签对齐\n      let xAxisTickNumber = 0\n      if (xAxisBoundaryGap) {\n        xAxisTickNumber = xAxisTickAlign ? xAxisLabelDataArr.length : xAxisLabelDataArr.length + 1\n      } else {\n        xAxisTickNumber = xAxisLabelDataArr.length\n      }\n\n      for (let index = 0; index < xAxisTickNumber; index++) {\n        if (xAxisBoundaryGap && xAxisTickAlign) {\n          chartData.axisData.xAxisTickPoint.push({\n            show: true,\n            startX: xStart + xEachSpacing * index + xEachSpacing / 2,\n            startY: _yStart,\n            endX: xStart + xEachSpacing * index + xEachSpacing / 2,\n            endY: _yStart + xAxisTickLength,\n          })\n        } else {\n          chartData.axisData.xAxisTickPoint.push({\n            show: true,\n            startX: xStart + xEachSpacing * index,\n            startY: _yStart,\n            endX: xStart + xEachSpacing * index,\n            endY: _yStart + xAxisTickLength,\n          })\n        }\n      }\n    }\n\n    if (xAxisTickShowIndex && xAxisTickShowIndex.length) {\n      chartData.axisData.xAxisTickPoint = chartData.axisData.xAxisTickPoint.map((item, index) => {\n        let isShow = xAxisTickShowIndex.some(showIndex => {\n          return showIndex === index\n        })\n\n        if (isShow) {\n          item.show = true\n        } else {\n          item.show = false\n        }\n\n        return item\n      })\n    }\n\n    // 计算 xAxisLinePoint\n    if (xAxisShow && xAxisLineShow) {\n      let _yStart = yStartInit\n      if (yIsSamePart) {\n        if (xAxisShow && xAxisLabelShow) {\n          _yStart -= xAxisLabelMaxHeight + xAxisLabelGap // 更新_yStart数据\n        }\n        if (xAxisShow && xAxisTickShow) {\n          _yStart -= xAxisTickLength // 更新_yStart数据\n        }\n        _yStart -= xAxisLineWidth / 2\n      } else {\n        _yStart = yZero\n      }\n\n      chartData.axisData.xAxisLinePoint = {\n        startX: xStart,\n        startY: _yStart,\n        endX: xEnd + xAxisTickLineWidth / 2,\n        endY: _yStart,\n      }\n    }\n\n    // 计算 xAxisNamePoint\n    if (xAxisShow && xAxisNameShow) {\n      let _yStart = yStartInit\n      if (yIsSamePart) {\n        if (xAxisShow && xAxisLabelShow) {\n          _yStart -= xAxisLabelMaxHeight + xAxisLabelGap // 更新_yStart数据\n        }\n        if (xAxisShow && xAxisTickShow) {\n          _yStart -= xAxisTickLength // 更新_yStart数据\n        }\n        if (xAxisShow && xAxisLineShow) {\n          _yStart -= xAxisLineWidth / 2 // 更新_yStart数据\n        }\n      } else {\n        _yStart = yZero\n      }\n\n      chartData.axisData.xAxisNamePoint = {\n        text: xAxisNameText,\n        x: xEnd + xAxisNameGap,\n        y: _yStart,\n      }\n    }\n  }\n\n  chartData.axisData.xStart = xStart\n  chartData.axisData.xEnd = xEnd\n  chartData.axisData.yStart = yStart\n  chartData.axisData.yEnd = yEnd\n\n  chartData.axisData.yIsSamePart = yIsSamePart\n  chartData.axisData.xIsSamePart = xIsSamePart\n\n  chartData.axisData.yZero = yZero\n  chartData.axisData.yPlusSpacing = yPlusSpacing\n  chartData.axisData.yMinusSpacing = yMinusSpacing\n  chartData.axisData.ySpacing = ySpacing\n  chartData.axisData.yEachSpacing = yEachSpacing\n  chartData.axisData.xZero = xZero\n  chartData.axisData.xPlusSpacing = xPlusSpacing\n  chartData.axisData.xMinusSpacing = xMinusSpacing\n  chartData.axisData.xSpacing = xSpacing\n  chartData.axisData.xEachSpacing = xEachSpacing\n\n  chartData.axisData.yMaxData = yMaxData\n  chartData.axisData.yMinData = yMinData\n  chartData.axisData.yDataRange = yDataRange\n  chartData.axisData.xMaxData = xMaxData\n  chartData.axisData.xMinData = xMinData\n  chartData.axisData.xDataRange = xDataRange\n\n  console.log('complete calAxisData', this.chartData.axisData)\n}\n", "import { percentToNum, convertCoordinateOrigin } from '../util/util'\nimport cloneDeep from 'lodash.clonedeep'\n\nexport default function calAxisRadarData() {\n  let { context, opts, legendData, chartData } = this\n  let { width, height, padding, radarAxis, categories } = opts\n  let { center, radius, splitNumber, axisName: radarAxisName } = radarAxis\n  let { show: radarAxisNameShow, textStyle: radarAxisNameTextStyle } = radarAxisName\n  let { fontSize: radarAxisNameFontSize, margin: radarAxisNameMargin } = radarAxisNameTextStyle\n  let [centerX, centerY] = center\n\n  chartData.radarAxis = {\n    center: [],\n    radius: 0,\n    lineEndPosition: [],\n    namePosition: [],\n    axisNameStyle: radarAxisNameTextStyle,\n  }\n\n  if (typeof centerX == 'string') {\n    centerX = width * percentToNum(centerX)\n  }\n  if (typeof centerY == 'string') {\n    centerY = (height - legendData.legendHeight - padding[2]) * percentToNum(centerY)\n  }\n  if (typeof radius == 'string') {\n    radius = ((height - legendData.legendHeight - padding[2]) * percentToNum(radius)) / 2\n  }\n\n  chartData.radarAxis.center = [centerX, centerY]\n  chartData.radarAxis.radius = radius\n\n  let spacingAangle = (2 * Math.PI) / categories.length\n  let start = Math.PI / 2 // 以90度为起点, 逆时针累加\n\n  let arr = []\n  for (let i = 0; i < splitNumber; i++) {\n    let scale = (splitNumber - i) / splitNumber\n    arr[i] = categories.reduce((arr, item, index) => {\n      let endPoint = {\n        x: radius * Math.cos(start + spacingAangle * index) * scale,\n        y: radius * Math.sin(start + spacingAangle * index) * scale,\n      }\n      arr.push(convertCoordinateOrigin(endPoint, chartData.radarAxis.center))\n      return arr\n    }, [])\n  }\n  chartData.radarAxis.lineEndPosition = arr\n\n  chartData.radarAxis.namePosition = categories.reduce((arr, item, index) => {\n    let point = {\n      x: (radius + radarAxisNameFontSize / 2 + radarAxisNameMargin) * Math.cos(start + spacingAangle * index),\n      y: (radius + radarAxisNameFontSize / 2 + radarAxisNameMargin) * Math.sin(start + spacingAangle * index),\n    }\n    let position = convertCoordinateOrigin(point, chartData.radarAxis.center)\n\n    context.font = `${radarAxisNameFontSize}px`\n\n    arr.push({\n      text: item,\n      point,\n      position,\n    })\n    return arr\n  }, [])\n\n  console.log('complete calAxisRadarData', this.chartData.radarAxis)\n}\n", "import cloneDeep from 'lodash.clonedeep'\n\nexport default function calChartBarData() {\n  let { opts, chartData, seriesMap } = this\n  let { xAxis, yAxis } = opts\n  let seriesBar = seriesMap['bar']\n\n  let {\n    xStart,\n    xEnd,\n    yStart,\n    yEnd,\n    yZero,\n    yPlusSpacing,\n    yMinusSpacing,\n    ySpacing,\n    yEachSpacing,\n    xZero,\n    xPlusSpacing,\n    xMinusSpacing,\n    xSpacing,\n    xEachSpacing,\n    yMaxData,\n    yMinData,\n    yDataRange,\n    xMaxData,\n    xMinData,\n    xDataRange,\n    xAxisLabelPoint,\n    yAxisLabelPoint,\n  } = chartData.axisData\n\n  let autoWidth = 0\n  let autoWidthNumber = 0\n  let sumWidth = 0\n\n  let maxData = xAxis.type == 'value' ? xMaxData : yMaxData\n  let minData = xAxis.type == 'value' ? xMinData : yMinData\n  let dataRange = xAxis.type == 'value' ? xDataRange : yDataRange\n  let valueAxisPlusSpacing = xAxis.type == 'value' ? xPlusSpacing : yPlusSpacing\n  let valueAxisMinusSpacing = xAxis.type == 'value' ? xMinusSpacing : yMinusSpacing\n  let valueAxisSpacing = xAxis.type == 'value' ? xSpacing : ySpacing\n  let categoryAxisEachSpacing = xAxis.type == 'category' ? xEachSpacing : yEachSpacing\n  let categoryAxisData = xAxis.type == 'category' ? xAxis.data : yAxis.data\n\n  let allDataObject = {}\n  seriesBar.forEach(seriesItem => {\n    if (seriesItem.stack) {\n      if (!allDataObject[seriesItem.stack]) {\n        allDataObject[seriesItem.stack] = []\n      }\n\n      allDataObject[seriesItem.stack].push(seriesItem)\n    } else {\n      if (!allDataObject[seriesItem.name]) {\n        allDataObject[seriesItem.name] = []\n      }\n\n      allDataObject[seriesItem.name].push(seriesItem)\n    }\n  })\n\n  let chartBar = []\n\n  for (let i = 0, len = categoryAxisData.length; i < len; i++) {\n    let chartBarArrItem = []\n\n    Object.keys(allDataObject).forEach(key => {\n      chartBarArrItem.push(cloneDeep(allDataObject[key]))\n    })\n    chartBar.push(chartBarArrItem)\n  }\n\n  chartBar.forEach((barItemArr, barItemArrIndex) => {\n    barItemArr.forEach((barItem, barIndex) => {\n      barItem.forEach((seriesItem, seriesIndex) => {\n        let isShow = true\n        if (seriesItem.showIndex && seriesItem.showIndex.length) {\n          isShow = seriesItem.showIndex.some(showIndex => {\n            return showIndex == chartBarArrIndex\n          })\n        }\n        seriesItem.show = isShow\n        seriesItem.data = seriesItem.data[barItemArrIndex]\n\n        if (seriesIndex == 0) {\n          let { barMaxWidth, barMinWidth, barWidth, barGap } = seriesItem\n          if (typeof barWidth == 'number') {\n            if (barWidth > barMaxWidth) {\n              seriesItem.barWidth = barMaxWidth\n            }\n            if (barWidth < barMinWidth) {\n              seriesItem.barWidth = barMinWidth\n            }\n\n            if (barItemArrIndex == 0) {\n              sumWidth += seriesItem.barWidth\n            }\n          } else {\n            if (barItemArrIndex == 0) {\n              autoWidthNumber++\n            }\n          }\n        } else {\n          seriesItem.barWidth = barItem[0].barWidth\n        }\n      })\n\n      if (barItemArrIndex == 0) {\n        if (barIndex == 0) {\n          sumWidth += 2 * barItem[0].barGap\n        } else {\n          sumWidth += barItem[0].barGap\n        }\n      }\n    })\n  })\n\n  // 计算autoWidth\n  if (sumWidth + autoWidthNumber < categoryAxisEachSpacing) {\n    autoWidth = (categoryAxisEachSpacing - sumWidth) / autoWidthNumber\n  } else {\n    autoWidth = 1\n  }\n\n  // 修正barWidth, 计算sumWidth\n  chartBar.forEach((barItemArr, barItemArrIndex) => {\n    barItemArr.forEach((barItem, barIndex) => {\n      barItem.forEach((seriesItem, seriesIndex) => {\n        let { barMaxWidth, barWidth } = seriesItem\n        if (seriesIndex == 0 && barWidth == 'auto') {\n          seriesItem.barWidth = autoWidth > barMaxWidth ? barMaxWidth : autoWidth\n        } else {\n          seriesItem.barWidth = barItem[0].barWidth\n          seriesItem.barGap = barItem[0].barGap\n        }\n\n        if (barItemArrIndex == 0 && seriesIndex == 0 && barWidth == 'auto') {\n          sumWidth += seriesItem.barWidth\n        }\n      })\n    })\n  })\n\n  if (xAxis.type == 'category') {\n    chartBar.forEach((barItemArr, barItemArrIndex) => {\n      let x = xAxisLabelPoint[barItemArrIndex].x - sumWidth / 2\n\n      barItemArr.forEach((barItem, barIndex) => {\n        x += barItem[0].barGap + barItem[0].barWidth / 2\n\n        let yPositive = 0\n        let yNagative = 0\n\n        if (maxData >= 0 && minData >= 0) {\n          yPositive = yStart\n        } else if (maxData <= 0 && minData <= 0) {\n          yNagative = yEnd\n        } else {\n          yPositive = yZero\n          yNagative = yZero\n        }\n\n        barItem.forEach((seriesItem, seriesIndex) => {\n          seriesItem.x = x\n\n          // 记录y坐标点和柱体高度\n          let barHeight = 0\n\n          if (maxData >= 0 && minData >= 0) {\n            if (seriesItem.data == 0) {\n              seriesItem.y = yStart\n              barHeight = 0\n            } else {\n              seriesItem.y = yPositive\n              barHeight = (valueAxisSpacing * (seriesItem.data - minData)) / dataRange\n              yPositive -= barHeight\n            }\n          } else if (maxData <= 0 && minData <= 0) {\n            if (seriesItem.data == 0) {\n              seriesItem.y = yEnd\n              barHeight = 0\n            } else {\n              seriesItem.y = yNagative\n              barHeight = (valueAxisSpacing * (Math.abs(seriesItem.data) - Math.abs(maxData))) / dataRange\n              yNagative += barHeight\n            }\n          } else {\n            if (seriesItem.data > 0) {\n              seriesItem.y = yPositive\n              barHeight = (valueAxisPlusSpacing * seriesItem.data) / maxData\n              yPositive -= barHeight\n            } else if (seriesItem.data < 0) {\n              seriesItem.y = yNagative\n              barHeight = (valueAxisMinusSpacing * Math.abs(seriesItem.data)) / Math.abs(minData)\n              yNagative += barHeight\n            } else {\n              seriesItem.y = yZero\n              barHeight = 0\n            }\n          }\n\n          seriesItem.barHeight = barHeight\n        })\n\n        x += barItem[0].barWidth / 2\n      })\n    })\n  } else {\n    chartBar.forEach((barItemArr, barItemArrIndex) => {\n      let y = yAxisLabelPoint[barItemArrIndex].y + sumWidth / 2\n\n      barItemArr.forEach((barItem, barIndex) => {\n        y -= barItem[0].barGap + barItem[0].barWidth / 2\n\n        let xPositive = 0\n        let xNagative = 0\n\n        if (maxData >= 0 && minData >= 0) {\n          xPositive = xStart\n        } else if (maxData <= 0 && minData <= 0) {\n          xNagative = xEnd\n        } else {\n          xPositive = xZero\n          xNagative = xZero\n        }\n\n        barItem.forEach((seriesItem, seriesIndex) => {\n          seriesItem.y = y\n\n          // 记录y坐标点和柱体高度\n          let barHeight = 0\n\n          if (maxData >= 0 && minData >= 0) {\n            seriesItem.x = xPositive\n            barHeight = (valueAxisSpacing * (seriesItem.data - minData)) / dataRange\n            xPositive += barHeight\n          } else if (maxData <= 0 && minData <= 0) {\n            seriesItem.x = xNagative\n            barHeight = (valueAxisSpacing * (Math.abs(seriesItem.data) - Math.abs(maxData))) / dataRange\n            xNagative -= barHeight\n          } else {\n            if (seriesItem.data > 0) {\n              seriesItem.x = xPositive\n              barHeight = (valueAxisPlusSpacing * seriesItem.data) / maxData\n              xPositive += barHeight\n            } else {\n              seriesItem.x = xNagative\n              barHeight = (valueAxisMinusSpacing * Math.abs(seriesItem.data)) / Math.abs(minData)\n              xNagative -= barHeight\n            }\n          }\n\n          seriesItem.barHeight = barHeight\n        })\n\n        y -= barItem[0].barWidth / 2\n      })\n    })\n  }\n\n  chartData.chartBar = chartBar\n\n  console.log('complete calChartBarData', chartData.chartBar)\n}\n", "import cloneDeep from 'lodash.clonedeep'\n\nexport default function calChartLineData() {\n  let { opts, chartData, seriesMap } = this\n  let { xAxis } = opts\n  let seriesLine = cloneDeep(seriesMap['line'])\n\n  let {\n    xStart,\n    xEnd,\n    yStart,\n    yEnd,\n    yZero,\n    yPlusSpacing,\n    yMinusSpacing,\n    ySpacing,\n    xZero,\n    xPlusSpacing,\n    xMinusSpacing,\n    xSpacing,\n    yMaxData,\n    yMinData,\n    yDataRange,\n    xMaxData,\n    xMinData,\n    xDataRange,\n    xAxisLabelPoint,\n    yAxisLabelPoint,\n  } = chartData.axisData\n\n  let maxData = xAxis.type == 'value' ? xMaxData : yMaxData\n  let minData = xAxis.type == 'value' ? xMinData : yMinData\n  let dataRange = xAxis.type == 'value' ? xDataRange : yDataRange\n  let valueAxisPlusSpacing = xAxis.type == 'value' ? xPlusSpacing : yPlusSpacing\n  let valueAxisMinusSpacing = xAxis.type == 'value' ? xMinusSpacing : yMinusSpacing\n  let valueAxisSpacing = xAxis.type == 'value' ? xSpacing : ySpacing\n\n  let chartLine = []\n\n  if (xAxis.type == 'category') {\n    chartLine = seriesLine.reduce((chartLineArr, seriesItem) => {\n      seriesItem.data = seriesItem.data.reduce((dataArr, dataItem, dataIndex) => {\n        let x, y, height\n        if (typeof dataItem == 'number') {\n          x = xAxisLabelPoint[dataIndex].x\n\n          dataItem = dataItem > maxData ? maxData : dataItem\n          dataItem = dataItem < minData ? minData : dataItem\n\n          if (maxData >= 0 && minData >= 0) {\n            height = (valueAxisSpacing * (dataItem - minData)) / dataRange\n            y = yStart - height\n          } else if (maxData <= 0 && minData <= 0) {\n            height = (valueAxisSpacing * (Math.abs(dataItem) - Math.abs(maxData))) / dataRange\n            y = yEnd + height\n          } else {\n            if (dataItem > 0) {\n              height = (valueAxisPlusSpacing * dataItem) / maxData\n              y = yZero - height\n            } else {\n              height = (valueAxisMinusSpacing * Math.abs(dataItem)) / Math.abs(minData)\n              y = yZero + height\n            }\n          }\n        }\n\n        dataArr.push({\n          x,\n          y,\n          data: dataItem,\n          height,\n        })\n        return dataArr\n      }, [])\n\n      chartLineArr.push(seriesItem)\n\n      return chartLineArr\n    }, [])\n  } else {\n    chartLine = seriesLine.reduce((chartLineArr, seriesItem) => {\n      seriesItem.data = seriesItem.data.reduce((dataArr, dataItem, dataIndex) => {\n        let x, y, height\n\n        if (typeof dataItem == 'number') {\n          y = yAxisLabelPoint[dataIndex].y\n\n          dataItem = dataItem > maxData ? maxData : dataItem\n          dataItem = dataItem < minData ? minData : dataItem\n\n          if (maxData >= 0 && minData >= 0) {\n            height = (valueAxisSpacing * (dataItem - minData)) / dataRange\n            x = xStart + height\n          } else if (maxData <= 0 && minData <= 0) {\n            height = (valueAxisSpacing * (Math.abs(dataItem) - Math.abs(maxData))) / dataRange\n            x = xEnd - height\n          } else {\n            if (dataItem > 0) {\n              height = (valueAxisPlusSpacing * dataItem) / maxData\n              x = xZero + height\n            } else {\n              height = (valueAxisMinusSpacing * Math.abs(dataItem)) / Math.abs(minData)\n              x = xZero - height\n            }\n          }\n        }\n\n        dataArr.push({\n          x,\n          y,\n          data: dataItem,\n          height,\n        })\n        return dataArr\n      }, [])\n\n      chartLineArr.push(seriesItem)\n\n      return chartLineArr\n    }, [])\n  }\n\n  chartData.chartLine = chartLine\n\n  console.log('complete calChartLineData', chartData.chartLine)\n}\n", "import { percentToNum } from '../util/util'\nimport cloneDeep from 'lodash.clonedeep'\n\nexport default function calChartPieData() {\n  let { opts, legendData, seriesMap } = this\n  let { width, height, padding } = opts\n  let chartPie = cloneDeep(seriesMap['pie'][0])\n  let { data, center, radius } = chartPie\n  let [centerX, centerY] = center\n  let [radius1, radius2] = radius\n\n  let valueSum = data.reduce((sum, dataItem) => {\n    sum += dataItem.value === null ? 0 : dataItem.value\n    return sum\n  }, 0)\n\n  if (typeof centerX == 'string') {\n    centerX = width * percentToNum(centerX)\n  }\n  if (typeof centerY == 'string') {\n    centerY = (height - legendData.legendHeight - padding[2]) * percentToNum(centerY)\n  }\n  if (typeof radius1 == 'string') {\n    radius1 = ((height - legendData.legendHeight - padding[2]) * percentToNum(radius1)) / 2\n  }\n  if (typeof radius2 == 'string') {\n    radius2 = ((height - legendData.legendHeight - padding[2]) * percentToNum(radius2)) / 2\n  }\n\n  let sortData = data.concat([]).sort((a, b) => {\n    return b.value - a.value\n  })\n\n  chartPie.valueSum = valueSum\n  chartPie.center = [centerX, centerY]\n  chartPie.radius = [radius1, radius2]\n  chartPie.maxData = sortData[0].value\n  chartPie.minData = sortData[sortData.length - 1].value\n\n  this.chartData.chartPie = chartPie\n\n  console.log('complete calChartPieData', this.chartData.chartPie)\n}\n", "import { convertCoordinateOrigin } from '../util/util'\nimport cloneDeep from 'lodash.clonedeep'\n\nexport default function calChartRadarData() {\n  let { opts, chartData, seriesMap } = this\n  let { radarAxis, categories, series } = opts\n  let seriesRadar = cloneDeep(seriesMap['radar'])\n\n  let { max } = radarAxis\n  let { radius } = chartData.radarAxis\n\n  let maxData = 0\n  series.forEach(seriesItem => {\n    maxData = Math.max(maxData, ...seriesItem.data)\n  })\n  maxData = max == 'auto' ? maxData : max\n\n  let spacingRadian = (2 * Math.PI) / categories.length\n  let start = Math.PI / 2 // 以90度为起点, 逆时针累加\n\n  seriesRadar.forEach(radarItem => {\n    radarItem.dataPosition = radarItem.data.reduce((arr, dataItem, dataIndex) => {\n      let scale = dataItem / maxData\n      let point = {\n        x: radius * Math.cos(start + spacingRadian * dataIndex) * scale,\n        y: radius * Math.sin(start + spacingRadian * dataIndex) * scale,\n      }\n      arr.push({\n        data: dataItem,\n        point,\n        spacingRadian,\n        _start_: spacingRadian * dataIndex,\n      })\n\n      return arr\n    }, [])\n  })\n\n  chartData.chartRadar = seriesRadar\n\n  console.log('complete calChartRadarData', this.chartData.chartRadar)\n}\n", "import { HEX2HSL, HSL2HEX } from '../util/util'\nimport cloneDeep from 'lodash.clonedeep'\n\nexport default function calChartScatterData() {\n  let { opts, chartData, seriesMap } = this\n  let {\n    xStart,\n    xEnd,\n    yStart,\n    yEnd,\n    yZero,\n    yPlusSpacing,\n    yMinusSpacing,\n    ySpacing,\n    xZero,\n    xPlusSpacing,\n    xMinusSpacing,\n    xSpacing,\n    yMaxData,\n    yMinData,\n    yDataRange,\n    xMaxData,\n    xMinData,\n    xDataRange,\n  } = chartData.axisData\n\n  let seriesScatter = cloneDeep(seriesMap['scatter'])\n\n  chartData.chartScatter = seriesScatter.map(seriesItem => {\n    let { data, radius, itemStyle } = seriesItem\n    let { color: scatterItemColor } = itemStyle\n    let radiusMax, radiusMin, radiusRange\n    let zMax, zMin, zRange\n    let HSLColorMax, HSLColorMin, HSLColorRange\n\n    if (typeof radius !== 'number') {\n      radiusMax = radius[1]\n      radiusMin = radius[0]\n      radiusRange = radiusMax - radiusMin\n\n      let sortData = data.concat([]).sort((a, b) => {\n        return a.z - b.z\n      })\n      zMax = sortData[sortData.length - 1].z\n      zMin = sortData[0].z ? sortData[0].z : 0\n      zRange = zMax - zMin\n    }\n\n    if (typeof scatterItemColor !== 'string') {\n      let [HEXColorMin, HEXColorMax] = scatterItemColor\n      HSLColorMax = HEX2HSL(HEXColorMax)\n      HSLColorMin = HEX2HSL(HEXColorMin)\n      HSLColorRange = [HSLColorMax[0] - HSLColorMin[0], HSLColorMax[1] - HSLColorMin[1], HSLColorMax[2] - HSLColorMin[2]]\n\n      seriesItem.label.color = '#000000'\n    }\n\n    seriesItem.data = data.concat([]).map(dataItem => {\n      let { x, y, z } = dataItem\n      let positionX, positionY\n      let _radius, _color\n\n      if (yMaxData >= 0 && yMaxData >= 0) {\n        positionY = yStart - (ySpacing * (y - yMinData)) / yDataRange\n      } else if (yMinData <= 0 && yMinData <= 0) {\n        positionY = yEnd + (ySpacing * (Math.abs(y) - Math.abs(yMaxData))) / yDataRange\n      } else {\n        if (y > 0) {\n          positionY = yZero - (yPlusSpacing * y) / yMaxData\n        } else {\n          positionY = yZero + (yMinusSpacing * Math.abs(y)) / Math.abs(yMinData)\n        }\n      }\n\n      if (xMaxData >= 0 && xMaxData >= 0) {\n        positionX = xStart + (xSpacing * (x - xMinData)) / xDataRange\n      } else if (xMinData <= 0 && xMinData <= 0) {\n        positionX = xEnd - (xSpacing * (Math.abs(x) - Math.abs(xMaxData))) / xDataRange\n      } else {\n        if (x > 0) {\n          positionX = xZero + (xPlusSpacing * x) / xMaxData\n        } else {\n          positionX = xZero - (xMinusSpacing * Math.abs(x)) / Math.abs(xMinData)\n        }\n      }\n      dataItem.positionX = positionX\n      dataItem.positionY = positionY\n\n      if (typeof radius !== 'number') {\n        dataItem.z = z ? z : 0\n        let scale = (z - zMin) / zRange\n        dataItem.radius = radiusMin + radiusRange * scale\n      } else {\n        dataItem.radius = radius\n      }\n\n      if (typeof scatterItemColor !== 'string') {\n        dataItem.z = z ? z : 0\n        let scale = (z - zMin) / zRange\n        let HSLColor = [HSLColorMin[0] + HSLColorRange[0] * scale, HSLColorMin[1] + HSLColorRange[1] * scale, HSLColorMin[2] + HSLColorRange[2] * scale]\n\n        dataItem.color = HSL2HEX(HSLColor)\n      } else {\n        dataItem.color = scatterItemColor\n      }\n\n      return dataItem\n    })\n\n    return seriesItem\n  })\n\n  console.log('complete calChartScatterData', this.chartData.chartScatter)\n}\n", "import { percentToNum } from '../util/util'\n\nexport default function calChartPieData() {\n  let { opts, legendData, chartData, seriesMap } = this\n  let { width, height, series, padding } = opts\n\n  let chartFunnel = seriesMap['funnel'][0]\n  let { data, width: funnelWidth, height: funnelHeight, top, left, right, bottom, max, min, gap, sort, shape, funnelAlign, label, itemStyle } = chartFunnel\n  let { borderWidth } = itemStyle\n  let xStart = padding[3]\n  let xEnd = width - padding[1]\n  let yStart = padding[0]\n  let yEnd = height - padding[2] - legendData.legendHeight\n  let containerWidth = xEnd - xStart\n  let containerHeight = yEnd - yStart\n\n  max = max > 100 ? 100 : max\n  min = min < 0 ? 0 : min\n\n  xStart = left == 'auto' ? xStart : xStart + containerWidth * percentToNum(left)\n  xEnd = right == 'auto' ? xEnd : xEnd - containerWidth * percentToNum(right)\n  yStart = top == 'auto' ? yStart : yStart + containerHeight * percentToNum(top)\n  yEnd = bottom == 'auto' ? yEnd : yEnd - containerHeight * percentToNum(bottom)\n  containerWidth = funnelWidth == 'auto' ? xEnd - xStart : containerWidth * percentToNum(funnelWidth)\n  containerHeight = funnelHeight == 'auto' ? yEnd - yStart : containerHeight * percentToNum(funnelHeight)\n\n  let funnelItemHeight = (containerHeight - (data.length - 1) * gap) / data.length\n\n  data.forEach(dataItem => {\n    dataItem.value = dataItem.value > max ? max : dataItem.value\n    dataItem.value = dataItem.value < min ? min : dataItem.value\n\n    dataItem.width = containerWidth * (dataItem.value / max)\n    dataItem.height = funnelItemHeight\n  })\n\n  let pointX, pointY\n  if (funnelAlign == 'left') {\n    pointX = xStart\n    pointY = yStart\n  } else if (funnelAlign == 'right') {\n    pointX = xEnd\n    pointY = yStart\n  } else {\n    if (sort == 'ascending') {\n      pointX = xStart + data[data.length - 1].width / 2 - data[0].width / 2\n    } else {\n      pointX = xStart\n    }\n    pointY = yStart\n  }\n\n  data.forEach((dataItem, dataIndex) => {\n    let point = []\n\n    if (funnelAlign == 'left') {\n      if (sort == 'descending') {\n        if (dataIndex + 1 == data.length) {\n          if (shape == 'funnel') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          } else if (shape == 'pyramid') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          }\n        } else {\n          point.push({ x: pointX, y: pointY })\n          point.push({ x: pointX + dataItem.width, y: pointY })\n          point.push({ x: pointX + data[dataIndex + 1].width, y: pointY + dataItem.height })\n          point.push({ x: pointX, y: pointY + dataItem.height })\n        }\n      } else if (sort == 'ascending') {\n        if (dataIndex == 0) {\n          if (shape == 'funnel') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          } else if (shape == 'pyramid') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          }\n        } else {\n          point.push({ x: pointX, y: pointY })\n          point.push({ x: pointX + data[dataIndex - 1].width, y: pointY })\n          point.push({ x: pointX + dataItem.width, y: pointY + dataItem.height })\n          point.push({ x: pointX, y: pointY + dataItem.height })\n        }\n      }\n    } else if (funnelAlign == 'right') {\n      if (sort == 'descending') {\n        if (dataIndex + 1 == data.length) {\n          if (shape == 'funnel') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX - dataItem.width, y: pointY })\n            point.push({ x: pointX - dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          } else if (shape == 'pyramid') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX - dataItem.width, y: pointY })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          }\n        } else {\n          point.push({ x: pointX, y: pointY })\n          point.push({ x: pointX - dataItem.width, y: pointY })\n          point.push({ x: pointX - data[dataIndex + 1].width, y: pointY + dataItem.height })\n          point.push({ x: pointX, y: pointY + dataItem.height })\n        }\n      } else if (sort == 'ascending') {\n        if (dataIndex == 0) {\n          if (shape == 'funnel') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX - dataItem.width, y: pointY })\n            point.push({ x: pointX - dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          } else if (shape == 'pyramid') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX - dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          }\n        } else {\n          point.push({ x: pointX, y: pointY })\n          point.push({ x: pointX - data[dataIndex - 1].width, y: pointY })\n          point.push({ x: pointX - dataItem.width, y: pointY + dataItem.height })\n          point.push({ x: pointX, y: pointY + dataItem.height })\n        }\n      }\n    } else {\n      if (sort == 'descending') {\n        if (dataIndex + 1 == data.length) {\n          if (shape == 'funnel') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          } else if (shape == 'pyramid') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY })\n            point.push({ x: pointX + dataItem.width / 2, y: pointY + dataItem.height })\n          }\n        } else {\n          point.push({ x: pointX, y: pointY })\n          point.push({ x: pointX + dataItem.width, y: pointY })\n          point.push({ x: pointX + dataItem.width / 2 + data[dataIndex + 1].width / 2, y: pointY + dataItem.height })\n          point.push({ x: pointX + dataItem.width / 2 - data[dataIndex + 1].width / 2, y: pointY + dataItem.height })\n        }\n      } else if (sort == 'ascending') {\n        if (dataIndex == 0) {\n          if (shape == 'funnel') {\n            point.push({ x: pointX, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          } else if (shape == 'pyramid') {\n            point.push({ x: pointX + dataItem.width / 2, y: pointY })\n            point.push({ x: pointX + dataItem.width, y: pointY + dataItem.height })\n            point.push({ x: pointX, y: pointY + dataItem.height })\n          }\n        } else {\n          point.push({ x: pointX + dataItem.width / 2 - data[dataIndex - 1].width / 2, y: pointY })\n          point.push({ x: pointX + dataItem.width / 2 + data[dataIndex - 1].width / 2, y: pointY })\n          point.push({ x: pointX + dataItem.width, y: pointY + dataItem.height })\n          point.push({ x: pointX, y: pointY + dataItem.height })\n        }\n      }\n    }\n\n    dataItem.point = point\n\n    let medianWidth\n    if (sort == 'descending') {\n      if (dataIndex + 1 == data.length) {\n        medianWidth = shape == 'funnel' ? dataItem.width : dataItem.width / 2\n      } else {\n        medianWidth = (dataItem.width + data[dataIndex + 1].width) / 2\n      }\n    } else {\n      if (dataIndex == 0) {\n        medianWidth = shape == 'funnel' ? dataItem.width : dataItem.width / 2\n      } else {\n        medianWidth = (dataItem.width + data[dataIndex - 1].width) / 2\n      }\n    }\n\n    if (label.position == 'inside') {\n      if (funnelAlign == 'left') {\n        dataItem.textPoint = { x: pointX + medianWidth / 2, y: pointY + dataItem.height / 2 }\n      } else if (funnelAlign == 'right') {\n        dataItem.textPoint = { x: pointX - medianWidth / 2, y: pointY + dataItem.height / 2 }\n      } else {\n        dataItem.textPoint = { x: pointX + dataItem.width / 2, y: pointY + dataItem.height / 2 }\n      }\n    } else {\n      if (funnelAlign == 'left') {\n        dataItem.textPoint = { x: pointX + medianWidth + label.margin, y: pointY + dataItem.height / 2 }\n      } else if (funnelAlign == 'right') {\n        dataItem.textPoint = { x: pointX - medianWidth - label.margin, y: pointY + dataItem.height / 2 }\n      } else {\n        dataItem.textPoint = { x: pointX + dataItem.width / 2 + medianWidth / 2 + label.margin, y: pointY + dataItem.height / 2 }\n      }\n    }\n\n    if (dataIndex + 1 !== data.length) {\n      if (funnelAlign == 'center') {\n        pointX = pointX + dataItem.width / 2 - data[dataIndex + 1].width / 2\n        pointY = pointY + dataItem.height + gap\n      } else {\n        pointY = pointY + dataItem.height + gap\n      }\n    }\n  })\n\n  chartData.chartFunnel = chartFunnel\n\n  console.log('complete calChartFunnelData', this.chartData.chartFunnel)\n}\n", "export default function calChartCandlestick() {\n  let { opts, chartData, legendData } = this\n  let { height: chartHeight, padding, series } = opts\n  let candlestickSeries = series.filter(seriesItem => {\n    return seriesItem.type == 'candlestick' || seriesItem.type == 'k'\n  })\n\n  if (candlestickSeries.length == 0) return\n\n  let {\n    name,\n    type,\n    data,\n    barMaxWidth,\n    barMinWidth,\n    barWidth,\n    itemStyle,\n    highLine: seriesHighLine,\n    lowLine: seriesLowLine,\n    bar: seriesBar,\n  } = candlestickSeries[0]\n  let { color, bordercolor, opacity, color0, bordercolor0, opacity0, borderWidth } = itemStyle\n  let { show: highLineShow, lineStyle: highLineStyle } = seriesHighLine\n  let { show: lowLineShow, lineStyle: lowLineStyle } = seriesLowLine\n  let { show: barShow, height: barHeight, margin: barMargin, data: barData, lineStyle: barLineStyle } = seriesBar\n  let {\n    xStart,\n    xEnd,\n    yStart,\n    yEnd,\n    yZero,\n    yPlusSpacing,\n    yMinusSpacing,\n    ySpacing,\n    xZero,\n    xPlusSpacing,\n    xMinusSpacing,\n    xEachSpacing,\n    xSpacing,\n    yMaxData,\n    yMinData,\n    yDataRange,\n    xMaxData,\n    xMinData,\n    xDataRange,\n    xAxisLabelPoint,\n    yAxisLabelPoint,\n  } = chartData.axisData\n\n  let candlestickRect, candlestickHighLine, candlestickLowLine, candlestickBar\n  let highData = 0\n  let lowData = Infinity\n\n  if (barWidth == 'auto') {\n    barWidth = xEachSpacing > barMaxWidth ? barMaxWidth : xEachSpacing\n    barWidth = barWidth > 3 ? barWidth - 2 : barWidth\n  }\n\n  candlestickRect = data.reduce((chartCandlestick, dataItem, dataIndex) => {\n    const [\n      start, // 开盘价\n      end, // 收盘价\n      low, // 最低价\n      high, // 最高价\n      volumn, // 交易量\n    ] = dataItem\n    highData = Math.max(end, highData)\n    lowData = Math.min(end, lowData)\n\n    let candlestickItem = {}\n\n    candlestickItem.start = start\n    candlestickItem.end = end\n    candlestickItem.low = low\n    candlestickItem.high = high\n    candlestickItem.volumn = volumn\n    candlestickItem.color = start > end ? color0 : color\n    candlestickItem.bordercolor = start > end ? bordercolor0 : bordercolor\n    candlestickItem.opacity = start > end ? opacity : opacity0\n    candlestickItem.borderWidth = borderWidth\n\n    candlestickItem.upLinePoint = {\n      startX: xAxisLabelPoint[dataIndex].x,\n      startY: yStart - (ySpacing * (low - yMinData)) / yDataRange,\n      endX: xAxisLabelPoint[dataIndex].x,\n      endY: yStart - (ySpacing * (Math.max(start, end) - yMinData)) / yDataRange,\n    }\n    candlestickItem.downLinePoint = {\n      startX: xAxisLabelPoint[dataIndex].x,\n      startY: yStart - (ySpacing * (high - yMinData)) / yDataRange,\n      endX: xAxisLabelPoint[dataIndex].x,\n      endY: yStart - (ySpacing * (Math.min(start, end) - yMinData)) / yDataRange,\n    }\n    candlestickItem.rectPoint = {\n      x: Math.floor(xAxisLabelPoint[dataIndex].x - barWidth / 2),\n      y: yStart - (ySpacing * (Math.max(start, end) - yMinData)) / yDataRange,\n      width: barWidth,\n      height: (ySpacing * Math.abs(start - end)) / yDataRange,\n    }\n    chartCandlestick.push(candlestickItem)\n    return chartCandlestick\n  }, [])\n\n  if (highLineShow) {\n    candlestickHighLine = {\n      data: highData,\n      startX: xStart,\n      startY: yStart - (ySpacing * (highData - yMinData)) / yDataRange,\n      endX: xEnd,\n      endY: yStart - (ySpacing * (highData - yMinData)) / yDataRange,\n    }\n  }\n\n  if (lowLineShow) {\n    candlestickLowLine = {\n      data: lowData,\n      startX: xStart,\n      startY: yStart - (ySpacing * (lowData - yMinData)) / yDataRange,\n      endX: xEnd,\n      endY: yStart - (ySpacing * (lowData - yMinData)) / yDataRange,\n    }\n  }\n\n  if (barShow) {\n    let maxData = Math.max(...barData)\n    let minData = Math.min(...barData)\n    let range = maxData - minData\n\n    candlestickBar = {\n      data: [],\n      lineStartX: xStart,\n      lineStartY: null,\n      lineEndX: xEnd,\n      lineEndY: null,\n    }\n    candlestickBar.data = candlestickRect.reduce((arr, item, index) => {\n      let { color, rectPoint } = item\n      let { x, width } = rectPoint\n      let y = chartHeight - padding[2] - legendData.legendHeight - barLineStyle.lineWidth / 2\n      let height = barHeight * 0.2 + (barHeight * 0.8 * (barData[index] - minData)) / range\n\n      arr.push({\n        color,\n        x,\n        y,\n        width,\n        height,\n      })\n\n      candlestickBar.lineStartY = y\n      candlestickBar.lineEndY = y\n\n      return arr\n    }, [])\n  }\n\n  this.chartData.chartCandlestick = {\n    type,\n    name,\n    rect: candlestickRect,\n    highLine: candlestickHighLine,\n    lowhLine: candlestickLowLine,\n    bar: candlestickBar,\n  }\n\n  console.log('complete calChartCandlestick', this.chartData.chartCandlestick)\n}\n", "import { HEX2HSL, HSL2HEX } from '../util/util'\nimport cloneDeep from 'lodash.clonedeep'\n\nexport default function calChartHeatmapData() {\n  let { chartData, seriesMap } = this\n  let { xStart, yStart, xEachSpacing, yEachSpacing } = chartData.axisData\n\n  let seriesHeatmap = cloneDeep(seriesMap['heatmap'])\n\n  chartData.chartHeatmap = seriesHeatmap.map(seriesItem => {\n    let { data, itemStyle } = seriesItem\n    let { color, useSplit } = itemStyle\n    let zMax, zMin, zRange\n    let HSLColorMax, HSLColorMin, HSLColorRange\n\n    let sortData = data.concat([]).sort((a, b) => {\n      return a[2] - b[2]\n    })\n    zMax = sortData[sortData.length - 1][2]\n    zMin = sortData[0][2]\n    zRange = zMax - zMin\n\n    let [HEXColorMin, HEXColorMax] = color\n    HSLColorMax = HEX2HSL(HEXColorMax)\n    HSLColorMin = HEX2HSL(HEXColorMin)\n    HSLColorRange = [HSLColorMax[0] - HSLColorMin[0], HSLColorMax[1] - HSLColorMin[1], HSLColorMax[2] - HSLColorMin[2]]\n\n    seriesItem.data = data.map(dataItem => {\n      let [x, y, z] = dataItem\n      let positionX, positionY\n\n      positionX = xStart + x * xEachSpacing\n      positionY = yStart - (y + 1) * yEachSpacing\n      dataItem.positionX = positionX\n      dataItem.positionY = positionY\n\n      z = z ? z : 0\n      let scale = (z - zMin) / zRange\n      let HSLColor = [HSLColorMin[0] + HSLColorRange[0] * scale, HSLColorMin[1] + HSLColorRange[1] * scale, HSLColorMin[2] + HSLColorRange[2] * scale]\n      dataItem.color = HSL2HEX(HSLColor)\n      dataItem.useSplit = useSplit\n      return dataItem\n    })\n\n    return seriesItem\n  })\n  console.log('complete calChartHeatmapData', this.chartData.chartHeatmap)\n}\n", "function defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\nfunction meanX(children) {\n  return children.reduce(meanXReduce, 0) / children.length;\n}\n\nfunction meanXReduce(x, c) {\n  return x + c.x;\n}\n\nfunction maxY(children) {\n  return 1 + children.reduce(maxYReduce, 0);\n}\n\nfunction maxYReduce(y, c) {\n  return Math.max(y, c.y);\n}\n\nfunction leafLeft(node) {\n  var children;\n  while (children = node.children) node = children[0];\n  return node;\n}\n\nfunction leafRight(node) {\n  var children;\n  while (children = node.children) node = children[children.length - 1];\n  return node;\n}\n\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = false;\n\n  function cluster(root) {\n    var previousNode,\n        x = 0;\n\n    // First walk, computing the initial x & y values.\n    root.eachAfter(function(node) {\n      var children = node.children;\n      if (children) {\n        node.x = meanX(children);\n        node.y = maxY(children);\n      } else {\n        node.x = previousNode ? x += separation(node, previousNode) : 0;\n        node.y = 0;\n        previousNode = node;\n      }\n    });\n\n    var left = leafLeft(root),\n        right = leafRight(root),\n        x0 = left.x - separation(left, right) / 2,\n        x1 = right.x + separation(right, left) / 2;\n\n    // Second walk, normalizing x & y to the desired size.\n    return root.eachAfter(nodeSize ? function(node) {\n      node.x = (node.x - root.x) * dx;\n      node.y = (root.y - node.y) * dy;\n    } : function(node) {\n      node.x = (node.x - x0) / (x1 - x0) * dx;\n      node.y = (1 - (root.y ? node.y / root.y : 1)) * dy;\n    });\n  }\n\n  cluster.separation = function(x) {\n    return arguments.length ? (separation = x, cluster) : separation;\n  };\n\n  cluster.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], cluster) : (nodeSize ? null : [dx, dy]);\n  };\n\n  cluster.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], cluster) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return cluster;\n}\n", "function count(node) {\n  var sum = 0,\n      children = node.children,\n      i = children && children.length;\n  if (!i) sum = 1;\n  else while (--i >= 0) sum += children[i].value;\n  node.value = sum;\n}\n\nexport default function() {\n  return this.eachAfter(count);\n}\n", "import node_count from \"./count.js\";\nimport node_each from \"./each.js\";\nimport node_eachBefore from \"./eachBefore.js\";\nimport node_eachAfter from \"./eachAfter.js\";\nimport node_sum from \"./sum.js\";\nimport node_sort from \"./sort.js\";\nimport node_path from \"./path.js\";\nimport node_ancestors from \"./ancestors.js\";\nimport node_descendants from \"./descendants.js\";\nimport node_leaves from \"./leaves.js\";\nimport node_links from \"./links.js\";\n\nexport default function hierarchy(data, children) {\n  var root = new Node(data),\n      valued = +data.value && (root.value = data.value),\n      node,\n      nodes = [root],\n      child,\n      childs,\n      i,\n      n;\n\n  if (children == null) children = defaultChildren;\n\n  while (node = nodes.pop()) {\n    if (valued) node.value = +node.data.value;\n    if ((childs = children(node.data)) && (n = childs.length)) {\n      node.children = new Array(n);\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = node.children[i] = new Node(childs[i]));\n        child.parent = node;\n        child.depth = node.depth + 1;\n      }\n    }\n  }\n\n  return root.eachBefore(computeHeight);\n}\n\nfunction node_copy() {\n  return hierarchy(this).eachBefore(copyData);\n}\n\nfunction defaultChildren(d) {\n  return d.children;\n}\n\nfunction copyData(node) {\n  node.data = node.data.data;\n}\n\nexport function computeHeight(node) {\n  var height = 0;\n  do node.height = height;\n  while ((node = node.parent) && (node.height < ++height));\n}\n\nexport function Node(data) {\n  this.data = data;\n  this.depth =\n  this.height = 0;\n  this.parent = null;\n}\n\nNode.prototype = hierarchy.prototype = {\n  constructor: Node,\n  count: node_count,\n  each: node_each,\n  eachAfter: node_eachAfter,\n  eachBefore: node_eachBefore,\n  sum: node_sum,\n  sort: node_sort,\n  path: node_path,\n  ancestors: node_ancestors,\n  descendants: node_descendants,\n  leaves: node_leaves,\n  links: node_links,\n  copy: node_copy\n};\n", "export default function(callback) {\n  var node = this, current, next = [node], children, i, n;\n  do {\n    current = next.reverse(), next = [];\n    while (node = current.pop()) {\n      callback(node), children = node.children;\n      if (children) for (i = 0, n = children.length; i < n; ++i) {\n        next.push(children[i]);\n      }\n    }\n  } while (next.length);\n  return this;\n}\n", "export default function(callback) {\n  var node = this, nodes = [node], next = [], children, i, n;\n  while (node = nodes.pop()) {\n    next.push(node), children = node.children;\n    if (children) for (i = 0, n = children.length; i < n; ++i) {\n      nodes.push(children[i]);\n    }\n  }\n  while (node = next.pop()) {\n    callback(node);\n  }\n  return this;\n}\n", "export default function(callback) {\n  var node = this, nodes = [node], children, i;\n  while (node = nodes.pop()) {\n    callback(node), children = node.children;\n    if (children) for (i = children.length - 1; i >= 0; --i) {\n      nodes.push(children[i]);\n    }\n  }\n  return this;\n}\n", "export default function(value) {\n  return this.eachAfter(function(node) {\n    var sum = +value(node.data) || 0,\n        children = node.children,\n        i = children && children.length;\n    while (--i >= 0) sum += children[i].value;\n    node.value = sum;\n  });\n}\n", "export default function(compare) {\n  return this.eachBefore(function(node) {\n    if (node.children) {\n      node.children.sort(compare);\n    }\n  });\n}\n", "export default function(end) {\n  var start = this,\n      ancestor = leastCommonAncestor(start, end),\n      nodes = [start];\n  while (start !== ancestor) {\n    start = start.parent;\n    nodes.push(start);\n  }\n  var k = nodes.length;\n  while (end !== ancestor) {\n    nodes.splice(k, 0, end);\n    end = end.parent;\n  }\n  return nodes;\n}\n\nfunction leastCommonAncestor(a, b) {\n  if (a === b) return a;\n  var aNodes = a.ancestors(),\n      bNodes = b.ancestors(),\n      c = null;\n  a = aNodes.pop();\n  b = bNodes.pop();\n  while (a === b) {\n    c = a;\n    a = aNodes.pop();\n    b = bNodes.pop();\n  }\n  return c;\n}\n", "export default function() {\n  var node = this, nodes = [node];\n  while (node = node.parent) {\n    nodes.push(node);\n  }\n  return nodes;\n}\n", "export default function() {\n  var nodes = [];\n  this.each(function(node) {\n    nodes.push(node);\n  });\n  return nodes;\n}\n", "export default function() {\n  var leaves = [];\n  this.eachBefore(function(node) {\n    if (!node.children) {\n      leaves.push(node);\n    }\n  });\n  return leaves;\n}\n", "export default function() {\n  var root = this, links = [];\n  root.each(function(node) {\n    if (node !== root) { // Don’t include the root’s parent, if any.\n      links.push({source: node.parent, target: node});\n    }\n  });\n  return links;\n}\n", "export var slice = Array.prototype.slice;\n\nexport function shuffle(array) {\n  var m = array.length,\n      t,\n      i;\n\n  while (m) {\n    i = Math.random() * m-- | 0;\n    t = array[m];\n    array[m] = array[i];\n    array[i] = t;\n  }\n\n  return array;\n}\n", "import {shuffle, slice} from \"../array.js\";\n\nexport default function(circles) {\n  var i = 0, n = (circles = shuffle(slice.call(circles))).length, B = [], p, e;\n\n  while (i < n) {\n    p = circles[i];\n    if (e && enclosesWeak(e, p)) ++i;\n    else e = encloseBasis(B = extendBasis(B, p)), i = 0;\n  }\n\n  return e;\n}\n\nfunction extendBasis(B, p) {\n  var i, j;\n\n  if (enclosesWeakAll(p, B)) return [p];\n\n  // If we get here then B must have at least one element.\n  for (i = 0; i < B.length; ++i) {\n    if (enclosesNot(p, B[i])\n        && enclosesWeakAll(encloseBasis2(B[i], p), B)) {\n      return [B[i], p];\n    }\n  }\n\n  // If we get here then B must have at least two elements.\n  for (i = 0; i < B.length - 1; ++i) {\n    for (j = i + 1; j < B.length; ++j) {\n      if (enclosesNot(encloseBasis2(B[i], B[j]), p)\n          && enclosesNot(encloseBasis2(B[i], p), B[j])\n          && enclosesNot(encloseBasis2(B[j], p), B[i])\n          && enclosesWeakAll(encloseBasis3(B[i], B[j], p), B)) {\n        return [B[i], B[j], p];\n      }\n    }\n  }\n\n  // If we get here then something is very wrong.\n  throw new Error;\n}\n\nfunction enclosesNot(a, b) {\n  var dr = a.r - b.r, dx = b.x - a.x, dy = b.y - a.y;\n  return dr < 0 || dr * dr < dx * dx + dy * dy;\n}\n\nfunction enclosesWeak(a, b) {\n  var dr = a.r - b.r + 1e-6, dx = b.x - a.x, dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\n\nfunction enclosesWeakAll(a, B) {\n  for (var i = 0; i < B.length; ++i) {\n    if (!enclosesWeak(a, B[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction encloseBasis(B) {\n  switch (B.length) {\n    case 1: return encloseBasis1(B[0]);\n    case 2: return encloseBasis2(B[0], B[1]);\n    case 3: return encloseBasis3(B[0], B[1], B[2]);\n  }\n}\n\nfunction encloseBasis1(a) {\n  return {\n    x: a.x,\n    y: a.y,\n    r: a.r\n  };\n}\n\nfunction encloseBasis2(a, b) {\n  var x1 = a.x, y1 = a.y, r1 = a.r,\n      x2 = b.x, y2 = b.y, r2 = b.r,\n      x21 = x2 - x1, y21 = y2 - y1, r21 = r2 - r1,\n      l = Math.sqrt(x21 * x21 + y21 * y21);\n  return {\n    x: (x1 + x2 + x21 / l * r21) / 2,\n    y: (y1 + y2 + y21 / l * r21) / 2,\n    r: (l + r1 + r2) / 2\n  };\n}\n\nfunction encloseBasis3(a, b, c) {\n  var x1 = a.x, y1 = a.y, r1 = a.r,\n      x2 = b.x, y2 = b.y, r2 = b.r,\n      x3 = c.x, y3 = c.y, r3 = c.r,\n      a2 = x1 - x2,\n      a3 = x1 - x3,\n      b2 = y1 - y2,\n      b3 = y1 - y3,\n      c2 = r2 - r1,\n      c3 = r3 - r1,\n      d1 = x1 * x1 + y1 * y1 - r1 * r1,\n      d2 = d1 - x2 * x2 - y2 * y2 + r2 * r2,\n      d3 = d1 - x3 * x3 - y3 * y3 + r3 * r3,\n      ab = a3 * b2 - a2 * b3,\n      xa = (b2 * d3 - b3 * d2) / (ab * 2) - x1,\n      xb = (b3 * c2 - b2 * c3) / ab,\n      ya = (a3 * d2 - a2 * d3) / (ab * 2) - y1,\n      yb = (a2 * c3 - a3 * c2) / ab,\n      A = xb * xb + yb * yb - 1,\n      B = 2 * (r1 + xa * xb + ya * yb),\n      C = xa * xa + ya * ya - r1 * r1,\n      r = -(A ? (B + Math.sqrt(B * B - 4 * A * C)) / (2 * A) : C / B);\n  return {\n    x: x1 + xa + xb * r,\n    y: y1 + ya + yb * r,\n    r: r\n  };\n}\n", "import enclose from \"./enclose.js\";\n\nfunction place(b, a, c) {\n  var dx = b.x - a.x, x, a2,\n      dy = b.y - a.y, y, b2,\n      d2 = dx * dx + dy * dy;\n  if (d2) {\n    a2 = a.r + c.r, a2 *= a2;\n    b2 = b.r + c.r, b2 *= b2;\n    if (a2 > b2) {\n      x = (d2 + b2 - a2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, b2 / d2 - x * x));\n      c.x = b.x - x * dx - y * dy;\n      c.y = b.y - x * dy + y * dx;\n    } else {\n      x = (d2 + a2 - b2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, a2 / d2 - x * x));\n      c.x = a.x + x * dx - y * dy;\n      c.y = a.y + x * dy + y * dx;\n    }\n  } else {\n    c.x = a.x + c.r;\n    c.y = a.y;\n  }\n}\n\nfunction intersects(a, b) {\n  var dr = a.r + b.r - 1e-6, dx = b.x - a.x, dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\n\nfunction score(node) {\n  var a = node._,\n      b = node.next._,\n      ab = a.r + b.r,\n      dx = (a.x * b.r + b.x * a.r) / ab,\n      dy = (a.y * b.r + b.y * a.r) / ab;\n  return dx * dx + dy * dy;\n}\n\nfunction Node(circle) {\n  this._ = circle;\n  this.next = null;\n  this.previous = null;\n}\n\nexport function packEnclose(circles) {\n  if (!(n = circles.length)) return 0;\n\n  var a, b, c, n, aa, ca, i, j, k, sj, sk;\n\n  // Place the first circle.\n  a = circles[0], a.x = 0, a.y = 0;\n  if (!(n > 1)) return a.r;\n\n  // Place the second circle.\n  b = circles[1], a.x = -b.r, b.x = a.r, b.y = 0;\n  if (!(n > 2)) return a.r + b.r;\n\n  // Place the third circle.\n  place(b, a, c = circles[2]);\n\n  // Initialize the front-chain using the first three circles a, b and c.\n  a = new Node(a), b = new Node(b), c = new Node(c);\n  a.next = c.previous = b;\n  b.next = a.previous = c;\n  c.next = b.previous = a;\n\n  // Attempt to place each remaining circle…\n  pack: for (i = 3; i < n; ++i) {\n    place(a._, b._, c = circles[i]), c = new Node(c);\n\n    // Find the closest intersecting circle on the front-chain, if any.\n    // “Closeness” is determined by linear distance along the front-chain.\n    // “Ahead” or “behind” is likewise determined by linear distance.\n    j = b.next, k = a.previous, sj = b._.r, sk = a._.r;\n    do {\n      if (sj <= sk) {\n        if (intersects(j._, c._)) {\n          b = j, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sj += j._.r, j = j.next;\n      } else {\n        if (intersects(k._, c._)) {\n          a = k, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sk += k._.r, k = k.previous;\n      }\n    } while (j !== k.next);\n\n    // Success! Insert the new circle c between a and b.\n    c.previous = a, c.next = b, a.next = b.previous = b = c;\n\n    // Compute the new closest circle pair to the centroid.\n    aa = score(a);\n    while ((c = c.next) !== b) {\n      if ((ca = score(c)) < aa) {\n        a = c, aa = ca;\n      }\n    }\n    b = a.next;\n  }\n\n  // Compute the enclosing circle of the front chain.\n  a = [b._], c = b; while ((c = c.next) !== b) a.push(c._); c = enclose(a);\n\n  // Translate the circles to put the enclosing circle around the origin.\n  for (i = 0; i < n; ++i) a = circles[i], a.x -= c.x, a.y -= c.y;\n\n  return c.r;\n}\n\nexport default function(circles) {\n  packEnclose(circles);\n  return circles;\n}\n", "export function optional(f) {\n  return f == null ? null : required(f);\n}\n\nexport function required(f) {\n  if (typeof f !== \"function\") throw new Error;\n  return f;\n}\n", "export function constantZero() {\n  return 0;\n}\n\nexport default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {packEnclose} from \"./siblings.js\";\nimport {optional} from \"../accessors.js\";\nimport constant, {constant<PERSON>ero} from \"../constant.js\";\n\nfunction defaultRadius(d) {\n  return Math.sqrt(d.value);\n}\n\nexport default function() {\n  var radius = null,\n      dx = 1,\n      dy = 1,\n      padding = constantZero;\n\n  function pack(root) {\n    root.x = dx / 2, root.y = dy / 2;\n    if (radius) {\n      root.eachBefore(radiusLeaf(radius))\n          .eachAfter(packChildren(padding, 0.5))\n          .eachBefore(translateChild(1));\n    } else {\n      root.eachBefore(radiusLeaf(defaultRadius))\n          .eachAfter(packChildren(constantZero, 1))\n          .eachAfter(packChildren(padding, root.r / Math.min(dx, dy)))\n          .eachBefore(translateChild(Math.min(dx, dy) / (2 * root.r)));\n    }\n    return root;\n  }\n\n  pack.radius = function(x) {\n    return arguments.length ? (radius = optional(x), pack) : radius;\n  };\n\n  pack.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], pack) : [dx, dy];\n  };\n\n  pack.padding = function(x) {\n    return arguments.length ? (padding = typeof x === \"function\" ? x : constant(+x), pack) : padding;\n  };\n\n  return pack;\n}\n\nfunction radiusLeaf(radius) {\n  return function(node) {\n    if (!node.children) {\n      node.r = Math.max(0, +radius(node) || 0);\n    }\n  };\n}\n\nfunction packChildren(padding, k) {\n  return function(node) {\n    if (children = node.children) {\n      var children,\n          i,\n          n = children.length,\n          r = padding(node) * k || 0,\n          e;\n\n      if (r) for (i = 0; i < n; ++i) children[i].r += r;\n      e = packEnclose(children);\n      if (r) for (i = 0; i < n; ++i) children[i].r -= r;\n      node.r = e + r;\n    }\n  };\n}\n\nfunction translateChild(k) {\n  return function(node) {\n    var parent = node.parent;\n    node.r *= k;\n    if (parent) {\n      node.x = parent.x + k * node.x;\n      node.y = parent.y + k * node.y;\n    }\n  };\n}\n", "export default function(node) {\n  node.x0 = Math.round(node.x0);\n  node.y0 = Math.round(node.y0);\n  node.x1 = Math.round(node.x1);\n  node.y1 = Math.round(node.y1);\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (x1 - x0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.y0 = y0, node.y1 = y1;\n    node.x0 = x0, node.x1 = x0 += node.value * k;\n  }\n}\n", "import {required} from \"./accessors.js\";\nimport {Node, computeHeight} from \"./hierarchy/index.js\";\n\nvar keyPrefix = \"$\", // Protect against keys like “__proto__”.\n    preroot = {depth: -1},\n    ambiguous = {};\n\nfunction defaultId(d) {\n  return d.id;\n}\n\nfunction defaultParentId(d) {\n  return d.parentId;\n}\n\nexport default function() {\n  var id = defaultId,\n      parentId = defaultParentId;\n\n  function stratify(data) {\n    var d,\n        i,\n        n = data.length,\n        root,\n        parent,\n        node,\n        nodes = new Array(n),\n        nodeId,\n        nodeKey,\n        nodeByKey = {};\n\n    for (i = 0; i < n; ++i) {\n      d = data[i], node = nodes[i] = new Node(d);\n      if ((nodeId = id(d, i, data)) != null && (nodeId += \"\")) {\n        nodeKey = keyPrefix + (node.id = nodeId);\n        nodeByKey[nodeKey] = nodeKey in nodeByKey ? ambiguous : node;\n      }\n    }\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i], nodeId = parentId(data[i], i, data);\n      if (nodeId == null || !(nodeId += \"\")) {\n        if (root) throw new Error(\"multiple roots\");\n        root = node;\n      } else {\n        parent = nodeByKey[keyPrefix + nodeId];\n        if (!parent) throw new Error(\"missing: \" + nodeId);\n        if (parent === ambiguous) throw new Error(\"ambiguous: \" + nodeId);\n        if (parent.children) parent.children.push(node);\n        else parent.children = [node];\n        node.parent = parent;\n      }\n    }\n\n    if (!root) throw new Error(\"no root\");\n    root.parent = preroot;\n    root.eachBefore(function(node) { node.depth = node.parent.depth + 1; --n; }).eachBefore(computeHeight);\n    root.parent = null;\n    if (n > 0) throw new Error(\"cycle\");\n\n    return root;\n  }\n\n  stratify.id = function(x) {\n    return arguments.length ? (id = required(x), stratify) : id;\n  };\n\n  stratify.parentId = function(x) {\n    return arguments.length ? (parentId = required(x), stratify) : parentId;\n  };\n\n  return stratify;\n}\n", "import {Node} from \"./hierarchy/index.js\";\n\nfunction defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\n// function radialSeparation(a, b) {\n//   return (a.parent === b.parent ? 1 : 2) / a.depth;\n// }\n\n// This function is used to traverse the left contour of a subtree (or\n// subforest). It returns the successor of v on this contour. This successor is\n// either given by the leftmost child of v or by the thread of v. The function\n// returns null if and only if v is on the highest level of its subtree.\nfunction nextLeft(v) {\n  var children = v.children;\n  return children ? children[0] : v.t;\n}\n\n// This function works analogously to nextLeft.\nfunction nextRight(v) {\n  var children = v.children;\n  return children ? children[children.length - 1] : v.t;\n}\n\n// Shifts the current subtree rooted at w+. This is done by increasing\n// prelim(w+) and mod(w+) by shift.\nfunction moveSubtree(wm, wp, shift) {\n  var change = shift / (wp.i - wm.i);\n  wp.c -= change;\n  wp.s += shift;\n  wm.c += change;\n  wp.z += shift;\n  wp.m += shift;\n}\n\n// All other shifts, applied to the smaller subtrees between w- and w+, are\n// performed by this function. To prepare the shifts, we have to adjust\n// change(w+), shift(w+), and change(w-).\nfunction executeShifts(v) {\n  var shift = 0,\n      change = 0,\n      children = v.children,\n      i = children.length,\n      w;\n  while (--i >= 0) {\n    w = children[i];\n    w.z += shift;\n    w.m += shift;\n    shift += w.s + (change += w.c);\n  }\n}\n\n// If vi-’s ancestor is a sibling of v, returns vi-’s ancestor. Otherwise,\n// returns the specified (default) ancestor.\nfunction nextAncestor(vim, v, ancestor) {\n  return vim.a.parent === v.parent ? vim.a : ancestor;\n}\n\nfunction TreeNode(node, i) {\n  this._ = node;\n  this.parent = null;\n  this.children = null;\n  this.A = null; // default ancestor\n  this.a = this; // ancestor\n  this.z = 0; // prelim\n  this.m = 0; // mod\n  this.c = 0; // change\n  this.s = 0; // shift\n  this.t = null; // thread\n  this.i = i; // number\n}\n\nTreeNode.prototype = Object.create(Node.prototype);\n\nfunction treeRoot(root) {\n  var tree = new TreeNode(root, 0),\n      node,\n      nodes = [tree],\n      child,\n      children,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if (children = node._.children) {\n      node.children = new Array(n = children.length);\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = node.children[i] = new TreeNode(children[i], i));\n        child.parent = node;\n      }\n    }\n  }\n\n  (tree.parent = new TreeNode(null, 0)).children = [tree];\n  return tree;\n}\n\n// Node-link tree diagram using the Reingold-Tilford \"tidy\" algorithm\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = null;\n\n  function tree(root) {\n    var t = treeRoot(root);\n\n    // Compute the layout using Buchheim et al.’s algorithm.\n    t.eachAfter(firstWalk), t.parent.m = -t.z;\n    t.eachBefore(secondWalk);\n\n    // If a fixed node size is specified, scale x and y.\n    if (nodeSize) root.eachBefore(sizeNode);\n\n    // If a fixed tree size is specified, scale x and y based on the extent.\n    // Compute the left-most, right-most, and depth-most nodes for extents.\n    else {\n      var left = root,\n          right = root,\n          bottom = root;\n      root.eachBefore(function(node) {\n        if (node.x < left.x) left = node;\n        if (node.x > right.x) right = node;\n        if (node.depth > bottom.depth) bottom = node;\n      });\n      var s = left === right ? 1 : separation(left, right) / 2,\n          tx = s - left.x,\n          kx = dx / (right.x + s + tx),\n          ky = dy / (bottom.depth || 1);\n      root.eachBefore(function(node) {\n        node.x = (node.x + tx) * kx;\n        node.y = node.depth * ky;\n      });\n    }\n\n    return root;\n  }\n\n  // Computes a preliminary x-coordinate for v. Before that, FIRST WALK is\n  // applied recursively to the children of v, as well as the function\n  // APPORTION. After spacing out the children by calling EXECUTE SHIFTS, the\n  // node v is placed to the midpoint of its outermost children.\n  function firstWalk(v) {\n    var children = v.children,\n        siblings = v.parent.children,\n        w = v.i ? siblings[v.i - 1] : null;\n    if (children) {\n      executeShifts(v);\n      var midpoint = (children[0].z + children[children.length - 1].z) / 2;\n      if (w) {\n        v.z = w.z + separation(v._, w._);\n        v.m = v.z - midpoint;\n      } else {\n        v.z = midpoint;\n      }\n    } else if (w) {\n      v.z = w.z + separation(v._, w._);\n    }\n    v.parent.A = apportion(v, w, v.parent.A || siblings[0]);\n  }\n\n  // Computes all real x-coordinates by summing up the modifiers recursively.\n  function secondWalk(v) {\n    v._.x = v.z + v.parent.m;\n    v.m += v.parent.m;\n  }\n\n  // The core of the algorithm. Here, a new subtree is combined with the\n  // previous subtrees. Threads are used to traverse the inside and outside\n  // contours of the left and right subtree up to the highest common level. The\n  // vertices used for the traversals are vi+, vi-, vo-, and vo+, where the\n  // superscript o means outside and i means inside, the subscript - means left\n  // subtree and + means right subtree. For summing up the modifiers along the\n  // contour, we use respective variables si+, si-, so-, and so+. Whenever two\n  // nodes of the inside contours conflict, we compute the left one of the\n  // greatest uncommon ancestors using the function ANCESTOR and call MOVE\n  // SUBTREE to shift the subtree and prepare the shifts of smaller subtrees.\n  // Finally, we add a new thread (if necessary).\n  function apportion(v, w, ancestor) {\n    if (w) {\n      var vip = v,\n          vop = v,\n          vim = w,\n          vom = vip.parent.children[0],\n          sip = vip.m,\n          sop = vop.m,\n          sim = vim.m,\n          som = vom.m,\n          shift;\n      while (vim = nextRight(vim), vip = nextLeft(vip), vim && vip) {\n        vom = nextLeft(vom);\n        vop = nextRight(vop);\n        vop.a = v;\n        shift = vim.z + sim - vip.z - sip + separation(vim._, vip._);\n        if (shift > 0) {\n          moveSubtree(nextAncestor(vim, v, ancestor), v, shift);\n          sip += shift;\n          sop += shift;\n        }\n        sim += vim.m;\n        sip += vip.m;\n        som += vom.m;\n        sop += vop.m;\n      }\n      if (vim && !nextRight(vop)) {\n        vop.t = vim;\n        vop.m += sim - sop;\n      }\n      if (vip && !nextLeft(vom)) {\n        vom.t = vip;\n        vom.m += sip - som;\n        ancestor = v;\n      }\n    }\n    return ancestor;\n  }\n\n  function sizeNode(node) {\n    node.x *= dx;\n    node.y = node.depth * dy;\n  }\n\n  tree.separation = function(x) {\n    return arguments.length ? (separation = x, tree) : separation;\n  };\n\n  tree.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], tree) : (nodeSize ? null : [dx, dy]);\n  };\n\n  tree.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], tree) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return tree;\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (y1 - y0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.x0 = x0, node.x1 = x1;\n    node.y0 = y0, node.y1 = y0 += node.value * k;\n  }\n}\n", "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\n\nexport var phi = (1 + Math.sqrt(5)) / 2;\n\nexport function squarifyRatio(ratio, parent, x0, y0, x1, y1) {\n  var rows = [],\n      nodes = parent.children,\n      row,\n      nodeValue,\n      i0 = 0,\n      i1 = 0,\n      n = nodes.length,\n      dx, dy,\n      value = parent.value,\n      sumValue,\n      minValue,\n      maxValue,\n      newRatio,\n      minRatio,\n      alpha,\n      beta;\n\n  while (i0 < n) {\n    dx = x1 - x0, dy = y1 - y0;\n\n    // Find the next non-empty node.\n    do sumValue = nodes[i1++].value; while (!sumValue && i1 < n);\n    minValue = maxValue = sumValue;\n    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);\n    beta = sumValue * sumValue * alpha;\n    minRatio = Math.max(maxValue / beta, beta / minValue);\n\n    // Keep adding nodes while the aspect ratio maintains or improves.\n    for (; i1 < n; ++i1) {\n      sumValue += nodeValue = nodes[i1].value;\n      if (nodeValue < minValue) minValue = nodeValue;\n      if (nodeValue > maxValue) maxValue = nodeValue;\n      beta = sumValue * sumValue * alpha;\n      newRatio = Math.max(maxValue / beta, beta / minValue);\n      if (newRatio > minRatio) { sumValue -= nodeValue; break; }\n      minRatio = newRatio;\n    }\n\n    // Position and record the row orientation.\n    rows.push(row = {value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1)});\n    if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);\n    else treemapSlice(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);\n    value -= sumValue, i0 = i1;\n  }\n\n  return rows;\n}\n\nexport default (function custom(ratio) {\n\n  function squarify(parent, x0, y0, x1, y1) {\n    squarifyRatio(ratio, parent, x0, y0, x1, y1);\n  }\n\n  squarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return squarify;\n})(phi);\n", "import roundNode from \"./round.js\";\nimport squarify from \"./squarify.js\";\nimport {required} from \"../accessors.js\";\nimport constant, {constantZero} from \"../constant.js\";\n\nexport default function() {\n  var tile = squarify,\n      round = false,\n      dx = 1,\n      dy = 1,\n      paddingStack = [0],\n      paddingInner = constantZero,\n      paddingTop = constantZero,\n      paddingRight = constantZero,\n      paddingBottom = constantZero,\n      paddingLeft = constantZero;\n\n  function treemap(root) {\n    root.x0 =\n    root.y0 = 0;\n    root.x1 = dx;\n    root.y1 = dy;\n    root.eachBefore(positionNode);\n    paddingStack = [0];\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(node) {\n    var p = paddingStack[node.depth],\n        x0 = node.x0 + p,\n        y0 = node.y0 + p,\n        x1 = node.x1 - p,\n        y1 = node.y1 - p;\n    if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n    if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n    node.x0 = x0;\n    node.y0 = y0;\n    node.x1 = x1;\n    node.y1 = y1;\n    if (node.children) {\n      p = paddingStack[node.depth + 1] = paddingInner(node) / 2;\n      x0 += paddingLeft(node) - p;\n      y0 += paddingTop(node) - p;\n      x1 -= paddingRight(node) - p;\n      y1 -= paddingBottom(node) - p;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      tile(node, x0, y0, x1, y1);\n    }\n  }\n\n  treemap.round = function(x) {\n    return arguments.length ? (round = !!x, treemap) : round;\n  };\n\n  treemap.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], treemap) : [dx, dy];\n  };\n\n  treemap.tile = function(x) {\n    return arguments.length ? (tile = required(x), treemap) : tile;\n  };\n\n  treemap.padding = function(x) {\n    return arguments.length ? treemap.paddingInner(x).paddingOuter(x) : treemap.paddingInner();\n  };\n\n  treemap.paddingInner = function(x) {\n    return arguments.length ? (paddingInner = typeof x === \"function\" ? x : constant(+x), treemap) : paddingInner;\n  };\n\n  treemap.paddingOuter = function(x) {\n    return arguments.length ? treemap.paddingTop(x).paddingRight(x).paddingBottom(x).paddingLeft(x) : treemap.paddingTop();\n  };\n\n  treemap.paddingTop = function(x) {\n    return arguments.length ? (paddingTop = typeof x === \"function\" ? x : constant(+x), treemap) : paddingTop;\n  };\n\n  treemap.paddingRight = function(x) {\n    return arguments.length ? (paddingRight = typeof x === \"function\" ? x : constant(+x), treemap) : paddingRight;\n  };\n\n  treemap.paddingBottom = function(x) {\n    return arguments.length ? (paddingBottom = typeof x === \"function\" ? x : constant(+x), treemap) : paddingBottom;\n  };\n\n  treemap.paddingLeft = function(x) {\n    return arguments.length ? (paddingLeft = typeof x === \"function\" ? x : constant(+x), treemap) : paddingLeft;\n  };\n\n  return treemap;\n}\n", "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\nimport {phi, squarifyRatio} from \"./squarify.js\";\n\nexport default (function custom(ratio) {\n\n  function resquarify(parent, x0, y0, x1, y1) {\n    if ((rows = parent._squarify) && (rows.ratio === ratio)) {\n      var rows,\n          row,\n          nodes,\n          i,\n          j = -1,\n          n,\n          m = rows.length,\n          value = parent.value;\n\n      while (++j < m) {\n        row = rows[j], nodes = row.children;\n        for (i = row.value = 0, n = nodes.length; i < n; ++i) row.value += nodes[i].value;\n        if (row.dice) treemapDice(row, x0, y0, x1, y0 += (y1 - y0) * row.value / value);\n        else treemapSlice(row, x0, y0, x0 += (x1 - x0) * row.value / value, y1);\n        value -= row.value;\n      }\n    } else {\n      parent._squarify = rows = squarifyRatio(ratio, parent, x0, y0, x1, y1);\n      rows.ratio = ratio;\n    }\n  }\n\n  resquarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return resquarify;\n})(phi);\n", "import roundNode from \"./treemap/round.js\";\nimport treemapDice from \"./treemap/dice.js\";\n\nexport default function() {\n  var dx = 1,\n      dy = 1,\n      padding = 0,\n      round = false;\n\n  function partition(root) {\n    var n = root.height + 1;\n    root.x0 =\n    root.y0 = padding;\n    root.x1 = dx;\n    root.y1 = dy / n;\n    root.eachBefore(positionNode(dy, n));\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(dy, n) {\n    return function(node) {\n      if (node.children) {\n        treemapDice(node, node.x0, dy * (node.depth + 1) / n, node.x1, dy * (node.depth + 2) / n);\n      }\n      var x0 = node.x0,\n          y0 = node.y0,\n          x1 = node.x1 - padding,\n          y1 = node.y1 - padding;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      node.x0 = x0;\n      node.y0 = y0;\n      node.x1 = x1;\n      node.y1 = y1;\n    };\n  }\n\n  partition.round = function(x) {\n    return arguments.length ? (round = !!x, partition) : round;\n  };\n\n  partition.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], partition) : [dx, dy];\n  };\n\n  partition.padding = function(x) {\n    return arguments.length ? (padding = +x, partition) : padding;\n  };\n\n  return partition;\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      i, n = nodes.length,\n      sum, sums = new Array(n + 1);\n\n  for (sums[0] = sum = i = 0; i < n; ++i) {\n    sums[i + 1] = sum += nodes[i].value;\n  }\n\n  partition(0, n, parent.value, x0, y0, x1, y1);\n\n  function partition(i, j, value, x0, y0, x1, y1) {\n    if (i >= j - 1) {\n      var node = nodes[i];\n      node.x0 = x0, node.y0 = y0;\n      node.x1 = x1, node.y1 = y1;\n      return;\n    }\n\n    var valueOffset = sums[i],\n        valueTarget = (value / 2) + valueOffset,\n        k = i + 1,\n        hi = j - 1;\n\n    while (k < hi) {\n      var mid = k + hi >>> 1;\n      if (sums[mid] < valueTarget) k = mid + 1;\n      else hi = mid;\n    }\n\n    if ((valueTarget - sums[k - 1]) < (sums[k] - valueTarget) && i + 1 < k) --k;\n\n    var valueLeft = sums[k] - valueOffset,\n        valueRight = value - valueLeft;\n\n    if ((x1 - x0) > (y1 - y0)) {\n      var xk = (x0 * valueRight + x1 * valueLeft) / value;\n      partition(i, k, valueLeft, x0, y0, xk, y1);\n      partition(k, j, valueRight, xk, y0, x1, y1);\n    } else {\n      var yk = (y0 * valueRight + y1 * valueLeft) / value;\n      partition(i, k, valueLeft, x0, y0, x1, yk);\n      partition(k, j, valueRight, x0, yk, x1, y1);\n    }\n  }\n}\n", "import dice from \"./dice.js\";\nimport slice from \"./slice.js\";\n\nexport default function(parent, x0, y0, x1, y1) {\n  (parent.depth & 1 ? slice : dice)(parent, x0, y0, x1, y1);\n}\n", "import * as d3Hierarchy from 'd3-hierarchy'\n\nexport default function calChartTreemapData() {\n  let { opts, legendData, chartData, seriesMap } = this\n  let { width, height, padding } = opts\n  let chartTreemap = seriesMap['treemap'][0]\n\n  chartTreemap.tile =\n    chartTreemap.tile == 'treemapBinary' || 'treemapDice' || 'treemapResquarify' || 'treemapSlice' || 'treemapSliceDice' || 'treemapSquarify'\n      ? chartTreemap.tile\n      : 'treemapSquarify'\n\n  let hierarchydata = d3Hierarchy\n    .hierarchy(chartTreemap, function (d) {\n      return d.data\n    })\n    .sum(function (d) {\n      return d.value\n    })\n  let treemapGen = d3Hierarchy\n    .treemap()\n    .tile(d3Hierarchy[chartTreemap.tile])\n    .size([width, height])\n    .paddingTop(padding[0])\n    .paddingRight(padding[1])\n    .paddingBottom(padding[2] + legendData.legendHeight)\n    .paddingLeft(padding[3])\n  let treemapData = treemapGen(hierarchydata)\n\n  chartData.chartTreemap = treemapData\n\n  console.log('complete calChartTreemapData', chartData.chartTreemap)\n}\n", "// Word cloud layout by <PERSON>, https://www.jasondavies.com/wordcloud/\n// Algorithm due to <PERSON>, http://static.mrfeinberg.com/bv_ch03.pdf\n\nconst cloudRadians = Math.PI / 180\n// cw = (1 << 11) >> 5, // wt\n// ch = 1 << 11; // wt\nlet cw, ch\n\nfunction cloudText(d) {\n  return d.text\n}\n\nfunction cloudFont() {\n  return 'Serif'\n}\n\nfunction cloudFontNormal() {\n  return 'normal'\n}\n\nfunction cloudFontSize(d) {\n  return d.value\n}\n\nfunction cloudRotate() {\n  return ~~(Math.random() * 2) * 90\n}\n\nfunction cloudPadding() {\n  return 1\n}\n\n// Fetches a monochrome sprite bitmap for the specified text.\n// Load in batches for speed.\nfunction cloudSprite(contextAndRatio, d, data, di) {\n  if (d.sprite) return\n  const c = contextAndRatio.context,\n    ratio = contextAndRatio.ratio\n  c.clearRect(0, 0, (cw << 5) / ratio, ch / ratio)\n  let x = 0,\n    y = 0,\n    maxh = 0\n  const n = data.length\n  --di\n  while (++di < n) {\n    d = data[di]\n    c.save()\n    c.fillStyle = c.strokeStyle = '#ff0000'\n    c.textAlign = 'center'\n    c.font = `${d.style} ${d.weight} ${~~((d.size + 1) / ratio)}px ${d.font}`\n    let w = c.measureText(d.text + 'm').width * ratio,\n      h = d.size << 1\n    if (d.rotate) {\n      const sr = Math.sin(d.rotate * cloudRadians),\n        cr = Math.cos(d.rotate * cloudRadians),\n        wcr = w * cr,\n        wsr = w * sr,\n        hcr = h * cr,\n        hsr = h * sr\n      w = ((Math.max(Math.abs(wcr + hsr), Math.abs(wcr - hsr)) + 0x1f) >> 5) << 5\n      h = ~~Math.max(Math.abs(wsr + hcr), Math.abs(wsr - hcr))\n    } else {\n      w = ((w + 0x1f) >> 5) << 5\n    }\n    if (h > maxh) maxh = h\n    if (x + w >= cw << 5) {\n      x = 0\n      y += maxh\n      maxh = 0\n    }\n    if (y + h >= ch) break\n    c.translate((x + (w >> 1)) / ratio, (y + (h >> 1)) / ratio)\n    if (d.rotate) c.rotate(d.rotate * cloudRadians)\n    c.fillText(d.text, 0, 0)\n    if (d.padding) {\n      c.lineWidth = 2 * d.padding\n      c.strokeText(d.text, 0, 0)\n    }\n\n    c.restore()\n    d.width = w\n    d.height = h\n    d.xoff = x\n    d.yoff = y\n    d.x1 = w >> 1\n    d.y1 = h >> 1\n    d.x0 = -d.x1\n    d.y0 = -d.y1\n    d.hasText = true\n    x += w\n  }\n  const pixels = c.getImageData(0, 0, (cw << 5) / ratio, ch / ratio).data,\n    sprite = []\n  while (--di >= 0) {\n    d = data[di]\n    if (!d.hasText) continue\n    const w = d.width,\n      w32 = w >> 5\n    let h = d.y1 - d.y0\n    // Zero the buffer\n    for (let i = 0; i < h * w32; i++) sprite[i] = 0\n    x = d.xoff\n    if (x == null) return\n    y = d.yoff\n    let seen = 0,\n      seenRow = -1\n    for (let j = 0; j < h; j++) {\n      for (let i = 0; i < w; i++) {\n        let k = w32 * j + (i >> 5),\n          m = pixels[((y + j) * (cw << 5) + (x + i)) << 2] ? 1 << (31 - (i % 32)) : 0\n        sprite[k] |= m\n        seen |= m\n      }\n      if (seen) seenRow = j\n      else {\n        d.y0++\n        h--\n        j--\n        y++\n      }\n    }\n    d.y1 = d.y0 + seenRow\n    d.sprite = sprite.slice(0, (d.y1 - d.y0) * w32)\n  }\n}\n\n// Use mask-based collision detection.\nfunction cloudCollide(tag, board, sw) {\n  sw >>= 5\n  const sprite = tag.sprite,\n    w = tag.width >> 5,\n    lx = tag.x - (w << 4),\n    sx = lx & 0x7f,\n    msx = 32 - sx,\n    h = tag.y1 - tag.y0\n  let x = (tag.y + tag.y0) * sw + (lx >> 5),\n    last\n  for (let j = 0; j < h; j++) {\n    last = 0\n    for (let i = 0; i <= w; i++) {\n      if (((last << msx) | (i < w ? (last = sprite[j * w + i]) >>> sx : 0)) & board[x + i]) return true\n    }\n    x += sw\n  }\n  return false\n}\n\nfunction cloudBounds(bounds, d) {\n  const b0 = bounds[0],\n    b1 = bounds[1]\n  if (d.x + d.x0 < b0.x) b0.x = d.x + d.x0\n  if (d.y + d.y0 < b0.y) b0.y = d.y + d.y0\n  if (d.x + d.x1 > b1.x) b1.x = d.x + d.x1\n  if (d.y + d.y1 > b1.y) b1.y = d.y + d.y1\n}\n\nfunction collideRects(a, b) {\n  return a.x + a.x1 > b[0].x && a.x + a.x0 < b[1].x && a.y + a.y1 > b[0].y && a.y + a.y0 < b[1].y\n}\n\nfunction archimedeanSpiral(size) {\n  let e = size[0] / size[1]\n  return function (t) {\n    return [e * (t *= 0.1) * Math.cos(t), t * Math.sin(t)]\n  }\n}\n\nfunction rectangularSpiral(size) {\n  const dy = 4,\n    dx = (dy * size[0]) / size[1]\n  let x = 0,\n    y = 0\n  return function (t) {\n    const sign = t < 0 ? -1 : 1\n    // See triangular numbers: T_n = n * (n + 1) / 2.\n    switch ((Math.sqrt(1 + 4 * sign * t) - sign) & 3) {\n      case 0:\n        x += dx\n        break\n      case 1:\n        y += dy\n        break\n      case 2:\n        x -= dx\n        break\n      default:\n        y -= dy\n        break\n    }\n    return [x, y]\n  }\n}\n\n// TODO reuse arrays?\nfunction zeroArray(n) {\n  const a = []\n  let i = -1\n  while (++i < n) a[i] = 0\n  return a\n}\n\nfunction functor(d) {\n  return typeof d === 'function'\n    ? d\n    : function () {\n        return d\n      }\n}\n\nconst spirals = {\n  archimedean: archimedeanSpiral,\n  rectangular: rectangularSpiral,\n}\n\nexport default function () {\n  let size = [256, 256],\n    text = cloudText,\n    font = cloudFont,\n    fontSize = cloudFontSize,\n    fontStyle = cloudFontNormal,\n    fontWeight = cloudFontNormal,\n    rotate = cloudRotate,\n    padding = cloudPadding,\n    spiral = archimedeanSpiral,\n    words = [],\n    timeInterval = Infinity,\n    random = Math.random,\n    canvas = null,\n    end = function () {}\n  const cloud = {}\n\n  cloud.end = function (_) {\n    return arguments.length ? ((end = functor(_)), cloud) : end\n  }\n\n  cloud.canvas = function (_) {\n    return arguments.length ? ((canvas = functor(_)), cloud) : canvas\n  }\n\n  cloud.start = function () {\n    const minSize = Math.min(...size)\n    cw = minSize >> 5\n    ch = minSize\n\n    const contextAndRatio = getContext(canvas()),\n      board = cloud.board ? cloud.board : zeroArray((size[0] >> 5) * size[1]),\n      n = words.length,\n      tags = [],\n      data = words\n        .map(function (d, i) {\n          d.text = text.call(this, d, i)\n          d.font = font.call(this, d, i)\n          d.style = fontStyle.call(this, d, i)\n          d.weight = fontWeight.call(this, d, i)\n          d.rotate = rotate.call(this, d, i)\n          d.size = ~~fontSize.call(this, d, i)\n          d.padding = padding.call(this, d, i)\n          return d\n        })\n        .sort(function (a, b) {\n          return b.size - a.size\n        })\n    let i = -1,\n      bounds = !cloud.board\n        ? null\n        : [\n            {\n              x: 0,\n              y: 0,\n            },\n            {\n              x: size[0],\n              y: size[1],\n            },\n          ]\n\n    step()\n\n    function step() {\n      const start = Date.now()\n      while (Date.now() - start < timeInterval && ++i < n) {\n        const d = data[i]\n        d.x = (size[0] * (random() + 0.5)) >> 1\n        d.y = (size[1] * (random() + 0.5)) >> 1\n        cloudSprite(contextAndRatio, d, data, i)\n        if (d.hasText && place(board, d, bounds)) {\n          tags.push(d)\n          if (bounds) {\n            if (!cloud.hasImage) {\n              // update bounds if image mask not set\n              cloudBounds(bounds, d)\n            }\n          } else {\n            bounds = [\n              { x: d.x + d.x0, y: d.y + d.y0 },\n              { x: d.x + d.x1, y: d.y + d.y1 },\n            ]\n          }\n          // Temporary hack\n          d.x -= size[0] >> 1\n          d.y -= size[1] >> 1\n        }\n      }\n\n      if (i >= n) {\n        console.log('END', tags)\n        end(tags)\n      }\n\n      cloud._tags = tags\n      cloud._bounds = bounds\n    }\n\n    return cloud\n  }\n\n  function getContext(canvas) {\n    canvas.width = canvas.height = 1\n    const ratio = Math.sqrt(canvas.getContext('2d').getImageData(0, 0, 1, 1).data.length >> 2)\n    canvas.width = (cw << 5) / ratio\n    canvas.height = ch / ratio\n    const context = canvas.getContext('2d')\n    context.fillStyle = context.strokeStyle = '#ff0000'\n    context.textAlign = 'center'\n    return { context, ratio }\n  }\n\n  function place(board, tag, bounds) {\n    // const perimeter = [{ x: 0, y: 0 }, { x: size[0], y: size[1] }],\n    const startX = tag.x,\n      startY = tag.y,\n      maxDelta = Math.sqrt(size[0] * size[0] + size[1] * size[1]),\n      s = spiral(size),\n      dt = random() < 0.5 ? 1 : -1\n    let dxdy,\n      t = -dt,\n      dx,\n      dy\n\n    while ((dxdy = s((t += dt)))) {\n      dx = ~~dxdy[0]\n      dy = ~~dxdy[1]\n\n      if (Math.min(Math.abs(dx), Math.abs(dy)) >= maxDelta) break\n\n      tag.x = startX + dx\n      tag.y = startY + dy\n\n      if (tag.x + tag.x0 < 0 || tag.y + tag.y0 < 0 || tag.x + tag.x1 > size[0] || tag.y + tag.y1 > size[1]) continue\n      // TODO only check for collisions within current bounds.\n      if (!bounds || !cloudCollide(tag, board, size[0])) {\n        if (!bounds || collideRects(tag, bounds)) {\n          const sprite = tag.sprite,\n            w = tag.width >> 5,\n            sw = size[0] >> 5,\n            lx = tag.x - (w << 4),\n            sx = lx & 0x7f,\n            msx = 32 - sx,\n            h = tag.y1 - tag.y0\n          let last,\n            x = (tag.y + tag.y0) * sw + (lx >> 5)\n          for (let j = 0; j < h; j++) {\n            last = 0\n            for (let i = 0; i <= w; i++) {\n              board[x + i] |= (last << msx) | (i < w ? (last = sprite[j * w + i]) >>> sx : 0)\n            }\n            x += sw\n          }\n          delete tag.sprite\n          return true\n        }\n      }\n    }\n    return false\n  }\n\n  cloud.createMask = img => {\n    const can = canvas()\n    const [width, height] = size\n    const w32 = width >> 5\n    const board = zeroArray((width >> 5) * height)\n    can.width = width\n    can.height = height\n    const cxt = can.getContext('2d')\n    cxt.drawImage(img, 0, 0, img.width, img.height, 0, 0, width, height)\n    const imageData = cxt.getImageData(0, 0, width, height).data\n    for (let j = 0; j < height; j++) {\n      for (let i = 0; i < width; i++) {\n        const k = w32 * j + (i >> 5)\n        const tmp = (j * width + i) << 2\n        const flag = imageData[tmp] >= 250 && imageData[tmp + 1] >= 250 && imageData[tmp + 2] >= 250\n        const m = flag ? 1 << (31 - (i % 32)) : 0\n        board[k] |= m\n      }\n    }\n    cloud.board = board\n    cloud.hasImage = true\n\n    return cloud\n  }\n\n  cloud.timeInterval = function (_) {\n    return arguments.length ? ((timeInterval = _ == null ? Infinity : _), cloud) : timeInterval\n  }\n\n  cloud.words = function (_) {\n    return arguments.length ? ((words = _), cloud) : words\n  }\n\n  cloud.size = function (_) {\n    return arguments.length ? ((size = [+_[0], +_[1]]), cloud) : size\n  }\n\n  cloud.font = function (_) {\n    return arguments.length ? ((font = functor(_)), cloud) : font\n  }\n\n  cloud.fontStyle = function (_) {\n    return arguments.length ? ((fontStyle = functor(_)), cloud) : fontStyle\n  }\n\n  cloud.fontWeight = function (_) {\n    return arguments.length ? ((fontWeight = functor(_)), cloud) : fontWeight\n  }\n\n  cloud.rotate = function (_) {\n    return arguments.length ? ((rotate = functor(_)), cloud) : rotate\n  }\n\n  cloud.text = function (_) {\n    return arguments.length ? ((text = functor(_)), cloud) : text\n  }\n\n  cloud.spiral = function (_) {\n    return arguments.length ? ((spiral = spirals[_] || _), cloud) : spiral\n  }\n\n  cloud.fontSize = function (_) {\n    return arguments.length ? ((fontSize = functor(_)), cloud) : fontSize\n  }\n\n  cloud.padding = function (_) {\n    return arguments.length ? ((padding = functor(_)), cloud) : padding\n  }\n\n  cloud.random = function (_) {\n    return arguments.length ? ((random = _), cloud) : random\n  }\n\n  return cloud\n}\n", "import cloneDeep from 'lodash.clonedeep'\nimport tagCloud from '../util/tag-cloud'\n\nexport default function calChartTagCloudData() {\n  let { opts, chartData, seriesMap } = this\n  let { element, width, height } = opts\n  let chartTagCloud = cloneDeep(seriesMap['tagCloud'][0])\n\n  const layout = tagCloud()\n  layout.canvas(chartTagCloud.canvas || element)\n  layout.size(chartTagCloud.size || [width, height])\n  layout.words(chartTagCloud.data)\n  layout.end(end)\n  if (chartTagCloud.font) layout.font(chartTagCloud.font)\n  if (chartTagCloud.fontSize) layout.fontSize(chartTagCloud.fontSize)\n  if (chartTagCloud.padding) layout.padding(chartTagCloud.padding)\n  if (chartTagCloud.rotate) layout.rotate(chartTagCloud.rotate)\n  if (chartTagCloud.spiral) layout.spiral(chartTagCloud.spiral)\n  if (chartTagCloud.timeInterval) layout.timeInterval(chartTagCloud.timeInterval)\n  if (chartTagCloud.imageMask) layout.createMask(chartTagCloud.imageMask)\n\n  layout.start()\n\n  function end(data) {\n    chartTagCloud.data = data\n    chartData.chartTagCloud = chartTagCloud\n  }\n\n  console.log('complete calChartTagCloudData', chartData.chartTagCloud)\n}\n", "import { calOptions } from './calOptions'\nimport calSeriesMap from './calSeriesMap'\nimport calSeriesColor from './calSeriesColor'\nimport calLegendData from './calLegendData'\nimport calAxisData from './calAxisData'\nimport calAxisRadarData from './calAxisRadarData'\n\nimport calChartBarData from './calChartBarData'\nimport calChartLineData from './calChartLineData'\nimport calChartPieData from './calChartPieData'\nimport calChartRadarData from './calChartRadarData'\nimport calChartScatterData from './calChartScatterData'\nimport calChartFunnelData from './calChartFunnelData'\nimport calChartCandlestickData from './calChartCandlestickData'\nimport calChartHeatmapData from './calChartHeatmapData'\nimport calChartTreemapData from './calChartTreemapData'\nimport calChartTagCloudData from './calChartTagCloudData'\n\nexport default function calChartsData() {\n  // 将opts的数据补充完整\n  calOptions.call(this)\n\n  // 计算数据\n  calSeriesMap.call(this)\n  calSeriesColor.call(this)\n  calLegendData.call(this)\n\n  // 有相同xy轴的图表，只计算一次\n  if (this.seriesMap.line || this.seriesMap.bar || this.seriesMap.scatter || this.seriesMap.candlestick || this.seriesMap.heatmap) {\n    calAxisData.call(this)\n  }\n  Object.keys(this.seriesMap).forEach(type => {\n    if (this.seriesMap[type]) {\n      switch (type) {\n        case 'bar':\n          calChartBarData.call(this)\n          break\n        case 'line':\n          calChartLineData.call(this)\n          break\n        case 'pie':\n          calChartPieData.call(this)\n          break\n        case 'radar':\n          calAxisRadarData.call(this)\n          calChartRadarData.call(this)\n          break\n        case 'scatter':\n          calChartScatterData.call(this)\n          break\n        case 'funnel':\n          calChartFunnelData.call(this)\n          break\n        case 'candlestick':\n          calChartCandlestickData.call(this)\n          break\n        case 'heatmap':\n          calChartHeatmapData.call(this)\n          break\n        case 'treemap':\n          calChartTreemapData.call(this)\n          break\n        case 'tagCloud':\n          calChartTagCloudData.call(this)\n          break\n      }\n    }\n  })\n\n  console.log('complete calChartsData')\n}\n", "export default {\n  easeIn: function (pos) {\n    return Math.pow(pos, 3)\n  },\n  easeOut: function (pos) {\n    return Math.pow(pos - 1, 3) + 1\n  },\n  easeInOut: function (pos) {\n    if ((pos /= 0.5) < 1) {\n      return 0.5 * Math.pow(pos, 3)\n    } else {\n      return 0.5 * (Math.pow(pos - 2, 3) + 2)\n    }\n  },\n  linear: function (pos) {\n    return pos\n  },\n}\n", "import Timing from './timing'\n\nexport default class Animation {\n  constructor(opts) {\n    this.isStop = false\n\n    let { animation, animationDuration, animationTiming, onProcess, onAnimationFinish } = opts\n\n    let createAnimationFrame = function () {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        return requestAnimationFrame\n      } else if (typeof setTimeout !== 'undefined') {\n        return function (step) {\n          setTimeout(function () {\n            let timeStamp = +new Date()\n            step(timeStamp)\n          }, 17)\n        }\n      }\n    }\n    let animationFrame = createAnimationFrame()\n\n    if (animation) {\n      let timingFunction = Timing[animationTiming]\n      let startTimeStamp = null\n\n      let step = function () {\n        if (this.isStop === true) {\n          onProcess(1)\n          onAnimationFinish()\n          return\n        }\n\n        let timeStamp = +new Date()\n        if (!startTimeStamp) startTimeStamp = timeStamp\n\n        if (timeStamp - startTimeStamp < animationDuration) {\n          let process = (timeStamp - startTimeStamp) / animationDuration\n          process = timingFunction(process)\n          opts.onProcess(process)\n          animationFrame(step)\n        } else {\n          onProcess(1)\n          onAnimationFinish()\n        }\n      }\n      step = step.bind(this)\n\n      animationFrame(step)\n    } else {\n      onProcess(1)\n      onAnimationFinish()\n    }\n  }\n\n  /**\n   * 停止动画\n   */\n  stop() {\n    this.isStop = true\n  }\n}\n", "export default function drawTooltip() {\n  const { context, opts, tooltipData } = this\n  const { yAxis, xAxis, tooltip } = opts\n  const { data, axisPointerData, tooltipX, tooltipY, tooltipWidth, tooltipHeight } = tooltipData\n  const { show, axisPointer, backgroundColor, backgroundRadius, backgroundOpacity, padding, itemGap, iconRadius, iconGap, textStyle } = tooltip\n  const { fontSize: textFontSize, color: textColor, lineHeight: textLineHeight } = textStyle\n  const { type: axisPointerType, lineStyle: axisPointerLineStyle, shadowStyle: axisPointerShadowStyle, cross: axisPointerCross } = axisPointer\n  const { lineWdith: axisPointerLineWidth, lineDash: axisPointerLineDash, color: axisPointerLineColor, opacity: axisPointerLineOpacity } = axisPointerLineStyle\n  const { color: axisPointerShadowColor, opacity: axisPointerShadowOpacity } = axisPointerShadowStyle\n  const {\n    show: crossShow,\n    lineWidth: crossLineWidth,\n    lineDash: crossLineDash,\n    lineColor: crossLineColor,\n    lineOpacity: crossLineOpacity,\n    backgroundColor: crossBakcgroundColor,\n    backgroundOpacity: crossBackgroundOpacity,\n    fontColor: crossFontColor,\n    fontPadding: crossFontPadding,\n  } = axisPointerCross\n\n  if (!show || data.length == 0) return\n\n  // draw axisPointer\n  if (axisPointerData) {\n    const { xAxisPointer, yAxisPointer, crossPointer } = axisPointerData\n    const {\n      yAxisLabel,\n      yAxisLabelWidth,\n      yAxisLabelHeight,\n      yAxisLabelX,\n      yAxisLabelY,\n      yAxisLineX0,\n      yAxisLineY0,\n      yAxisLineX1,\n      yAxisLineY1,\n      xAxisLabel,\n      xAxisLabelWidth,\n      xAxisLabelHeight,\n      xAxisLabelX,\n      xAxisLabelY,\n      xAxisLineX0,\n      xAxisLineY0,\n      xAxisLineX1,\n      xAxisLineY1,\n    } = crossPointer\n\n    if (crossShow) {\n      const yAxisLabelFontSize = yAxis.axisLabel.textStyle.fontSize\n      const xAxisLabelFontSize = xAxis.axisLabel.textStyle.fontSize\n\n      // draw cross lineY\n      context.save()\n      context.lineWidth = crossLineWidth\n      context.setLineDash(crossLineDash)\n      context.strokeStyle = crossLineColor\n      context.globalAlpha = crossLineOpacity\n      context.beginPath()\n      context.moveTo(yAxisLineX0, yAxisLineY0)\n      context.lineTo(yAxisLineX1, yAxisLineY1)\n      context.stroke()\n      context.restore()\n\n      // draw cross lineX\n      context.save()\n      context.lineWidth = crossLineWidth\n      context.setLineDash(crossLineDash)\n      context.strokeStyle = crossLineColor\n      context.globalAlpha = crossLineOpacity\n      context.beginPath()\n      context.moveTo(xAxisLineX0, xAxisLineY0)\n      context.lineTo(xAxisLineX1, xAxisLineY1)\n      context.stroke()\n      context.restore()\n\n      // draw cross backgroundY\n      context.save()\n      context.fillStyle = crossBakcgroundColor\n      context.globalAlpha = crossBackgroundOpacity\n      context.fillRect(yAxisLabelX + crossFontPadding, yAxisLabelY - yAxisLabelFontSize / 2 - crossFontPadding, -yAxisLabelWidth, yAxisLabelHeight)\n      context.restore()\n\n      // draw cross backgroundX\n      context.save()\n      context.fillStyle = crossBakcgroundColor\n      context.globalAlpha = crossBackgroundOpacity\n      context.fillRect(xAxisLabelX - xAxisLabelWidth / 2, xAxisLabelY - crossFontPadding, xAxisLabelWidth, xAxisLabelHeight)\n      context.restore()\n\n      // draw cross labelY\n      context.save()\n      context.fillStyle = crossFontColor\n      context.font = `${yAxisLabelFontSize}px`\n      context.textBaseline = 'middle'\n      context.textAlign = 'right'\n      context.fillText(yAxisLabel, yAxisLabelX, yAxisLabelY)\n      context.restore()\n\n      // draw cross labelX\n      context.save()\n      context.fillStyle = crossFontColor\n      context.font = `${xAxisLabelFontSize}px`\n      context.textBaseline = 'top'\n      context.textAlign = 'center'\n      context.fillText(xAxisLabel, xAxisLabelX, xAxisLabelY)\n      context.restore()\n    }\n\n    if (axisPointerType == 'line') {\n      if (xAxisPointer) {\n        const { x0, y0, x1, y1 } = xAxisPointer\n\n        context.save()\n        context.globalAlpha = axisPointerLineOpacity\n        if (axisPointerLineDash) {\n          context.setLineDash(axisPointerLineDash)\n        }\n        context.lineWidth = axisPointerLineWidth\n        context.strokeStyle = axisPointerLineColor\n        context.beginPath()\n        context.moveTo(x0, y0)\n        context.lineTo(x1, y1)\n        context.stroke()\n        context.restore()\n      }\n\n      if (yAxisPointer) {\n      }\n    } else if (axisPointerType == 'shadow') {\n      if (xAxisPointer) {\n        const { x, y, width, height } = xAxisPointer\n\n        context.save()\n        context.globalAlpha = axisPointerShadowOpacity\n        context.fillStyle = axisPointerShadowColor\n        context.fillRect(x, y, width, height)\n        context.restore()\n      }\n\n      if (yAxisPointer) {\n      }\n    }\n  }\n\n  data.forEach(item => {\n    if (item.type == 'line' || item.type == 'radar') {\n      // 放大 symbol\n      const { x, y, color, symbolType, symbolSize, symbolColor } = item\n\n      switch (symbolType) {\n        case 'circle':\n          context.beginPath()\n          context.fillStyle = symbolColor == 'auto' ? color : symbolColor\n          context.arc(x, y, (symbolSize + 5) / 2, 0, 2 * Math.PI)\n          context.fill()\n          context.beginPath()\n          context.fillStyle = '#ffffff'\n          context.arc(x, y, (symbolSize + 6) / 4, 0, 2 * Math.PI)\n          context.fill()\n          context.restore()\n          break\n      }\n    }\n\n    if (item.type == 'pie') {\n      // 放大 itemArea\n      const { color, center, radius, _start_, _end_ } = item\n      const [centerX, centerY] = center\n      const [radiusMin, radiusMax] = radius\n\n      context.save()\n      context.beginPath()\n      context.moveTo(centerX, centerY)\n      context.fillStyle = color\n      context.arc(centerX, centerY, radiusMax + 8, _start_, _end_)\n      context.fill()\n      if (radiusMin > 0) {\n        context.beginPath()\n        context.moveTo(centerX, centerY)\n        context.fillStyle = this.opts.backgroundColor\n        context.arc(centerX, centerY, radiusMin, _start_, _end_)\n        context.fill()\n      }\n      context.restore()\n    }\n  })\n\n  // draw tooltip container\n  drawRadiusRect(context, tooltipX, tooltipY, tooltipWidth, tooltipHeight, backgroundRadius, backgroundColor, backgroundOpacity)\n\n  // draw tooltip content\n  let _contentX = tooltipX + padding + iconRadius\n  let _contentY = tooltipY + padding + textLineHeight / 2\n\n  if (tooltipData.tooltipTitle) {\n    // 存在标题\n    context.save()\n    context.fillStyle = textColor\n    context.font = `${textFontSize}px`\n    context.textBaseline = 'middle'\n    context.textAlign = 'left'\n    context.fillText(tooltipData.tooltipTitle, _contentX - iconRadius, _contentY)\n    context.save()\n\n    _contentY += textLineHeight + itemGap\n  }\n\n  data.forEach(item => {\n    if (item.type == 'candlestick' || item.type == 'k') {\n      const { name, start, end, high, low, volumn, color } = item\n\n      context.save()\n      context.beginPath()\n      context.fillStyle = color\n      context.arc(_contentX, _contentY, iconRadius, 0, 2 * Math.PI)\n      context.fill()\n      _contentX += iconRadius + iconGap\n\n      context.beginPath()\n      context.fillStyle = textColor\n      context.font = `${textFontSize}px`\n      context.textBaseline = 'middle'\n      context.textAlign = 'left'\n      context.fillText(name, _contentX, _contentY)\n      _contentY += textLineHeight + itemGap\n      context.fillText(start, _contentX, _contentY)\n      _contentY += textLineHeight + itemGap\n      context.fillText(end, _contentX, _contentY)\n      _contentY += textLineHeight + itemGap\n      context.fillText(low, _contentX, _contentY)\n      _contentY += textLineHeight + itemGap\n      context.fillText(high, _contentX, _contentY)\n      _contentY += textLineHeight + itemGap\n      if (volumn) {\n        context.fillText(volumn, _contentX, _contentY)\n        _contentY += textLineHeight + itemGap\n      }\n      context.restore()\n      _contentX = tooltipX + padding + iconRadius\n    } else {\n      const { text, color } = item\n\n      context.save()\n      context.beginPath()\n      context.fillStyle = color\n      context.arc(_contentX, _contentY, iconRadius, 0, 2 * Math.PI)\n      context.fill()\n      _contentX += iconRadius + iconGap\n      context.beginPath()\n      context.fillStyle = textColor\n      context.font = `${textFontSize}px`\n      context.textBaseline = 'middle'\n      context.textAlign = 'left'\n      context.fillText(text, _contentX, _contentY)\n      context.restore()\n\n      _contentX = tooltipX + padding + iconRadius\n      _contentY += textLineHeight + itemGap\n    }\n  })\n\n  console.log('complete drawTooltip')\n}\n\nfunction drawRadiusRect(context, x, y, width, height, radius, backgroundColor = '#000000', opacity = 0.7) {\n  // context.save()\n  // context.fillStyle = backgroundColor\n  // context.globalAlpha = opacity\n  // context.beginPath()\n  // context.moveTo(x + radius, y)\n  // context.arcTo(x + width, y, x + width, y + height, radius)\n  // context.lineTo(x + width, y + height - radius)\n  // context.arcTo(x + width, y + height, x, y + height, radius)\n  // context.lineTo(x + radius, y + height)\n  // context.arcTo(x, y + height, x, y, radius)\n  // context.lineTo(x, y + radius)\n  // context.arcTo(x, y, x + radius, y, radius)\n  // context.fill()\n  // context.restore()\n\n  // 兼容华为引擎\n  context.save()\n  context.fillStyle = backgroundColor\n  context.globalAlpha = opacity\n  context.beginPath()\n  context.moveTo(x + radius, y)\n  context.lineTo(x + width - radius, y)\n  context.arc(x + width - radius - 0.5, y + radius + 0.5, radius, -Math.PI / 2, 0)\n  context.lineTo(x + width, y + height - radius)\n  context.arc(x + width - radius - 0.5, y + height - radius - 0.5, radius, 0, Math.PI / 2)\n  context.lineTo(x + radius, y + height)\n  context.arc(x + radius + 0.5, y + height - radius - 0.5, radius, Math.PI / 2, Math.PI)\n  context.lineTo(x, y + radius)\n  context.arc(x + radius + 0.5, y + radius + 0.5, radius, Math.PI, (Math.PI * 3) / 2)\n  context.fill()\n  context.restore()\n}\n", "/**\n * 绘制背景图\n */\n\nexport default function drawBackground(startX = 0, startY = 0, endX = this.opts.width, endY = this.opts.height) {\n  this.context.clearRect(startX, startY, endX, endY)\n  this.context.fillStyle = this.opts.backgroundColor\n  this.context.fillRect(startX, startY, endX, endY)\n\n  console.log('complete drawBackground')\n}\n", "import { getColor } from '../util/util'\n\n/**\n * 绘制图例组件\n */\n\nexport default function drawLegend() {\n  if (!this.opts.legend.show) return\n\n  let { context, opts, legendData } = this\n  let { width, height, legend, padding } = opts\n  let { shapeWidth, shapeHeight, shapeRadius, itemGap, marginTop, textStyle } = legend\n  let { fontSize, color, padding: textPadding } = textStyle\n  let { legendList, legendWidth, legendHeight } = legendData\n  let startY = height - padding[2] - legendHeight + marginTop\n  let startX = padding[3] + (width - padding[1] - padding[3] - legendWidth) / 2\n  let legendHeightMax\n\n  legendList.forEach((listItem, listIndex) => {\n    startX = padding[3] + (width - padding[1] - padding[3] - legendWidth) / 2\n\n    listItem.forEach(legendItem => {\n      let { legendType, color, name, measureText } = legendItem\n      switch (legendType) {\n        case 'circle':\n          legendHeightMax = Math.max(shapeRadius * 2, fontSize)\n          context.beginPath()\n          context.moveTo(startX + shapeRadius, startY + legendHeightMax / 2)\n          context.arc(startX + shapeRadius, startY + legendHeightMax / 2, shapeRadius, 0, 2 * Math.PI)\n          context.closePath()\n\n          context.fillStyle = color\n          context.fill()\n\n          startX += shapeRadius * 2 + textPadding\n          break\n        case 'line':\n          legendHeightMax = Math.max(shapeHeight, fontSize)\n          let lineLength = (shapeWidth - shapeHeight) / 2\n          lineLength < 0 ? 0 : lineLength\n\n          context.beginPath()\n          context.moveTo(startX, startY + legendHeightMax / 2)\n          context.lineTo(startX + lineLength - 2, startY + legendHeightMax / 2)\n          context.closePath()\n          context.lineWidth = 2\n          context.strokeStyle = color\n          context.stroke()\n\n          context.beginPath()\n          context.moveTo(startX + shapeWidth / 2, startY + legendHeightMax / 2)\n          context.arc(startX + shapeWidth / 2, startY + legendHeightMax / 2, shapeHeight / 2, 0, 2 * Math.PI)\n          context.closePath()\n          context.fillStyle = color\n          context.fill()\n\n          context.beginPath()\n          context.moveTo(startX + lineLength + shapeHeight + 2, startY + legendHeightMax / 2)\n          context.lineTo(startX + shapeWidth, startY + legendHeightMax / 2)\n          context.closePath()\n          context.lineWidth = 2\n          context.strokeStyle = color\n          context.stroke()\n\n          startX += shapeWidth + textPadding\n          break\n        case 'rect':\n          legendHeightMax = Math.max(shapeHeight, fontSize)\n          context.fillStyle = getColor(\n            color,\n            context,\n            startX,\n            startY + legendHeightMax / 2 - shapeHeight / 2,\n            startX + shapeWidth,\n            startY + legendHeightMax / 2 + shapeHeight / 2\n          )\n          context.fillRect(startX, startY + legendHeightMax / 2 - shapeHeight / 2, shapeWidth, shapeHeight)\n\n          startX += shapeWidth + textPadding\n          break\n      }\n\n      context.save()\n      context.textAlign = 'left'\n      context.textBaseline = 'middle'\n      context.font = `${fontSize}px`\n      context.fillStyle = color\n      context.fillText(name, startX, startY + legendHeightMax / 2)\n      context.restore()\n\n      startX += measureText + itemGap\n    })\n\n    startY += legendHeightMax + itemGap\n  })\n\n  console.log('complete drawLegend')\n}\n", "/**\n * 绘制Y轴, 包括 axisName(名称), axisLabel(标签), axisTick(刻度线), axisLine(轴线)\n */\nexport default function drawAxis() {\n  let { context, opts, chartData } = this\n  let { xAxis, yAxis } = opts\n\n  let {\n    show: xAxisShow,\n    type: xAxisType,\n    axisName: xAxisName,\n    axisLabel: xAxisLabel,\n    axisTick: xAxisTick,\n    axisLine: xAxisLine,\n    axisSplitLine: xAxisSplitLine,\n  } = xAxis\n  let {\n    show: yAxisShow,\n    type: yAxisType,\n    axisName: yAxisName,\n    axisLabel: yAxisLabel,\n    axisTick: yAxisTick,\n    axisLine: yAxisLine,\n    axisSplitLine: yAxisSplitLine,\n  } = yAxis\n\n  let { show: xAxisNameShow, textStyle: xAxisNameTextStyle } = xAxisName\n  let { show: xAxisLabelShow, textStyle: xAxisLabelTextStyle, rotate: xAxisLabelRotate } = xAxisLabel\n  let { show: xAxisTickShow, lineStyle: xAxisTickStyle } = xAxisTick\n  let { show: xAxisLineShow, lineStyle: xAxisLineStyle } = xAxisLine\n  let { show: xAxisSplitLineShow, lineStyle: xAxisSplitLineStyle } = xAxisSplitLine\n\n  let { show: yAxisNameShow, textStyle: yAxisNameTextStyle } = yAxisName\n  let { show: yAxisLabelShow, textStyle: yAxisLabelTextStyle } = yAxisLabel\n  let { show: yAxisTickShow, lineStyle: yAxisTickStyle } = yAxisTick\n  let { show: yAxisLineShow, lineStyle: yAxisLineStyle } = yAxisLine\n  let { show: yAxisSplitLineShow, lineStyle: yAxisSplitLineStyle } = yAxisSplitLine\n\n  let { color: xAxisNameColor, fontSize: xAxisNameFontSize } = xAxisNameTextStyle\n  let { color: xAxisLabelColor, fontSize: xAxisLabelFontSize } = xAxisLabelTextStyle\n  let { color: xAxisTickLineColor, lineWidth: xAxisTickLineWidth } = xAxisTickStyle\n  let { color: xAxisLineColor, lineWidth: xAxisLineWidth } = xAxisLineStyle\n  let { color: xAxisSplitLineColor, lineWidth: xAxisSplitLineWidth } = xAxisSplitLineStyle\n\n  let { color: yAxisNameColor, fontSize: yAxisNameFontSize } = yAxisNameTextStyle\n  let { color: yAxisLabelColor, fontSize: yAxisLabelFontSize } = yAxisLabelTextStyle\n  let { color: yAxisTickLineColor, lineWidth: yAxisTickLineWidth } = yAxisTickStyle\n  let { color: yAxisLineColor, lineWidth: yAxisLineWidth } = yAxisLineStyle\n  let { color: yAxisSplitLineColor, lineWidth: yAxisSplitLineWidth } = yAxisSplitLineStyle\n\n  let {\n    xAxisLabelPoint,\n    xAxisTickPoint,\n    xAxisLinePoint,\n    xAxisSplitLinePoint,\n    xAxisNamePoint,\n    yAxisLabelPoint,\n    yAxisTickPoint,\n    yAxisLinePoint,\n    yAxisSplitLinePoint,\n    yAxisNamePoint,\n  } = chartData.axisData\n\n  if (yAxisShow) {\n    if (yAxisLabelShow) {\n      context.save()\n      context.font = `${yAxisLabelFontSize}px`\n      context.fillStyle = yAxisLabelColor\n      context.textAlign = 'right'\n      context.textBaseline = 'middle'\n      yAxisLabelPoint.forEach(item => {\n        if (yAxisType == 'value' || item.show) {\n          context.fillText(item.text, item.x, item.y)\n        }\n      })\n      context.restore()\n    }\n\n    if (yAxisSplitLineShow) {\n      context.lineWidth = yAxisSplitLineWidth\n      context.strokeStyle = yAxisSplitLineColor\n\n      yAxisSplitLinePoint.forEach((item, index) => {\n        if (yAxisType == 'value' || item.show) {\n          context.beginPath()\n          context.moveTo(item.startX, item.startY)\n          context.lineTo(item.endX, item.endY)\n          context.closePath()\n          context.stroke()\n        }\n      })\n    }\n\n    if (yAxisNameShow) {\n      context.save()\n      context.font = `${yAxisNameFontSize}px`\n      context.fillStyle = yAxisNameColor\n      context.textAlign = 'center'\n      context.textBaseline = 'bottom'\n      context.fillText(yAxisNamePoint.text, yAxisNamePoint.x, yAxisNamePoint.y)\n      context.restore()\n    }\n  }\n\n  if (xAxisShow) {\n    if (xAxisLabelShow) {\n      context.save()\n      context.font = `${xAxisLabelFontSize}px`\n      context.fillStyle = xAxisLabelColor\n      context.textBaseline = 'top'\n\n      if (xAxisLabelRotate == 0) {\n        context.textAlign = 'center'\n      } else if (xAxisLabelRotate > 0) {\n        context.textAlign = 'right'\n      } else if (xAxisLabelRotate < 0) {\n        context.textAlign = 'left'\n      }\n\n      xAxisLabelPoint.forEach(item => {\n        if (xAxisType == 'value' || item.show) {\n          if (xAxisLabelRotate == 0) {\n            context.fillText(item.text, item.x, item.y)\n          } else {\n            context.save()\n            context.translate(item.x, item.y)\n            context.rotate((-xAxisLabelRotate * Math.PI) / 180)\n            context.fillText(item.text, 0, 0)\n            context.restore()\n          }\n        }\n      })\n\n      context.restore()\n    }\n\n    if (xAxisSplitLineShow) {\n      context.lineWidth = xAxisSplitLineWidth\n      context.strokeStyle = xAxisSplitLineColor\n\n      xAxisSplitLinePoint.forEach((item, index) => {\n        if (xAxisType == 'value' || item.show) {\n          context.beginPath()\n          context.moveTo(item.startX, item.startY)\n          context.lineTo(item.endX, item.endY)\n          context.closePath()\n          context.stroke()\n        }\n      })\n    }\n\n    if (xAxisNameShow) {\n      context.save()\n      context.font = `${xAxisNameFontSize}px`\n      context.fillStyle = xAxisNameColor\n      context.textAlign = 'left'\n      context.textBaseline = 'middle'\n      context.fillText(xAxisNamePoint.text, xAxisNamePoint.x, xAxisNamePoint.y)\n      context.restore()\n    }\n  }\n\n  // 防止轴线被网格线覆盖, 最后绘制\n  if (yAxisShow) {\n    if (yAxisTickShow) {\n      context.lineWidth = yAxisTickLineWidth\n      context.strokeStyle = yAxisTickLineColor\n\n      yAxisTickPoint.forEach(item => {\n        if (yAxisType == 'value' || item.show) {\n          context.beginPath()\n          context.moveTo(item.startX, item.startY)\n          context.lineTo(item.endX, item.endY)\n          context.closePath()\n          context.stroke()\n        }\n      })\n    }\n\n    if (yAxisLineShow) {\n      context.beginPath()\n      context.moveTo(yAxisLinePoint.startX, yAxisLinePoint.startY)\n      context.lineTo(yAxisLinePoint.endX, yAxisLinePoint.endY)\n      context.closePath()\n\n      context.lineWidth = yAxisLineWidth\n      context.strokeStyle = yAxisLineColor\n      context.stroke()\n    }\n  }\n\n  if (xAxisShow) {\n    if (xAxisTickShow) {\n      context.lineWidth = xAxisTickLineWidth\n      context.strokeStyle = xAxisTickLineColor\n\n      xAxisTickPoint.forEach(item => {\n        if (xAxisType == 'value' || item.show) {\n          context.beginPath()\n          context.moveTo(item.startX, item.startY)\n          context.lineTo(item.endX, item.endY)\n          context.closePath()\n          context.stroke()\n        }\n      })\n    }\n\n    if (xAxisLineShow) {\n      context.beginPath()\n      context.moveTo(xAxisLinePoint.startX, xAxisLinePoint.startY)\n      context.lineTo(xAxisLinePoint.endX, xAxisLinePoint.endY)\n      context.closePath()\n\n      context.lineWidth = xAxisLineWidth\n      context.strokeStyle = xAxisLineColor\n      context.stroke()\n    }\n  }\n\n  console.log('complete drawAxis')\n}\n", "export default function drawAxisRadar() {\n  let { context, opts, chartData } = this\n  let { backgroundColor, radarAxis, categories } = opts\n  let {\n    shape: radarAxisShape,\n    splitNumber,\n    axisName: radarAxisName,\n    axisLine: radarAxisLine,\n    splitLine: radarAxisSplitLine,\n    splitArea: radarAxisSplitArea,\n  } = radarAxis\n  let { show: radarAxisNameShow, textStyle: radarAxisNameTextStyle } = radarAxisName\n  let { show: radarAxisLineShow, lineStyle: radarAxisLineStyle } = radarAxisLine\n  let { show: radarAxisSplitLineShow, lineStyle: radarAxisSplitLineStyle } = radarAxisSplitLine\n\n  let { color: radarAxisNameColor, fontSize: radarAxisNameFontSize } = radarAxisNameTextStyle\n  let { color: radarAxisLineColor, lineWidth: radarAxisLineWidth } = radarAxisLineStyle\n  let { color: radarAxisSplitLineColor, lineWidth: radarAxisSplitLineWidth } = radarAxisSplitLineStyle\n\n  let { odd, even } = radarAxisSplitArea\n  let { show: oddSplitAreaShow, color: oddSplitAreaColor, opacity: oddSplitAreaOpacity } = odd\n  let { show: evenSplitAreaShow, color: evenSplitAreaColor, opacity: evenSplitAreaOpacity } = even\n\n  let { center, radius, lineEndPosition, namePosition } = chartData.radarAxis\n  let [centerX, centerY] = center\n\n  if (radarAxisShape == 'polygon') {\n    lineEndPosition.forEach((splitPositionArr, splitIndex) => {\n      let oddOrEven = (splitNumber - splitIndex) % 2 // 0为偶数，1为奇数\n\n      context.beginPath()\n      splitPositionArr.forEach((splitPositionItem, splitPositionIndex) => {\n        if (splitPositionIndex == 0) {\n          context.moveTo(splitPositionItem.x, splitPositionItem.y)\n        } else {\n          context.lineTo(splitPositionItem.x, splitPositionItem.y)\n        }\n      })\n      context.closePath()\n\n      // draw evenSplitArea\n      if (oddOrEven === 0 && oddSplitAreaShow) {\n        context.fillStyle = backgroundColor\n        context.fill() // 避免存在透明度时，上一次绘制颜色的影响\n        context.save()\n        context.globalAlpha = evenSplitAreaOpacity\n        context.fillStyle = evenSplitAreaColor\n        context.fill()\n        context.restore()\n      }\n\n      // draw oddSplitArea\n      if (oddOrEven === 1 && evenSplitAreaShow) {\n        context.fillStyle = backgroundColor\n        context.fill() // 避免存在透明度时，上一次绘制颜色的影响\n        context.save()\n        context.globalAlpha = oddSplitAreaOpacity\n        context.fillStyle = oddSplitAreaColor == 'auto' ? backgroundColor : oddSplitAreaColor\n        context.fill()\n        context.restore()\n      }\n\n      // draw radarAxisSplitLine\n      if (radarAxisSplitLineShow) {\n        context.lineWidth = radarAxisSplitLineWidth\n        context.strokeStyle = radarAxisSplitLineColor\n        context.stroke()\n      }\n    })\n  } else {\n    for (let index = 0; index < splitNumber; index++) {\n      let scale = (splitNumber - index) / splitNumber\n      let oddOrEven = (splitNumber - index) % 2 // 0为偶数，1为奇数\n\n      context.beginPath()\n      context.arc(centerX, centerY, radius * scale, 0, Math.PI * 2)\n\n      // draw evenSplitArea\n      if (oddOrEven === 0 && oddSplitAreaShow) {\n        context.fillStyle = backgroundColor\n        context.fill() // 避免存在透明度时，上一次绘制颜色的影响\n        context.save()\n        context.globalAlpha = evenSplitAreaOpacity\n        context.fillStyle = evenSplitAreaColor\n        context.fill()\n        context.restore()\n      }\n\n      // draw oddSplitArea\n      if (oddOrEven === 1 && evenSplitAreaShow) {\n        context.fillStyle = backgroundColor\n        context.fill() // 避免存在透明度时，上一次绘制颜色的影响\n        context.save()\n        context.globalAlpha = oddSplitAreaOpacity\n        context.fillStyle = oddSplitAreaColor == 'auto' ? backgroundColor : oddSplitAreaColor\n        context.fill()\n        context.restore()\n      }\n\n      // draw radarAxisSplitLine\n      if (radarAxisSplitLineShow) {\n        context.lineWidth = radarAxisSplitLineWidth\n        context.strokeStyle = radarAxisSplitLineColor\n        context.stroke()\n      }\n    }\n  }\n\n  // draw radarAxisLine\n  if (radarAxisLineShow) {\n    lineEndPosition[0].forEach(lineEndPositionItem => {\n      context.beginPath()\n      context.moveTo(centerX, centerY)\n      context.lineTo(lineEndPositionItem.x, lineEndPositionItem.y)\n      context.lineWidth = radarAxisLineWidth\n      context.strokeStyle = radarAxisLineColor\n      context.stroke()\n    })\n  }\n\n  // draw radarAxisName\n  if (radarAxisNameShow) {\n    namePosition.forEach(namePositionItem => {\n      let { text, point, position } = namePositionItem\n      let { x: pointX } = point\n      let { x: positionX, y: positionY } = position\n\n      context.save()\n      if (positionX == centerX) {\n        context.textAlign = 'center'\n      } else if (pointX > 0) {\n        context.textAlign = 'left'\n      } else if (pointX < 0) {\n        context.textAlign = 'right'\n      }\n\n      context.textBaseline = 'middle'\n\n      context.font = `${radarAxisNameFontSize}px`\n      context.fillStyle = radarAxisNameColor\n      context.fillText(text, positionX, positionY)\n      context.restore()\n    })\n  }\n\n  console.log('complete drawAxisRadar')\n}\n", "import { getColor } from '../../util/util'\n\nexport default function drawChartPie(process) {\n  let { context, opts, chartData } = this\n  let { label: globalLabel, xAxis } = opts\n\n  let { yMaxData, yMinData, xMaxData, xMinData } = chartData.axisData\n\n  let maxData = xAxis.type == 'value' ? xMaxData : yMaxData\n  let minData = xAxis.type == 'value' ? xMinData : yMinData\n\n  if (xAxis.type == 'category') {\n    chartData.chartBar.forEach((barItemArr, barItemArrIndex) => {\n      barItemArr.forEach((barItem, barIndex) => {\n        barItem.forEach((seriesItem, seriesIndex) => {\n          let { x, y, data, barWidth, barHeight, itemStyle } = seriesItem\n          let { color: barItemColor } = itemStyle\n\n          context.save()\n          context.fillStyle = getColor(barItemColor, context, x - barWidth / 2, y - barHeight, x + barWidth, y)\n          if (data >= 0) {\n            context.fillRect(x - barWidth / 2, y, barWidth, -barHeight * process)\n          } else {\n            context.fillRect(x - barWidth / 2, y, barWidth, barHeight * process)\n          }\n\n          context.restore()\n        })\n      })\n    })\n\n    if (process == 1) {\n      chartData.chartBar.forEach((barItemArr, barItemArrIndex) => {\n        barItemArr.forEach((barItem, barIndex) => {\n          barItem.forEach((seriesItem, seriesIndex) => {\n            let { show: barItemShow, x, y, barWidth, barHeight, data, label, itemStyle } = seriesItem\n            let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin, format: labelFormat } = label\n            let { color: barItemColor } = itemStyle\n            const text = labelFormat ? labelFormat(data) : data\n\n            // globalLabel 权重大于 seriesLabel\n            labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n            labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n            labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n            labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n\n            if (labelShow && barItemShow) {\n              context.save()\n              context.font = `${labelFontSize}px`\n              context.strokeStyle = labelColor == 'auto' ? barItemColor : labelColor\n              context.fillStyle = '#ffffff'\n              context.textBaseline = 'middle'\n              context.textAlign = 'center'\n\n              if (data >= 0) {\n                context.strokeText(text, x, y - barHeight / 2)\n                context.fillText(text, x, y - barHeight / 2)\n              } else {\n                context.strokeText(text, x, y + barHeight / 2)\n                context.fillText(text, x, y + barHeight / 2)\n              }\n              context.restore()\n            }\n          })\n        })\n      })\n    }\n  } else {\n    chartData.chartBar.forEach((barItemArr, barItemArrIndex) => {\n      barItemArr.forEach((barItem, barIndex) => {\n        barItem.forEach((seriesItem, seriesIndex) => {\n          let { x, y, data, barWidth, barHeight, itemStyle } = seriesItem\n          let { color: barItemColor } = itemStyle\n\n          context.save()\n          context.fillStyle = barItemColor\n\n          if (data > 0) {\n            context.fillRect(x, y - (barWidth * process) / 2, barHeight, barWidth * process)\n          } else {\n            context.fillRect(x, y - (barWidth * process) / 2, -barHeight, barWidth * process)\n          }\n          context.restore()\n        })\n      })\n    })\n\n    if (process == 1) {\n      chartData.chartBar.forEach((barItemArr, barItemArrIndex) => {\n        barItemArr.forEach((barItem, barIndex) => {\n          barItem.forEach((seriesItem, seriesIndex) => {\n            let { show: barItemShow, x, y, barWidth, barHeight, data, label, itemStyle } = seriesItem\n            let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin, format: labelFormat } = label\n            let { color: barItemColor } = itemStyle\n            const text = labelFormat ? labelFormat(data) : data\n\n            // globalLabel 权重大于 seriesLabel\n            labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n            labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n            labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n            labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n\n            if (labelShow && barItemShow) {\n              context.save()\n              context.font = `${labelFontSize}px`\n              context.strokeStyle = labelColor == 'auto' ? barItemColor : labelColor\n              context.fillStyle = '#ffffff'\n              context.textBaseline = 'middle'\n              context.textAlign = 'center'\n\n              if (data >= 0) {\n                context.strokeText(text, x + barHeight / 2, y)\n                context.fillText(text, x + barHeight / 2, y)\n              } else {\n                context.strokeText(text, x - barHeight / 2, y)\n                context.fillText(text, x - barHeight / 2, y)\n              }\n              context.restore()\n            }\n          })\n        })\n      })\n    }\n  }\n\n  console.log('complete drawChartBar', process)\n}\n", "import cloneDeep from 'lodash.clonedeep'\nimport { getColor } from '../../util/util'\n\nexport default function drawChartLine(process) {\n  let { context, opts, chartData } = this\n  let { label: globalLabel, xAxis } = opts\n\n  let { xStart, xEnd, yStart, yEnd, yZero, xZero, yMaxData, yMinData, xMaxData, xMinData } = chartData.axisData\n\n  let maxData = xAxis.type == 'value' ? xMaxData : yMaxData\n  let minData = xAxis.type == 'value' ? xMinData : yMinData\n\n  function isNotMiddlePoint(dataArr, i) {\n    if (dataArr[i - 1] && dataArr[i + 1]) {\n      return dataArr[i].y >= Math.max(dataArr[i - 1].y, dataArr[i + 1].y) || dataArr[i].y <= Math.min(dataArr[i - 1].y, dataArr[i + 1].y)\n    } else {\n      return false\n    }\n  }\n\n  function drawLine(line, itemStyle) {\n    let { show: lineShow, lineWidth, color: lineColor, opacity: lineOpacity } = line\n    let { color: lineItemColor } = itemStyle\n\n    if (lineShow) {\n      context.save()\n      context.lineJoin = 'round'\n      context.globalAlpha = lineOpacity\n      context.lineWidth = lineWidth\n      context.strokeStyle = lineColor == 'auto' ? lineItemColor : lineColor\n      context.stroke()\n      context.restore()\n    }\n  }\n\n  function drawArea(area, itemStyle, lineStartX, lineStartY, lineEndX, lineEndY) {\n    let { show: areaShow, color: areaColor, opacity: areaOpacity } = area\n    let { color: lineItemColor } = itemStyle\n\n    if (areaShow) {\n      if (xAxis.type == 'category') {\n        if (maxData >= 0 && minData >= 0) {\n          context.lineTo(lineEndX, yStart)\n          context.lineTo(lineStartX, yStart)\n        } else if (maxData <= 0 && minData <= 0) {\n          context.lineTo(lineEndX, yEnd)\n          context.lineTo(lineStartX, yEnd)\n        } else {\n          context.lineTo(lineEndX, yZero)\n          context.lineTo(lineStartX, yZero)\n        }\n      } else {\n        if (maxData >= 0 && minData >= 0) {\n          context.lineTo(xStart, lineEndY)\n          context.lineTo(xStart, lineStartY)\n        } else if (maxData <= 0 && minData <= 0) {\n          context.lineTo(xEnd, lineEndY)\n          context.lineTo(xEnd, lineStartY)\n        } else {\n          context.lineTo(xZero, lineEndY)\n          context.lineTo(xZero, lineStartY)\n        }\n      }\n      context.closePath()\n      context.save()\n      context.globalAlpha = areaOpacity\n      context.fillStyle = getColor(areaColor == 'auto' ? lineItemColor : areaColor, context, xStart, yEnd, xEnd, yStart)\n      context.fill()\n      context.restore()\n    }\n  }\n\n  cloneDeep(chartData.chartLine).forEach(lineItem => {\n    let { itemStyle, line, symbol, area, label, smooth, connectNulls } = lineItem\n    let { color: lineItemColor } = itemStyle\n    let { show: lineShow, lineWidth, color: lineColor, opacity: lineOpacity } = line\n    let { show: symbolShow, type: symbolType, size: symbolSize, color: symbolColor } = symbol\n    let { show: areaShow, color: areaColor, opacity: areaOpacity } = area\n    let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin, format: labelFormat } = label\n\n    let lineStartX, lineStartY, lineEndX, lineEndY\n\n    if (smooth) {\n      // process更新y坐标数据\n      lineItem.data = lineItem.data.map(dataItem => {\n        let { x, y, height, data } = dataItem\n\n        if (xAxis.type == 'category') {\n          if (maxData >= 0 && minData >= 0) {\n            dataItem.y = y + height - height * process\n          } else if (maxData <= 0 && minData <= 0) {\n            dataItem.y = y - height + height * process\n          } else {\n            if (data > 0) {\n              dataItem.y = y + height - height * process\n            } else {\n              dataItem.y = y - height + height * process\n            }\n          }\n        } else {\n          if (maxData >= 0 && minData >= 0) {\n            dataItem.x = x - height + height * process\n          } else if (maxData <= 0 && minData <= 0) {\n            dataItem.x = x + height - height * process\n          } else {\n            if (data > 0) {\n              dataItem.x = x - height + height * process\n            } else {\n              dataItem.x = x + height - height * process\n            }\n          }\n        }\n\n        return dataItem\n      })\n\n      // 获取有效data\n      lineItem.validData = lineItem.data.filter(dataItem => {\n        return typeof dataItem.data == 'number'\n      })\n\n      // 计算贝塞尔曲线控制点并绘制路径\n      let bezierCurveData = connectNulls ? lineItem.validData : lineItem.data\n\n      bezierCurveData.forEach((dataItem, dataIndex, dataArr) => {\n        const a = 0.2\n        const b = 0.2\n        let pAx = null\n        let pAy = null\n        let pBx = null\n        let pBy = null\n        let { x, y, data } = dataItem\n\n        if (typeof data == 'number') {\n          if (lineStartX && lineStartY) {\n            let i = dataIndex - 1\n            if (i < 1) {\n              pAx = dataArr[0].x + (dataArr[1].x - dataArr[0].x) * a\n              pAy = dataArr[0].y + (dataArr[1].y - dataArr[0].y) * a\n            } else {\n              pAx = dataArr[i].x + (dataArr[i + 1].x - dataArr[i - 1].x) * a\n              pAy = dataArr[i].y + (dataArr[i + 1].y - dataArr[i - 1].y) * a\n            }\n\n            if (i > dataArr.length - 3) {\n              let last = dataArr.length - 1\n              pBx = dataArr[last].x - (dataArr[last].x - dataArr[last - 1].x) * b\n              pBy = dataArr[last].y - (dataArr[last].y - dataArr[last - 1].y) * b\n            } else {\n              pBx = dataArr[i + 1].x - (dataArr[i + 2].x - dataArr[i].x) * b\n              pBy = dataArr[i + 1].y - (dataArr[i + 2].y - dataArr[i].y) * b\n            }\n\n            if (isNotMiddlePoint(dataArr, i + 1)) {\n              pBy = dataArr[i + 1].y\n            }\n            if (isNotMiddlePoint(dataArr, i)) {\n              pAy = dataArr[i].y\n            }\n\n            context.bezierCurveTo(pAx, pAy, pBx, pBy, x, y)\n\n            lineEndX = x\n            lineEndY = y\n          } else {\n            context.beginPath()\n            context.moveTo(x, y)\n            lineStartX = x\n            lineStartY = y\n          }\n        }\n\n        if ((!connectNulls && typeof data !== 'number') || dataIndex + 1 == dataArr.length) {\n          if (lineEndX && lineEndY) {\n            drawLine(line, itemStyle)\n            drawArea(area, itemStyle, lineStartX, lineStartY, lineEndX, lineEndY)\n            lineEndX = null\n            lineEndY = null\n          }\n          lineStartX = null\n          lineStartY = null\n        }\n      })\n    } else {\n      lineItem.data.forEach((dataItem, dataIndex, dataArr) => {\n        let { x, y, height, data } = dataItem\n\n        if (xAxis.type == 'category') {\n          if (maxData >= 0 && minData >= 0) {\n            y = y + height - height * process\n          } else if (maxData <= 0 && minData <= 0) {\n            y = y - height + height * process\n          } else {\n            if (data > 0) {\n              y = y + height - height * process\n            } else {\n              y = y - height + height * process\n            }\n          }\n        } else {\n          if (maxData >= 0 && minData >= 0) {\n            x = x - height + height * process\n          } else if (maxData <= 0 && minData <= 0) {\n            x = x + height - height * process\n          } else {\n            if (data > 0) {\n              x = x - height + height * process\n            } else {\n              x = x + height - height * process\n            }\n          }\n        }\n\n        if (typeof data == 'number') {\n          if (lineStartX && lineStartY) {\n            context.lineTo(x, y)\n            lineEndX = x\n            lineEndY = y\n          } else {\n            context.beginPath()\n            context.moveTo(x, y)\n            lineStartX = x\n            lineStartY = y\n          }\n        }\n\n        if ((!connectNulls && typeof data !== 'number') || dataIndex + 1 == dataArr.length) {\n          if (lineEndX && lineEndY) {\n            drawLine(line, itemStyle)\n            drawArea(area, itemStyle, lineStartX, lineStartY, lineEndX, lineEndY)\n            lineStartX = null\n            lineStartY = null\n            lineEndX = null\n            lineEndY = null\n          }\n        }\n      })\n    }\n\n    if (process == 1) {\n      if (symbolShow) {\n        context.save()\n        lineItem.data.forEach(dataItem => {\n          let { x, y, data } = dataItem\n\n          if (typeof data !== 'number') return\n\n          switch (symbolType) {\n            case 'circle':\n              context.beginPath()\n              context.arc(x, y, symbolSize / 2, 0, 2 * Math.PI)\n              context.fillStyle = symbolColor == 'auto' ? lineItemColor : symbolColor\n              context.fill()\n\n              context.beginPath()\n              context.arc(x, y, symbolSize / 4, 0, 2 * Math.PI)\n              context.fillStyle = '#ffffff'\n              context.fill()\n              break\n          }\n        })\n        context.restore()\n      }\n\n      // globalLabel 权重大于 seriesLabel\n      labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n      labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n      labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n      labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n\n      if (labelShow) {\n        context.save()\n        context.font = `${labelFontSize}px`\n        context.fillStyle = labelColor == 'auto' ? lineItemColor : labelColor\n        context.textAlign = 'center'\n\n        lineItem.data.forEach(dataItem => {\n          let { x, y, data } = dataItem\n\n          if (typeof data !== 'number') return\n\n          const text = labelFormat ? labelFormat(data) : data\n\n          if (xAxis.type == 'category') {\n            if (maxData >= 0 && minData >= 0) {\n              context.textBaseline = 'bottom'\n              context.fillText(text, x, y - labelMargin)\n            } else if (maxData <= 0 && minData <= 0) {\n              context.textBaseline = 'top'\n              context.fillText(text, x, y + labelMargin)\n            } else {\n              if (data) {\n                context.textBaseline = 'bottom'\n                context.fillText(text, x, y - labelMargin)\n              } else {\n                context.textBaseline = 'top'\n                context.fillText(text, x, y + labelMargin)\n              }\n            }\n          } else {\n            context.textBaseline = 'bottom'\n            context.fillText(data, x, y - labelMargin)\n          }\n        })\n\n        context.restore()\n      }\n    }\n  })\n\n  console.log('complete drawChartLine', process)\n}\n", "import { avoidCollision, convertCoordinateOrigin } from '../../util/util'\n\nexport default function drawChartPie(process) {\n  let { context, opts, chartData } = this\n  let { backgroundColor, label: globalLabel } = opts\n  let { data, center, radius, offsetAngle, disablePieStroke, valueSum, maxData, roseType } = chartData.chartPie\n  let [centerX, centerY] = center\n  let [radiusMin, radiusMax] = radius\n  let _start_ = offsetAngle !== 0 ? (offsetAngle * Math.PI) / 180 : 0\n\n  data.forEach((dataItem, dataIndex) => {\n    dataItem._start_ = _start_\n\n    if (roseType == 'area') {\n      dataItem._proportion_ = (1 / data.length) * process\n    } else {\n      dataItem._proportion_ = (dataItem.value / valueSum) * process\n    }\n    dataItem._end_ = _start_ + 2 * dataItem._proportion_ * Math.PI\n\n    let radius = radiusMax\n    if (roseType == 'radius' || roseType == 'area') {\n      radius = radiusMin + ((radiusMax - radiusMin) * dataItem.value) / maxData\n    }\n    dataItem.radius = radius\n\n    context.beginPath()\n    context.moveTo(centerX, centerY)\n    context.arc(centerX, centerY, radius, dataItem._start_, dataItem._end_)\n    context.lineWidth = 2\n    context.strokeStyle = backgroundColor\n    context.fillStyle = dataItem.itemStyle.color\n    context.fill()\n    if (!disablePieStroke) {\n      context.stroke()\n    }\n\n    if (radiusMin > 0) {\n      context.beginPath()\n      context.moveTo(centerX, centerY)\n      context.arc(centerX, centerY, radiusMin, dataItem._start_, dataItem._end_)\n      context.fillStyle = backgroundColor\n      context.strokeStyle = backgroundColor\n      context.stroke()\n      context.fill()\n    }\n\n    _start_ = dataItem._end_\n  })\n\n  // 绘制文本标签\n  if (process == 1) {\n    let { label: seriesLabel, labelLine, title } = chartData.chartPie\n    let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin, format: labelFormat } = seriesLabel\n    let { length1, length2, lineWidth, lineDotRadius } = labelLine\n    let lineRadius = radiusMax + length1\n    let lastOrigin = null\n    let { show: titleShow, text, textStyle, subtext, subtextStyle, itemGap, backgroundColor, borderColor, borderWidth } = title\n    let { fontSize: textFontSize, color: textColor, lineHeight: textLineHeight } = textStyle\n    let { fontSize: subtextFontSize, color: subtextColor, lineHeight: subtextLineHeight } = subtextStyle\n\n    // globalLabel 权重大于 seriesLabel\n    labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n    labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n    labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n    labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n    labelFormat = globalLabel && globalLabel.format ? globalLabel.format : labelFormat\n\n    if (labelShow) {\n      data.forEach((dataItem, dataIndex) => {\n        let arc = 2 * Math.PI - (dataItem._start_ + (2 * Math.PI * dataItem._proportion_) / 2)\n        let text = labelFormat\n          ? labelFormat({ name: dataItem.name, value: dataItem.value, percent: ((dataItem.value / valueSum) * 100).toFixed(2) })\n          : `${((dataItem.value / valueSum) * 100).toFixed(2)}%`\n\n        // length1 start\n        let length1StartOrigin = {\n          x: Math.cos(arc) * dataItem.radius,\n          y: Math.sin(arc) * dataItem.radius,\n        }\n        // length2 start\n        let length2StartOrigin = {\n          x: Math.cos(arc) * lineRadius,\n          y: Math.sin(arc) * lineRadius,\n        }\n        // length2 end\n        let length2EndOrigin = {\n          x: length2StartOrigin.x >= 0 ? length2StartOrigin.x + length2 : length2StartOrigin.x - length2,\n          y: length2StartOrigin.y,\n        }\n\n        length2EndOrigin = avoidCollision(length2EndOrigin, lastOrigin, Math.max(lineDotRadius, labelFontSize / 2) * 2)\n        lastOrigin = length2EndOrigin\n\n        let length1StartPosition = convertCoordinateOrigin(length1StartOrigin, center)\n        let length2StartPosition = convertCoordinateOrigin(length2StartOrigin, center)\n        let length2EndPosition = convertCoordinateOrigin(length2EndOrigin, center)\n\n        // text start\n        context.font = `${labelFontSize}px`\n        let textWidth = context.measureText(text).width\n        let textStartPosition = Object.assign({}, length2EndPosition)\n        if (length2EndOrigin.x > 0) {\n          textStartPosition.x += lineDotRadius + labelMargin\n        } else {\n          textStartPosition.x -= textWidth + lineDotRadius + labelMargin\n        }\n\n        context.beginPath()\n        context.moveTo(length1StartPosition.x, length1StartPosition.y)\n        context.quadraticCurveTo(length2StartPosition.x, length2StartPosition.y, length2EndPosition.x, length2EndPosition.y)\n        context.lineWidth = lineWidth\n        context.strokeStyle = dataItem.itemStyle.color\n        context.stroke()\n        context.closePath()\n\n        context.beginPath()\n        context.moveTo(length2EndPosition.x, length2EndPosition.y)\n        context.arc(length2EndPosition.x, length2EndPosition.y, lineDotRadius, 0, 2 * Math.PI)\n        context.closePath()\n        context.fillStyle = dataItem.itemStyle.color\n        context.fill()\n\n        context.font = `${labelFontSize}px`\n        context.textBaseline = 'middle'\n        context.fillStyle = labelColor == 'auto' ? dataItem.itemStyle.color : labelColor\n        context.fillText(text, textStartPosition.x, textStartPosition.y)\n      })\n    }\n\n    if (titleShow) {\n      const textStrArr = text.split('\\n').filter(item => !!item)\n      const subtextStrArr = subtext.split('\\n').filter(item => !!item)\n\n      let titleY = centerY\n      let titleHeight = textLineHeight * textStrArr.length + subtextLineHeight * subtextStrArr.length\n\n      if (subtext) {\n        titleHeight += itemGap\n      }\n      titleY -= titleHeight / 2\n\n      context.save()\n      context.textAlign = 'center'\n      context.textBaseline = 'middle'\n\n      if (text) {\n        titleY += textLineHeight / 2\n        context.font = `${textFontSize}px`\n        context.fillStyle = textColor\n        textStrArr.forEach((text, index, arr) => {\n          context.fillText(text, centerX, titleY)\n          if (index + 1 == arr.length) {\n            titleY += textLineHeight / 2\n          } else {\n            titleY += textLineHeight\n          }\n        })\n        titleY += itemGap\n      }\n\n      if (subtext) {\n        titleY += subtextLineHeight / 2\n        context.font = `${subtextFontSize}px`\n        context.fillStyle = subtextColor\n        subtextStrArr.forEach(text => {\n          context.fillText(text, centerX, titleY)\n          titleY += subtextLineHeight\n        })\n      }\n      context.restore()\n    }\n  }\n\n  console.log('complete drawChartPie', process)\n}\n", "import { convertCoordinateOrigin } from '../../util/util'\nimport cloneDeep from 'lodash.clonedeep'\n\nexport default function drawChartRadar(process) {\n  let { context, opts, chartData } = this\n  let { label: globalLabel } = opts\n  let { center } = chartData.radarAxis\n\n  chartData.chartRadar.forEach(radarItem => {\n    let { dataPosition, itemStyle, area, line, symbol, label } = radarItem\n    let { show: areaShow, color: areaColor, opacity: areaOpactiy } = area\n    let { show: lineShow, lineWidht, color: lineColor, opacity: lineOpacity } = line\n    let { show: symbolShow, type: symbolType, size: symbolSize, color: symbolColor } = symbol\n    let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin } = label\n\n    context.beginPath()\n    dataPosition.forEach((dataItem, dataIndex) => {\n      let point = dataItem.point\n      let _point = { x: point.x * process, y: point.y * process }\n      let position = convertCoordinateOrigin(_point, center)\n      let { x: positionX, y: positionY } = position\n      dataItem.position = position\n\n      if (dataIndex == 0) {\n        context.moveTo(positionX, positionY)\n      } else {\n        context.lineTo(positionX, positionY)\n      }\n    })\n    context.closePath()\n\n    if (areaShow) {\n      context.save()\n      context.globalAlpha = areaOpactiy\n      context.fillStyle = areaColor == 'auto' ? itemStyle.color : areaColor\n      context.fill()\n      context.stroke()\n      context.restore()\n    }\n\n    if (lineShow) {\n      context.save()\n      context.lineWidht = lineWidht\n      context.globalAlpha = lineOpacity\n      context.strokeStyle = lineColor == 'auto' ? itemStyle.color : lineColor\n      context.stroke()\n      context.restore()\n    }\n\n    if (process == 1) {\n      if (symbolShow) {\n        switch (symbolType) {\n          case 'circle':\n            context.save()\n            dataPosition.forEach(dataItem => {\n              let { x: positionX, y: positionY } = dataItem.position\n              context.beginPath()\n              context.arc(positionX, positionY, symbolSize / 2, 0, Math.PI * 2)\n              context.fillStyle = symbolColor == 'auto' ? itemStyle.color : symbolColor\n              context.fill()\n\n              context.beginPath()\n              context.arc(positionX, positionY, symbolSize / 4, 0, Math.PI * 2)\n              context.fillStyle = '#fff'\n              context.fill()\n            })\n            context.restore()\n            break\n        }\n      }\n\n      // globalLabel 权重大于 seriesLabel\n      labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n      labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n      labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n      labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n\n      if (labelShow) {\n        context.save()\n        context.font = `${labelFontSize}px`\n        context.fillStyle = labelColor == 'auto' ? itemStyle.color : labelColor\n        context.textAlign = 'center'\n        context.textBaseline = 'bottom'\n\n        dataPosition.forEach(dataItem => {\n          let { x, y } = dataItem.position\n          context.fillText(dataItem.data, x, y - labelMargin)\n        })\n      }\n    }\n  })\n\n  console.log('complete drawChartRadar', process)\n}\n", "export default function drawChartScatter(process) {\n  let { context, opts, chartData } = this\n  let { label: globalLabel } = opts\n\n  chartData.chartScatter.forEach(scatterItem => {\n    let { name: scatterItemName, data, label, itemStyle, opacity, lineWidth, strokeColor } = scatterItem\n    let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin } = label\n    let { color: scatterItemColor } = itemStyle\n\n    data.forEach(dataItem => {\n      let { positionX, positionY, radius, color: dataItemColor } = dataItem\n      context.save()\n      context.beginPath()\n      context.arc(positionX, positionY, radius * process, 0, Math.PI * 2)\n      if (lineWidth > 0) {\n        context.strokeStyle = strokeColor == 'auto' ? dataItemColor : strokeColor\n        context.lineWidth = 0\n        context.stroke()\n      }\n      context.fillStyle = dataItemColor\n      context.globalAlpha = opacity\n      context.fill()\n      context.restore()\n    })\n\n    if (process == 1) {\n      // globalLabel 权重大于 seriesLabel\n      labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n      labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n      labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n      labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n\n      if (labelShow) {\n        context.save()\n        context.font = `${labelFontSize}px`\n        context.fillStyle = labelColor == 'auto' ? scatterItemColor : labelColor\n\n        data.forEach(dataItem => {\n          let { y, z, radius, name, positionX, positionY } = dataItem\n          let text = name ? name : z ? z : scatterItemName\n\n          if (typeof scatterItemColor !== 'string') {\n            context.textAlign = 'center'\n            context.textBaseline = 'bottom'\n            context.fillText(text, positionX, positionY - radius - labelMargin)\n          } else {\n            context.textAlign = 'center'\n            context.textBaseline = 'middle'\n            context.fillText(text, positionX, positionY)\n          }\n        })\n        context.restore()\n      }\n    }\n  })\n\n  console.log('complete drawChartScatter', process)\n}\n", "export default function drawChartPie(process) {\n  let { context, opts, chartData } = this\n  let { data, funnelAlign, itemStyle, label: seriesLabel } = chartData.chartFunnel\n  let { borderColor, borderWidth } = itemStyle\n\n  data.forEach(dataItem => {\n    let { point, itemStyle: dataItemStyle } = dataItem\n    let { color: dataItemColor } = dataItemStyle\n\n    context.beginPath()\n    point.forEach((pointItem, pointIndex) => {\n      let { x, y } = pointItem\n      if (pointIndex == 0) {\n        context.moveTo(x, y * process)\n      } else {\n        context.lineTo(x, y * process)\n      }\n    })\n    context.closePath()\n\n    if (borderWidth > 0) {\n      context.strokeStyle = borderColor\n      context.lineWidth = borderWidth\n      context.stroke()\n    }\n    context.fillStyle = dataItemColor\n    context.fill()\n  })\n\n  // 绘制文本标签\n  if (process == 1) {\n    let { label: globalLabel } = opts\n    let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin, position: labelPosition } = seriesLabel\n    // globalLabel 权重大于 seriesLabel\n    labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n    labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n    labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n    labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n\n    if (labelShow) {\n      context.save()\n      data.forEach(dataItem => {\n        let { name, itemStyle: dataItemStyle, textPoint } = dataItem\n        let { x, y } = textPoint\n\n        if (labelPosition == 'inside') {\n          context.textAlign = 'center'\n          context.textBaseline = 'middle'\n          context.font = `${labelFontSize}px`\n          context.strokeStyle = dataItemStyle.color\n          context.fillStyle = '#ffffff'\n          context.strokeText(name, x, y)\n          context.fillText(name, x, y)\n        } else {\n          if (funnelAlign == 'right') {\n            // 右对齐\n            context.textAlign = 'right'\n          } else {\n            // 左对齐 左右对称\n            context.textAlign = 'left'\n          }\n          context.textBaseline = 'middle'\n          context.font = `${labelFontSize}px`\n          context.fillStyle = labelColor == 'auto' ? dataItemStyle.color : labelColor\n          context.fillText(name, x, y)\n        }\n      })\n      context.restore()\n    }\n  }\n\n  console.log('complete drawChartFunnel', process)\n}\n", "import cloneDeep from 'lodash.clonedeep'\n\nexport default function drawChartCandlestick(process) {\n  let { context, opts, chartData, seriesMap } = this\n  let seriesCandlestick = cloneDeep(seriesMap['candlestick'])\n  let { highLine: seriesHighLine, lowLine: seriesLowLine, bar: seriesBar } = seriesCandlestick[0]\n  let { rect, highLine, lowhLine, bar } = chartData.chartCandlestick\n\n  context.save()\n  rect.forEach(item => {\n    let { color, bordercolor, opacity, borderWidth, rectPoint, upLinePoint, downLinePoint } = item\n    let { x, y, width, height } = rectPoint\n    let { startX: upLineStartX, startY: upLineStartY, endX: upLineEndX, endY: upLineEndY } = upLinePoint\n    let { startX: downLineStartX, startY: downLineStartY, endX: downLineEndX, endY: downLineEndY } = downLinePoint\n\n    context.strokeStyle = bordercolor\n    context.fillStyle = color\n    context.globalAlpha = opacity\n    context.strokeRect(x + borderWidth / 2, y + borderWidth / 2, width - borderWidth, height - borderWidth)\n    context.fillRect(x, y, width, height)\n\n    context.beginPath()\n    context.lineWidth = 1\n    context.strokeStyle = bordercolor\n    context.moveTo(upLineStartX, upLineStartY)\n    context.lineTo(upLineEndX, upLineEndY)\n    context.stroke()\n\n    context.moveTo(downLineStartX, downLineStartY)\n    context.lineTo(downLineEndX, downLineEndY)\n    context.stroke()\n  })\n  context.restore()\n\n  if (process == 1) {\n    let { show: highLineShow, lineStyle: highLineStyle } = seriesHighLine\n    let { show: lowLineShow, lineStyle: lowLineStyle } = seriesLowLine\n    let { show: barShow, itemStyle: barItemStyle, lineStyle: barLineStyle } = seriesBar\n    let { color: highLineColor, lineWidth: highLineWidth, lineDash: highLineDash, opacity: highLineOpacity } = highLineStyle\n    let { color: lowLineColor, lineWidth: lowLineWidth, lineDash: lowLineDash, opacity: lowLineOpacity } = lowLineStyle\n    let { startX: highLineStartX, startY: highLineStartY, endX: highLineEndX, endY: highLineEndY } = highLine\n    let { startX: lowLineStartX, startY: lowLineStartY, endX: lowLineEndX, endY: lowLineEndY } = lowhLine\n    let { color: barColor, opacity: barOpacity } = barItemStyle\n    let { lineWidth: barLineWidth, lineColor: barLineColor } = barLineStyle\n\n    if (highLineShow) {\n      context.save()\n      context.beginPath()\n      context.moveTo(highLineStartX, highLineStartY)\n      context.lineTo(highLineEndX, highLineEndY)\n      context.strokeStyle = highLineColor\n      context.lineWidth = highLineWidth\n      context.setLineDash(highLineDash)\n      context.globalAlpha = highLineOpacity\n      context.stroke()\n      context.restore()\n    }\n\n    if (lowLineShow) {\n      context.save()\n      context.beginPath()\n      context.moveTo(lowLineStartX, lowLineStartY)\n      context.lineTo(lowLineEndX, lowLineEndY)\n      context.strokeStyle = lowLineColor\n      context.lineWidth = lowLineWidth\n      context.setLineDash(lowLineDash)\n      context.globalAlpha = lowLineOpacity\n      context.stroke()\n      context.restore()\n    }\n\n    if (barShow) {\n      const { lineStartX, lineStartY, lineEndX, lineEndY, data } = bar\n\n      context.save()\n      context.lineWidth = barLineWidth\n      context.strokeStyle = barLineColor\n      context.moveTo(lineStartX, lineStartY)\n      context.lineTo(lineEndX, lineEndY)\n      context.stroke()\n      context.restore()\n\n      data.forEach(barItem => {\n        let { color, x, y, width, height } = barItem\n\n        context.save()\n        context.beginPath()\n        context.fillStyle = barColor == 'auto' ? color : barColor\n        context.globalAlpha = barOpacity\n        context.fillRect(x, y, width, -height)\n        context.restore()\n      })\n    }\n  }\n\n  console.log('complete drawChartCandlestick', process)\n}\n", "export default function drawChartHeatmap(process) {\n  let { context, opts, chartData } = this\n  let { xEachSpacing, yEachSpacing } = chartData.axisData\n  let { label: globalLabel } = opts\n\n  const xSplitLineWidth = opts.xAxis.axisSplitLine.lineStyle.lineWidth\n  const ySplitLineWidth = opts.yAxis.axisSplitLine.lineStyle.lineWidth\n\n  chartData.chartHeatmap.forEach(HeatmapItem => {\n    let { data, label } = HeatmapItem\n    let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin } = label\n\n    data.forEach(dataItem => {\n      let { positionX, positionY, color: dataItemColor, useSplit } = dataItem\n      context.save()\n      context.beginPath()\n      if (useSplit) {\n        context.rect(positionX + ySplitLineWidth * 2, positionY + xSplitLineWidth * 2, xEachSpacing - ySplitLineWidth * 4, yEachSpacing - xSplitLineWidth * 4)\n      } else {\n        context.rect(positionX, positionY, xEachSpacing, yEachSpacing)\n      }\n      context.fillStyle = dataItemColor\n      context.globalAlpha = process\n      context.fill()\n      context.restore()\n    })\n\n    if (process == 1) {\n      // globalLabel 权重大于 seriesLabel\n      labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n      labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n      labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n      labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n\n      if (labelShow) {\n        context.save()\n        context.font = `${labelFontSize}px`\n        context.fillStyle = labelColor == 'auto' ? '#ffffff' : labelColor\n        context.textAlign = 'center'\n        context.textBaseline = 'middle'\n\n        data.forEach(dataItem => {\n          let { positionX, positionY } = dataItem\n          let text = dataItem[2]\n          context.fillText(text, positionX + xEachSpacing / 2, positionY + yEachSpacing / 2)\n        })\n        context.restore()\n      }\n    }\n  })\n  console.log('complete drawChartHeatmap', process)\n}\n", "// import cloneDeep from 'lodash.clonedeep'\n\nexport default function drawChartTreemap(process) {\n  let { context, opts, chartData } = this\n  let { label: globalLabel } = opts\n  let { label: seriesLabel, splitLine } = chartData.chartTreemap.data\n  let { show: labelShow, fontSize: labelFontSize, color: labelColor, margin: labelMargin } = seriesLabel\n  let { show: splitLineShow, lineWidth: splitLineWidth, color: splitLineColor } = splitLine\n\n  context.save()\n  chartData.chartTreemap.children.forEach((item, index) => {\n    let { x0, y0, x1, y1, data } = item\n    let width = x1 - x0\n    let height = y1 - y0\n\n    context.fillStyle = data.itemStyle.color\n    context.globalAlpha = process\n    if (splitLineShow) {\n      context.lineWidth = splitLineWidth\n      context.strokeStyle = splitLineColor\n      context.strokeRect(x0, y0, width, height)\n    }\n    context.fillRect(x0, y0, width, height)\n  })\n  context.restore()\n\n  // 绘制文本标签\n  if (process == 1) {\n    // globalLabel 权重大于 seriesLabel\n    labelShow = globalLabel && typeof globalLabel.show == 'boolean' ? globalLabel.show : labelShow\n    labelFontSize = globalLabel && globalLabel.fontSize ? globalLabel.fontSize : labelFontSize\n    labelColor = globalLabel && globalLabel.color ? globalLabel.color : labelColor\n    labelMargin = globalLabel && globalLabel.margin ? globalLabel.margin : labelMargin\n\n    if (labelShow) {\n      context.save()\n      chartData.chartTreemap.children.forEach(item => {\n        let { x0, y0, x1, y1, data } = item\n        let { name, itemStyle } = data\n        let x = x0 + (x1 - x0) / 2\n        let y = y0 + (y1 - y0) / 2\n\n        let min = Math.min(x1 - x0, y1 - y0)\n\n        context.textAlign = 'center'\n        context.textBaseline = 'middle'\n        context.font = `${min * 0.2}px`\n        context.strokeStyle = labelColor == 'auto' ? itemStyle.color : labelColor\n        context.fillStyle = '#ffffff'\n        context.strokeText(name, x, y)\n        context.fillText(name, x, y)\n      })\n      context.restore()\n    }\n  }\n\n  console.log('complete drawChartTreemap', process)\n}\n", "// import cloneDeep from 'lodash.clonedeep'\n\nexport default function drawChartTagCloud(process) {\n  let { opts, chartData } = this\n  let { element, width, height } = opts\n\n  element.width = width\n  element.height = height\n  const context = element.getContext('2d')\n\n  chartData.chartTagCloud.data.forEach(item => {\n    let { text, x, y, font, size, rotate, itemStyle } = item\n\n    context.save()\n    context.beginPath()\n    context.font = `${size}px ${font}`\n    context.fillStyle = itemStyle.color\n    context.textAlign = 'center'\n    context.translate(width / 2 + x, height / 2 + y)\n    context.rotate((rotate * Math.PI) / 180)\n    context.fillText(text, 0, 0)\n    context.restore()\n  })\n\n  console.log('complete drawChartTagCloud', process)\n}\n", "import Animation from '../../util/animation'\n\nimport drawTooltip from '../drawTooltip'\nimport drawBackground from '../drawBackground'\nimport drawLegend from '../drawLegend'\nimport drawAxis from '../drawAxis'\nimport drawAxisRadar from '../drawAxisRadar'\n\nimport drawChartBar from './drawChartBar'\nimport drawChartLine from './drawChartLine'\nimport drawChartPie from './drawChartPie'\nimport drawChartRadar from './drawChartRadar'\nimport drawChartScatter from './drawChartScatter'\nimport drawChartFunnel from './drawChartFunnel'\nimport drawChartCandlestick from './drawChartCandlestick'\nimport drawChartHeatmap from './drawChartHeatmap'\nimport drawChartTreemap from './drawChartTreemap'\nimport drawChartTagCloud from './drawChartTagCloud'\n\nexport default function drawCharts() {\n  const { animation, animationDuration, animationTiming } = this.opts\n  this.animationInstance && this.animationInstance.stop()\n\n  this.animationInstance = new Animation({\n    animation,\n    animationDuration,\n    animationTiming,\n    onProcess: process => {\n      // 绘制图表\n\n      drawBackground.call(this) // 绘制背景\n      if (this.seriesMap.line || this.seriesMap.bar || this.seriesMap.scatter || this.seriesMap.candlestick || this.seriesMap.heatmap) {\n        drawAxis.call(this) // 有相同xy轴的图表，只绘制一次\n      }\n\n      Object.keys(this.seriesMap).forEach(type => {\n        switch (type) {\n          case 'bar':\n            drawChartBar.call(this, process)\n            break\n          case 'line':\n            drawChartLine.call(this, process)\n            break\n          case 'pie':\n            drawChartPie.call(this, process)\n            break\n          case 'radar':\n            drawAxisRadar.call(this)\n            drawChartRadar.call(this, process)\n            break\n          case 'scatter':\n            drawChartScatter.call(this, process)\n            break\n          case 'funnel':\n            drawChartFunnel.call(this, process)\n            break\n          case 'candlestick':\n            drawChartCandlestick.call(this, process)\n            break\n          case 'heatmap':\n            drawChartHeatmap.call(this, process)\n            break\n          case 'treemap':\n            drawChartTreemap.call(this, process)\n            break\n          case 'tagCloud':\n            drawChartTagCloud.call(this, process)\n            break\n        }\n      })\n\n      if (process == 1) {\n        drawLegend.call(this) // 绘制图例\n        drawTooltip.call(this) // 绘制tooltip\n      }\n    },\n    onAnimationFinish: () => {\n      this.event.trigger('renderComplete')\n    },\n  })\n}\n", "import Event from './util/event'\nimport Config from './config'\nimport { replenishData, calSeries } from './calculate/calOptions'\nimport { getAxisChartCurrentIndex, getPieChartCurrentIndex, getRadarChartCurrentIndex } from './calculate/calCurrentIndex'\nimport {\n  calAxisPointerData,\n  calTooltipContainerData,\n  calBarChartTooltipData,\n  calLineChartTooltipData,\n  calPieChartTooltipData,\n  calRadarChartTooltipData,\n  calCandlestickChartTooltipData,\n} from './calculate/calTooltipData'\nimport calChartsData from './calculate/calChartsData'\nimport drawCharts from './draw/drawCharts/drawCharts'\nimport cloneDeep from 'lodash.clonedeep'\n\nclass Charts {\n  constructor(opts = {}) {\n    this.config = Object.assign({}, Config)\n    this.opts = Object.assign({}, opts)\n    this.context = this.opts.element.getContext('2d')\n    this.tooltipData = {\n      tooltipTitle: '',\n      data: [],\n      maxTextWidth: 0,\n      offset: {},\n    }\n    this.legendData = {}\n    this.seriesMap = {}\n    this.chartData = {}\n\n    // 绑定事件\n    this.event = new Event()\n    this.event.addEventListener('renderComplete', opts.onRenderComplete)\n\n    // 计算图表数据\n    calChartsData.call(this)\n\n    // 绘制图表\n    drawCharts.call(this)\n  }\n\n  updateData(updateOpts = {}) {\n    Object.keys(updateOpts).forEach(key => {\n      if (key == 'series') {\n        this.opts.series = cloneDeep(updateOpts.series)\n        calSeries.call(this)\n      } else {\n        replenishData(updateOpts, key, this.opts, key, true)\n      }\n    })\n\n    console.log('complete updateData', this)\n\n    // 计算图表数据\n    calChartsData.call(this)\n\n    // 绘制图表\n    drawCharts.call(this)\n  }\n\n  showTooltip(e) {\n    const { animation, tooltip } = this.opts\n    const animationCache = animation\n\n    if (!tooltip.show) return\n\n    const { currentData, offset } = this.getCurrentIndex(e)\n\n    this.tooltipData = {\n      tooltipTitle: '',\n      data: [],\n      maxTextWidth: 0,\n      offset,\n    }\n\n    Object.keys(currentData).forEach(type => {\n      const currentIndex = currentData[type]\n      switch (type) {\n        case 'bar':\n          calBarChartTooltipData.call(this, currentIndex)\n          break\n        case 'line':\n          calLineChartTooltipData.call(this, currentIndex)\n          break\n        case 'pie':\n          calPieChartTooltipData.call(this, currentIndex)\n          break\n        case 'radar':\n          calRadarChartTooltipData.call(this, currentIndex)\n          break\n        case 'candlestick':\n        case 'k':\n          calCandlestickChartTooltipData.call(this, currentIndex)\n          break\n      }\n    })\n\n    Object.keys(currentData).forEach(type => {\n      switch (type) {\n        case 'bar':\n        case 'line':\n        case 'candlestick':\n        case 'k':\n          if (!this.tooltipData.axisPointerData) {\n            calAxisPointerData.call(this, currentData[type])\n          }\n          break\n      }\n    })\n\n    calTooltipContainerData.call(this)\n\n    console.log('complete showTooltip', this.tooltipData)\n\n    this.opts.animation = false\n    drawCharts.call(this)\n    this.opts.animation = animationCache\n  }\n\n  hideTooltip() {\n    const { opts, tooltipData } = this\n    const { animation } = opts\n    const animationCache = animation\n\n    this.tooltipData = {\n      tooltipTitle: '',\n      data: [],\n      maxTextWidth: 0,\n      offset: {},\n    }\n\n    console.log('complete hideTooltip', tooltipData)\n\n    this.opts.animation = false\n    drawCharts.call(this)\n    this.opts.animation = animationCache\n  }\n\n  getCurrentIndex(e) {\n    const touches = e.touches && e.touches.length ? e.touches : e.changedTouches\n    const offset = { x: touches[0].offsetX || 0, y: touches[0].offsetY || 0 }\n    let currentData = {}\n\n    Object.keys(this.seriesMap).forEach(type => {\n      switch (type) {\n        case 'bar':\n        case 'line':\n        case 'candlestick':\n        case 'k':\n          currentData[type] = getAxisChartCurrentIndex.call(this, offset)\n          break\n        case 'pie':\n          currentData[type] = getPieChartCurrentIndex.call(this, offset)\n          break\n        case 'radar':\n          currentData[type] = getRadarChartCurrentIndex.call(this, offset)\n          break\n      }\n    })\n\n    console.log('complete getCurrentIndex', currentData)\n\n    return {\n      currentData,\n      offset,\n    }\n  }\n}\n\nexport default Charts\n"], "names": ["Event", "[object Object]", "this", "events", "type", "listener", "push", "params", "for<PERSON>ach", "apply", "e", "console", "error", "animation", "animationDuration", "animationTiming", "backgroundColor", "colors", "tooltip", "show", "data", "maxTextWidth", "backgroundRadius", "backgroundOpacity", "padding", "itemGap", "iconRadius", "iconGap", "textStyle", "fontSize", "color", "lineHeight", "axisPointer", "lineStyle", "lineWidth", "opacity", "shadowStyle", "cross", "lineColor", "lineDash", "lineOpacity", "fontColor", "fontPadding", "label", "margin", "legend", "marginTop", "shapeRadius", "shapeWidth", "shapeHeight", "yAxisCategory", "boundaryGap", "axisName", "text", "gap", "align", "axisLabel", "axisTick", "alignWithLabel", "length", "axisLine", "axisSplitLine", "yAxisValue", "max", "min", "splitNumber", "xAxisCategory", "rotate", "xAxisValue", "radarAxis", "shape", "center", "radius", "splitLine", "splitArea", "odd", "even", "bar", "barMaxWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "barGap", "line", "smooth", "connectNulls", "symbol", "size", "area", "pie", "roseType", "offsetAngle", "disablePieStroke", "labelLine", "lineDotRadius", "length1", "length2", "title", "subtext", "subtextStyle", "radar", "scatter", "strokeColor", "funnel", "width", "height", "top", "left", "right", "bottom", "sort", "funnelAlign", "position", "itemStyle", "borderColor", "borderWidth", "candlestick", "bordercolor", "color0", "bordercolor0", "opacity0", "highLine", "lowLine", "heatmap", "useSplit", "treemap", "tagCloud", "timeInterval", "font", "spiral", "getColor", "context", "xStart", "yStart", "xEnd", "yEnd", "isObject", "linearGradient", "x0", "y0", "x1", "y1", "xSpacing", "ySpacing", "gradientColor", "createLinearGradient", "item", "offset", "addColorStop", "HEX2HSL", "hex", "result", "exec", "r", "parseInt", "g", "b", "h", "s", "Math", "l", "d", "round", "HSL2HEX", "hsl", "hue2rgb", "p", "q", "t", "toHex", "x", "toString", "convertCoordinateOrigin", "origin", "centerX", "centerY", "y", "isCollision", "souce", "target", "distance", "percentToNum", "percent", "String", "replace", "Number", "Object", "prototype", "call", "argsTag", "funcTag", "genTag", "mapTag", "setTag", "reFlags", "reIsHostCtor", "reIsUint", "cloneableTags", "freeGlobal", "global", "freeSelf", "self", "root", "Function", "freeExports", "exports", "nodeType", "freeModule", "module", "moduleExports", "addMapEntry", "map", "pair", "set", "addSetEntry", "value", "add", "arrayReduce", "array", "iteratee", "accumulator", "initAccum", "index", "isHostObject", "mapToArray", "Array", "key", "overArg", "func", "transform", "arg", "setToArray", "uid", "arrayProto", "funcProto", "objectProto", "coreJsData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "IE_PROTO", "funcToString", "hasOwnProperty", "objectToString", "reIsNative", "RegExp", "<PERSON><PERSON><PERSON>", "undefined", "Symbol", "Uint8Array", "getPrototype", "getPrototypeOf", "objectCreate", "create", "propertyIsEnumerable", "splice", "nativeGetSymbols", "getOwnPropertySymbols", "nativeIsBuffer", "<PERSON><PERSON><PERSON><PERSON>", "nativeKeys", "DataView", "getNative", "Map", "Promise", "Set", "WeakMap", "nativeCreate", "dataViewCtorString", "toSource", "mapCtorString", "promiseCtorString", "setCtorString", "weakMapCtorString", "symbol<PERSON>roto", "symbolValueOf", "valueOf", "Hash", "entries", "clear", "entry", "ListCache", "MapCache", "<PERSON><PERSON>", "__data__", "arrayLikeKeys", "inherited", "isArray", "isObjectLike", "isArrayLike", "isArrayLikeObject", "isArguments", "n", "baseTimes", "skipIndexes", "isIndex", "assignValue", "object", "objValue", "eq", "assocIndexOf", "baseClone", "isDeep", "isFull", "customizer", "stack", "isArr", "constructor", "input", "initCloneArray", "source", "copyArray", "tag", "getTag", "isFunc", "buffer", "slice", "copy", "<PERSON><PERSON><PERSON><PERSON>", "isPrototype", "proto", "initCloneObject", "copyObject", "getSymbols", "copySymbols", "baseAssign", "cloneFunc", "Ctor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataView", "byteOffset", "byteLength", "cloneDataView", "typedArray", "cloneTypedArray", "cloneMap", "regexp", "lastIndex", "cloneRegExp", "cloneSet", "initCloneByTag", "stacked", "get", "props", "keysFunc", "symbolsFunc", "values", "arrayPush", "baseGetAllKeys", "getAllKeys", "arrayEach", "subValue", "baseIsNative", "isFunction", "test", "arrayBuffer", "newValue", "getMapData", "getValue", "has", "pop", "hash", "string", "cache", "pairs", "LARGE_ARRAY_SIZE", "other", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resolve", "ctorString", "<PERSON><PERSON><PERSON><PERSON>", "baseKeys", "replenishData", "sources", "sourcesKey", "<PERSON><PERSON><PERSON>", "isCover", "_key", "sourcesItem", "sourcesIndex", "concat", "cloneDeep", "calOptions", "config", "opts", "series", "seriesItem", "yAxis", "xAxis", "calSeries", "seriesIndex", "getAxisChartCurrentIndex", "chartData", "xAxisLabelPoint", "yAxisLabelPoint", "axisData", "currentIndex", "getPieChartCurrentIndex", "chartPie", "raidusMin", "raidusMax", "hypot", "abs", "currentRadian", "atan2", "PI", "_start_", "getRadarChartCurrentIndex", "chartRadar", "dataPosition", "arr", "spacingRadian", "calTooltipContainerData", "tooltipData", "tooltipTitle", "offsetX", "offsetY", "tooltipX", "tooltipY", "tooltipWidth", "tooltipHeight", "contentLength", "volumn", "calAxisPointerData", "crossFontPadding", "xEachSpacing", "xDataRange", "xMinData", "yEachSpacing", "yDataRange", "yMinData", "isExistCandlestick", "some", "curerntAxisLabel", "currentAxisLabelX", "currentAxisLabelY", "crossPointer", "xAxisPointer", "yAxisPointer", "yAxisLabel", "toFixed", "yAxisLabelX", "yAxisLabelFontSize", "xAxisLabelFontSize", "yAxisLabel<PERSON>", "measureText", "yAxisLabelHeight", "xAxis<PERSON>abe<PERSON>", "xAxisLabelHeight", "yAxisLabelY", "yAxisLineX0", "yAxisLineY0", "yAxisLineX1", "yAxisLineY1", "xAxisLabel", "xAxisLabelX", "xAxisLabelY", "xAxisLineX0", "xAxisLineY0", "xAxisLineX1", "xAxisLineY1", "chartCandlestick", "lineStartY", "axisPointerData", "calLineChartTooltipData", "chartLine", "name", "symbolType", "symbolSize", "symbolColor", "format", "textWidth", "calBarChartTooltipData", "chartBar", "barItem", "calPieChartTooltipData", "itemName", "_proportion_", "_end_", "calRadarChartTooltipData", "namePosition", "calCandlestickChartTooltipData", "rect", "start", "end", "low", "high", "_start", "_end", "_low", "_high", "_volumn", "calSeriesMap", "seriesMap", "a", "calSeriesColor", "dataItem", "dataIndex", "calLegendData", "legendType", "legendData", "textPadding", "legend<PERSON><PERSON><PERSON>", "legend<PERSON><PERSON><PERSON><PERSON>um", "legendList", "currentRow", "_shapeWidth", "_legendData", "containerWidth", "dataSeriesItem", "isDataName", "_legendType", "itemWidth", "obj", "legend<PERSON><PERSON>ght", "calAxisData", "xAxisShow", "xAxisType", "xAxisData", "xAxisBoundaryGap", "xAxisMax", "xAxisMin", "xAxisSplitNumber", "xAxisName", "xAxisTick", "xAxisLine", "xAxisSplitLine", "yAxisShow", "yAxisType", "yAxisData", "yAxisBoundaryGap", "yAxisMax", "yAxisMin", "yAxisSplitNumber", "yAxisName", "yAxisTick", "yAxisLine", "yAxisSplitLine", "xAxisNameShow", "xAxisNameTextStyle", "xAxisNameGap", "xAxisNameText", "xAxisLabelShow", "xAxisLabelTextStyle", "xAxisLabelGap", "xAxisLabelRotate", "showIndex", "xAxisLabelShowIndex", "xAxisLabelFormat", "xAxisTickShow", "xAxisTickStyle", "xAxisTick<PERSON><PERSON><PERSON>", "xAxisTickAlign", "xAxisTickShowIndex", "xAxisLineShow", "xAxisLineStyle", "xAxisSplitLineShow", "xAxisSplitLineStyle", "xAxisSplitLineAlign", "xAxisSplitLineShowIndex", "yAxisNameShow", "yAxisNameTextStyle", "yAxisNameGap", "yAxisNameText", "yAxisLabelShow", "yAxisLabelTextStyle", "yAxisLabelGap", "yAxisLabelShowIndex", "yAxisLabelFormat", "yAxisTickShow", "yAxisTickStyle", "yAxis<PERSON><PERSON><PERSON><PERSON>", "yAxisTickAlign", "yAxisTickShowIndex", "yAxisLineShow", "yAxisLineStyle", "yAxisSplitLineShow", "yAxisSplitLineStyle", "yAxisSplitLineAlign", "yAxisSplitLineShowIndex", "xAxisNameFontSize", "xAxisTickLineWidth", "xAxisLineWidth", "xAxisSplitLineWidth", "yAxisNameFontSize", "yAxisTickLineWidth", "yAxisLineWidth", "yAxisSplitLineWidth", "yZero", "yPlusSpacing", "yMinusSpacing", "xZero", "xPlusSpacing", "xMinusSpacing", "yMaxData", "xMaxData", "xStartInit", "yStartInit", "yIsSamePart", "xIsSamePart", "calAxisValue", "axis", "barDataObject", "allDataObject", "allDataArr", "stackDataArr", "reduce", "dataArr", "filterData", "isNaN", "axisLabelDataArr", "maxData", "minData", "dataRange", "dataEachRange", "limit", "multiple", "isSamePart", "ceil", "floor", "unshift", "i", "xAxisTickPoint", "xAxisLinePoint", "xAxisSplitLinePoint", "xAxisNamePoint", "yAxisTickPoint", "yAxisLinePoint", "yAxisSplitLinePoint", "yAxisNamePoint", "xAxisLabelDataArr", "yAxisLabelDataArr", "xAxisLabelMaxWidth", "xAxisLabelMaxHeight", "xAxisLabelTextArr", "sin", "cos", "yAxisLabelMaxWidth", "yAxisLabelTextArr", "xSpacingNumber", "ySpacingNumber", "xAxisNameTextWidth", "spacing", "_xStart", "startX", "startY", "endX", "endY", "_yStart", "isShow", "yAxisSplitLineNumber", "yAxisTickNumber", "xAxisSplitLineNumber", "xAxisTickNumber", "calAxisRadarData", "categories", "radarAxisName", "radarAxisNameShow", "radarAxisNameTextStyle", "radarAxisNameFontSize", "radarAxisNameMargin", "lineEndPosition", "axisNameStyle", "spacingAangle", "scale", "endPoint", "point", "calChartBarData", "seriesBar", "autoWidth", "autoWidthNumber", "sumWidth", "valueAxisPlusSpacing", "valueAxisMinusSpacing", "valueAxisSpacing", "categoryAxisEachSpacing", "categoryAxisData", "len", "chartBarArrItem", "barItemArr", "barItemArrIndex", "barIndex", "chartBarArrIndex", "yPositive", "yNagative", "barHeight", "xPositive", "xNagative", "calChartLineData", "seriesLine", "chartLineArr", "calChartPieData", "radius1", "radius2", "valueSum", "sum", "sortData", "calChartRadarData", "seriesRadar", "radarItem", "calChartScatterData", "seriesScatter", "chartScatter", "radiusMax", "radiusMin", "radiusRange", "zMax", "zMin", "zRange", "HSLColorMax", "HSLColorMin", "HSLColorRange", "scatterItemColor", "z", "HEXColorMin", "HEXColorMax", "positionX", "positionY", "HSLColor", "chartFunnel", "funnelWidth", "funnelHeight", "containerHeight", "pointX", "pointY", "funnelItemHeight", "medianWidth", "textPoint", "calChartCandlestick", "chartHeight", "candlestickSeries", "filter", "candlestickRect", "candlestickHighLine", "candlestickLowLine", "candlestickBar", "seriesHighLine", "seriesLowLine", "highLineShow", "highLineStyle", "lowLineShow", "lowLineStyle", "barShow", "<PERSON><PERSON><PERSON><PERSON>", "barData", "barLineStyle", "highData", "lowData", "Infinity", "candlestickItem", "upLinePoint", "downLinePoint", "rectPoint", "range", "lineStartX", "lineEndX", "lineEndY", "lowhLine", "calChartHeatmapData", "seriesHeatmap", "chartHeatmap", "defaultSeparation", "parent", "meanXReduce", "c", "maxYReduce", "count", "node", "children", "hierarchy", "child", "childs", "Node", "valued", "nodes", "defaultChildren", "depth", "eachBefore", "computeHeight", "copyData", "eachAfter", "each", "callback", "current", "next", "reverse", "compare", "path", "ancestor", "aNodes", "ancestors", "bNodes", "leastCommonAncestor", "k", "descendants", "leaves", "links", "circles", "m", "random", "shuffle", "B", "enclosesWeak", "encloseBasis", "extendBasis", "j", "enclosesWeakAll", "enclosesNot", "encloseBasis2", "encloseBasis3", "Error", "dr", "dx", "dy", "r1", "x2", "y2", "r2", "x21", "y21", "r21", "sqrt", "x3", "y3", "r3", "a2", "a3", "b2", "b3", "c2", "c3", "d1", "d2", "d3", "ab", "xa", "xb", "ya", "yb", "A", "C", "place", "intersects", "score", "_", "circle", "previous", "packEnclose", "aa", "ca", "sj", "sk", "pack", "enclose", "optional", "f", "required", "constantZero", "defaultRadius", "radiusLeaf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "preroot", "ambiguous", "defaultId", "id", "defaultParentId", "parentId", "nextLeft", "v", "nextRight", "moveSubtree", "wm", "wp", "shift", "change", "nextAncestor", "vim", "TreeNode", "phi", "squarifyRatio", "ratio", "row", "nodeValue", "sumValue", "minValue", "maxValue", "newRatio", "minRatio", "alpha", "beta", "rows", "i0", "i1", "dice", "treemapDice", "treemapSlice", "custom", "squarify", "tile", "paddingStack", "paddingInner", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "positionNode", "roundNode", "arguments", "paddingOuter", "constant", "resquarify", "_squarify", "separation", "nodeSize", "cluster", "previousNode", "meanX", "maxY", "leafLeft", "leafRight", "partition", "stratify", "nodeId", "nodeKey", "nodeByKey", "tree", "treeRoot", "firstWalk", "secondWalk", "sizeNode", "tx", "kx", "ky", "siblings", "w", "executeShifts", "midpoint", "vip", "vop", "vom", "sip", "sop", "sim", "som", "apportion", "sums", "valueOffset", "valueTarget", "hi", "mid", "valueLeft", "valueRight", "xk", "yk", "calChartTreemapData", "chartTreemap", "hierarchydata", "d3Hierarchy\n    .hierarchy", "treemapData", "d3Hierarchy\n    .treemap", "d3Hierarchy", "treemapGen", "cloudRadians", "cw", "ch", "cloudText", "cloudFont", "cloudFontNormal", "cloudFontSize", "cloudRotate", "cloudPadding", "cloudSprite", "contextAndRatio", "di", "sprite", "clearRect", "maxh", "save", "fillStyle", "strokeStyle", "textAlign", "style", "weight", "sr", "cr", "wcr", "wsr", "hcr", "hsr", "translate", "fillText", "strokeText", "restore", "xoff", "yoff", "hasText", "pixels", "getImageData", "w32", "seen", "seenRow", "cloudCollide", "board", "sw", "lx", "sx", "msx", "last", "cloudBounds", "bounds", "b0", "b1", "archimedeanSpiral", "zeroArray", "functor", "spirals", "archimedean", "rectangular", "sign", "fontStyle", "fontWeight", "words", "canvas", "cloud", "max<PERSON><PERSON><PERSON>", "dt", "dxdy", "minSize", "getContext", "tags", "Date", "now", "hasImage", "_tags", "_bounds", "step", "createMask", "img", "can", "cxt", "drawImage", "imageData", "tmp", "calChartTagCloudData", "element", "chartTagCloud", "layout", "imageMask", "calChartsData", "calChartFunnelData", "calChartCandlestickData", "easeIn", "pos", "pow", "easeOut", "easeInOut", "linear", "Animation", "isStop", "onProcess", "onAnimationFinish", "animationFrame", "requestAnimationFrame", "setTimeout", "timeStamp", "timingFunction", "Timing", "startTimeStamp", "process", "bind", "drawTooltip", "textFontSize", "textColor", "textLineHeight", "axisPointerType", "axisPointerLineStyle", "axisPointerShadowStyle", "axisPointerCross", "lineWdith", "axisPointerLineWidth", "axisPointerLineDash", "axisPointerLineColor", "axisPointerLineOpacity", "axisPointerShadowColor", "axisPointerShadowOpacity", "crossShow", "crossLineWidth", "crossLineDash", "crossLineColor", "crossLineOpacity", "crossBakcgroundColor", "crossBackgroundOpacity", "crossFontColor", "setLineDash", "globalAlpha", "beginPath", "moveTo", "lineTo", "stroke", "fillRect", "textBaseline", "arc", "fill", "drawRadiusRect", "_contentX", "_contentY", "drawBackground", "drawLegend", "legendHeightMax", "listItem", "listIndex", "legendItem", "closePath", "lineLength", "drawAxis", "xAxisNameColor", "xAxisLabelColor", "xAxisTickLineColor", "xAxisLineColor", "xAxisSplitLineColor", "yAxisNameColor", "yAxisLabelColor", "yAxisTickLineColor", "yAxisLineColor", "yAxisSplitLineColor", "drawAxisRadar", "radarAxisShape", "radarAxisLine", "radarAxisSplitLine", "radarAxisSplitArea", "radarAxisLineShow", "radarAxisLineStyle", "radarAxisSplitLineShow", "radarAxisSplitLineStyle", "radarAxisNameColor", "radarAxisLineColor", "radarAxisLineWidth", "radarAxisSplitLineColor", "radarAxisSplitLineWidth", "oddSplitAreaShow", "oddSplitAreaColor", "oddSplitAreaOpacity", "evenSplitAreaShow", "evenSplitAreaColor", "evenSplitAreaOpacity", "splitPositionArr", "splitIndex", "oddOrEven", "splitPositionItem", "splitPositionIndex", "lineEndPositionItem", "namePositionItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "globalLabel", "barItemColor", "barItemShow", "labelShow", "labelFontSize", "labelColor", "labelMargin", "labelFormat", "drawChartLine", "isNotMiddlePoint", "drawLine", "lineShow", "lineItemColor", "lineJoin", "drawArea", "areaShow", "areaColor", "areaOpacity", "lineItem", "symbolShow", "validData", "pAx", "pAy", "pBx", "pBy", "bezierCurveTo", "seriesLabel", "lineRadius", "<PERSON><PERSON><PERSON><PERSON>", "titleShow", "subtextFontSize", "subtextColor", "subtextLineHeight", "length1StartOrigin", "length2StartOrigin", "length2EndOrigin", "avoidCollision", "length1StartPosition", "length2StartPosition", "length2EndPosition", "textStartPosition", "assign", "quadraticCurveTo", "textStrArr", "split", "subtextStrArr", "titleY", "titleHeight", "drawChartRadar", "areaOpactiy", "lineWidht", "drawChartScatter", "scatterItem", "scatterItemName", "dataItemColor", "dataItemStyle", "pointItem", "pointIndex", "labelPosition", "drawChart<PERSON>andlestick", "seriesCandlestick", "upLineStartX", "upLineStartY", "upLineEndX", "upLineEndY", "downLineStartX", "downLineStartY", "downLineEndX", "downLineEndY", "strokeRect", "barItemStyle", "highLineColor", "highLineWidth", "highLineDash", "highLineOpacity", "lowLineColor", "lowLineWidth", "lowLineDash", "lowLineOpacity", "highLineStartX", "highLineStartY", "highLineEndX", "highLineEndY", "lowLineStartX", "lowLineStartY", "lowLineEndX", "lowLineEndY", "barColor", "barOpacity", "barLineWidth", "barLineColor", "drawChartHeatmap", "xSplitLineWidth", "ySplitLineWidth", "HeatmapItem", "drawChartTreemap", "splitLineShow", "splitLineWidth", "splitLineColor", "drawChartTagCloud", "<PERSON><PERSON><PERSON><PERSON>", "animationInstance", "stop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draw<PERSON>hart<PERSON>unnel", "event", "trigger", "Config", "addEventListener", "onRenderComplete", "updateOpts", "animationCache", "currentData", "getCurrentIndex", "touches", "changedTouches"], "mappings": "AAAe,MAAMA,EACnBC,cACEC,KAAKC,OAAS,GAQhBF,iBAAiBG,EAAMC,EAAW,cAChCH,KAAKC,OAAOC,GAAQF,KAAKC,OAAOC,IAAS,GACzCF,KAAKC,OAAOC,GAAME,KAAKD,GAQzBJ,QAAQG,KAASG,GACTL,KAAKC,OAAOC,IAChBF,KAAKC,OAAOC,GAAMI,QAAQH,IACxB,IACEA,EAASI,MAAM,KAAMF,GACrB,MAAOG,GACPC,QAAQC,MAAMF,OCtBxB,MAAe,CACbG,WAAW,EACXC,kBAAmB,IACnBC,gBAAiB,YACjBC,gBAAiB,UACjBC,OAAQ,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,WAChEC,QAAS,CACPC,MAAM,EACNC,KAAM,GACNC,aAAc,EACdL,gBAAiB,UACjBM,iBAAkB,EAClBC,kBAAmB,GACnBC,QAAS,GACTC,QAAS,EACTC,WAAY,EACZC,QAAS,EACTC,UAAW,CACTC,SAAU,GACVC,MAAO,UACPC,WAAY,IAEdC,YAAa,CACX5B,KAAM,OACN6B,UAAW,CACTC,UAAW,EACXJ,MAAO,UACPK,QAAS,GAEXC,YAAa,CACXN,MAAO,UACPK,QAAS,IAEXE,MAAO,CACLlB,MAAM,EACNe,UAAW,EACXI,UAAW,UACXC,SAAU,CAAC,EAAG,IACdC,YAAa,EACbxB,gBAAiB,UACjBO,kBAAmB,EACnBkB,UAAW,UACXC,YAAa,KAInBC,MAAO,CACLxB,MAAM,EACNU,SAAU,GACVC,MAAO,OACPc,OAAQ,GAEVC,OAAQ,CACN1B,MAAM,EACNf,KAAM,UACN0C,UAAW,GACXrB,QAAS,GACTsB,YAAa,IACbC,WAAY,GACZC,YAAa,GACbrB,UAAW,CACTC,SAAU,GACVC,MAAO,UACPN,QAAS,IAGbA,QAAS,CAAC,GAAI,GAAI,GAAI,IACtB0B,cAAe,CACb/B,MAAM,EACNf,KAAM,WACN+C,aAAa,EACbC,SAAU,CACRjC,MAAM,EACNkC,KAAM,OACNC,IAAK,GACL1B,UAAW,CACTE,MAAO,UACPD,SAAU,GACV0B,MAAO,WAGXC,UAAW,CACTrC,MAAM,EACNmC,IAAK,EACL1B,UAAW,CACTE,MAAO,UACPD,SAAU,KAGd4B,SAAU,CACRtC,MAAM,EACNuC,gBAAgB,EAChBC,OAAQ,EACR1B,UAAW,CACTC,UAAW,EACXJ,MAAO,YAGX8B,SAAU,CACRzC,MAAM,EACNc,UAAW,CACTC,UAAW,EACXJ,MAAO,YAGX+B,cAAe,CACb1C,MAAM,EACNuC,gBAAgB,EAChBzB,UAAW,CACTC,UAAW,EACXJ,MAAO,aAIbgC,WAAY,CACV3C,MAAM,EACNf,KAAM,QACN2D,IAAK,OACLC,IAAK,OACLC,YAAa,EACbb,SAAU,CACRjC,MAAM,EACNkC,KAAM,OACNC,IAAK,GACL1B,UAAW,CACTE,MAAO,UACPD,SAAU,GACV0B,MAAO,WAGXC,UAAW,CACTrC,MAAM,EACNmC,IAAK,EACL1B,UAAW,CACTE,MAAO,UACPD,SAAU,KAGd4B,SAAU,CACRtC,MAAM,EACNwC,OAAQ,EACR1B,UAAW,CACTC,UAAW,EACXJ,MAAO,YAGX8B,SAAU,CACRzC,MAAM,EACNc,UAAW,CACTC,UAAW,EACXJ,MAAO,YAGX+B,cAAe,CACb1C,MAAM,EACNc,UAAW,CACTC,UAAW,EACXJ,MAAO,aAIboC,cAAe,CACb/C,MAAM,EACNf,KAAM,WACN+C,aAAa,EACbC,SAAU,CACRjC,MAAM,EACNkC,KAAM,OACNC,IAAK,GACL1B,UAAW,CACTE,MAAO,UACPD,SAAU,KAGd2B,UAAW,CACTrC,MAAM,EACNgD,OAAQ,EACRb,IAAK,EACL1B,UAAW,CACTE,MAAO,UACPD,SAAU,KAGd4B,SAAU,CACRtC,MAAM,EACNuC,gBAAgB,EAChBC,OAAQ,EACR1B,UAAW,CACTC,UAAW,EACXJ,MAAO,YAGX8B,SAAU,CACRzC,MAAM,EACNc,UAAW,CACTC,UAAW,EACXJ,MAAO,YAGX+B,cAAe,CACb1C,MAAM,EACNuC,gBAAgB,EAChBzB,UAAW,CACTC,UAAW,EACXJ,MAAO,aAIbsC,WAAY,CACVjD,MAAM,EACNf,KAAM,QACN2D,IAAK,OACLC,IAAK,OACLC,YAAa,EACbb,SAAU,CACRjC,MAAM,EACNkC,KAAM,OACNC,IAAK,GACL1B,UAAW,CACTE,MAAO,UACPD,SAAU,GACV0B,MAAO,WAGXC,UAAW,CACTrC,MAAM,EACNgD,OAAQ,EACRb,IAAK,EACL1B,UAAW,CACTE,MAAO,UACPD,SAAU,KAGd4B,SAAU,CACRtC,MAAM,EACNwC,OAAQ,EACR1B,UAAW,CACTC,UAAW,EACXJ,MAAO,YAGX8B,SAAU,CACRzC,MAAM,EACNc,UAAW,CACTC,UAAW,EACXJ,MAAO,YAGX+B,cAAe,CACb1C,MAAM,EACNc,UAAW,CACTC,UAAW,EACXJ,MAAO,aAIbuC,UAAW,CACTC,MAAO,UACPC,OAAQ,CAAC,MAAO,OAChBC,OAAQ,MACRT,IAAK,OACLE,YAAa,EACbb,SAAU,CACRjC,MAAM,EACNS,UAAW,CACTC,SAAU,GACVC,MAAO,UACPc,OAAQ,KAGZgB,SAAU,CACRzC,MAAM,EACNc,UAAW,CACTC,UAAW,EACXJ,MAAO,UACPK,QAAS,IAGbsC,UAAW,CACTtD,MAAM,EACNc,UAAW,CACTC,UAAW,EACXJ,MAAO,UACPK,QAAS,IAGbuC,UAAW,CACTC,IAAK,CACHxD,MAAM,EACNW,MAAO,UACPK,QAAS,GAEXyC,KAAM,CACJzD,MAAM,EACNW,MAAO,UACPK,QAAS,KAIf0C,IAAK,CACHC,YAAa,GACbC,YAAa,EACbC,SAAU,OACVC,OAAQ,GAEVC,KAAM,CACJC,QAAQ,EACRC,cAAc,EACdF,KAAM,CACJ/D,MAAM,EACNe,UAAW,EACXJ,MAAO,OACPK,QAAS,GAEXkD,OAAQ,CACNlE,MAAM,EACNf,KAAM,SACNkF,KAAM,EACNxD,MAAO,QAETyD,KAAM,CACJpE,MAAM,EACNW,MAAO,OACPK,QAAS,KAGbqD,IAAK,CACHjB,OAAQ,CAAC,MAAO,OAChBC,OAAQ,CAAC,EAAG,OACZiB,UAAU,EACVC,YAAa,EACbC,kBAAkB,EAClBC,UAAW,CACTC,cAAe,EACf3D,UAAW,EACX4D,QAAS,GACTC,QAAS,IAEXC,MAAO,CACL7E,MAAM,EACNkC,KAAM,MACNzB,UAAW,CACTC,SAAU,GACVC,MAAO,UACPC,WAAY,IAEdkE,QAAS,MACTC,aAAc,CACZrE,SAAU,GACVC,MAAO,UACPC,WAAY,IAEdN,QAAS,IAGb0E,MAAO,CACLjB,KAAM,CACJ/D,MAAM,EACNe,UAAW,EACXJ,MAAO,OACPK,QAAS,GAEXoD,KAAM,CACJpE,MAAM,EACNW,MAAO,OACPK,QAAS,IAEXkD,OAAQ,CACNlE,MAAM,EACNf,KAAM,SACNkF,KAAM,EACNxD,MAAO,SAGXsE,QAAS,CACP5B,OAAQ,GACRrC,QAAS,EACTD,UAAW,EACXmE,YAAa,QAEfC,OAAQ,CACNC,MAAO,OACPC,OAAQ,OACRC,IAAK,KACLC,KAAM,KACNC,MAAO,KACPC,OAAQ,KACR7C,IAAK,IACLC,IAAK,EACLV,IAAK,EACLgB,MAAO,SACPuC,KAAM,aACNC,YAAa,SACbnE,MAAO,CACLoE,SAAU,UAEZC,UAAW,CACTC,YAAa,UACbC,YAAa,IAGjBC,YAAa,CACXrC,YAAa,GACbC,YAAa,EACbC,SAAU,OACVgC,UAAW,CACTlF,MAAO,UACPsF,YAAa,UACbjF,QAAS,EACTkF,OAAQ,UACRC,aAAc,UACdC,SAAU,EACVL,YAAa,GAEfM,SAAU,CACRrG,MAAM,EACNc,UAAW,CACTH,MAAO,UACPI,UAAW,EACXK,SAAU,CAAC,GAAI,IACfJ,QAAS,IAGbsF,QAAS,CACPtG,MAAM,EACNc,UAAW,CACTH,MAAO,UACPI,UAAW,EACXK,SAAU,CAAC,GAAI,IACfJ,QAAS,IAGb0C,IAAK,CACH1D,MAAM,EACNqF,OAAQ,GACR5D,OAAQ,GACRoE,UAAW,CACTlF,MAAO,OACPK,QAAS,GAEXF,UAAW,CACTC,UAAW,EACXI,UAAW,aAIjBoF,QAAS,CACPV,UAAW,CACTlF,MAAO,CAAC,UAAW,WACnB6F,UAAU,IAGdC,QAAS,CACPnD,UAAW,CACTtD,MAAM,EACNe,UAAW,EACXJ,MAAO,YAGX+F,SAAU,CACRrG,QAAS,EACTsG,aAAc,IACdC,KAAM,QACNlG,SAAU,GACVsC,OAAQ,EACR6D,OAAQ,gBC5cL,SAASC,EAASnG,EAAOoG,EAASC,EAAQC,EAAQC,EAAMC,GAC7D,GAAIC,EAASzG,GAAQ,CACnB,MAAM0G,eAAEA,EAAcvH,OAAEA,GAAWa,GAC5B2G,EAAIC,EAAIC,EAAIC,GAAMJ,EAEnBK,EAAWR,EAAOF,EAClBW,EAAWR,EAAOF,EAElBW,EAAgBb,EAAQc,qBAAqBb,EAASU,EAAWJ,EAAIL,EAASU,EAAWJ,EAAIP,EAASU,EAAWF,EAAIP,EAASU,EAAWF,GAM/I,OALA3H,EAAOT,QAAQyI,IACb,MAAMC,OAAEA,EAAMpH,MAAEA,GAAUmH,EAC1BF,EAAcI,aAAaD,EAAQpH,KAG9BiH,EAGT,OAAOjH,EAQF,SAASsH,EAAQC,GACtB,IAAIC,EAAS,4CAA4CC,KAAKF,GAE1DG,EAAIC,SAASH,EAAO,GAAI,IACxBI,EAAID,SAASH,EAAO,GAAI,IACxBK,EAAIF,SAASH,EAAO,GAAI,IAE1BE,GAAK,IAAOE,GAAK,IAAOC,GAAK,IAC/B,IAEIC,EACFC,EAHE9F,EAAM+F,KAAK/F,IAAIyF,EAAGE,EAAGC,GACvB3F,EAAM8F,KAAK9F,IAAIwF,EAAGE,EAAGC,GAGrBI,GAAKhG,EAAMC,GAAO,EAEpB,GAAID,GAAOC,EACT4F,EAAIC,EAAI,MACH,CACL,IAAIG,EAAIjG,EAAMC,EAEd,OADA6F,EAAIE,EAAI,GAAMC,GAAK,EAAIjG,EAAMC,GAAOgG,GAAKjG,EAAMC,GACvCD,GACN,KAAKyF,EACHI,GAAKF,EAAIC,GAAKK,GAAKN,EAAIC,EAAI,EAAI,GAC/B,MACF,KAAKD,EACHE,GAAKD,EAAIH,GAAKQ,EAAI,EAClB,MACF,KAAKL,EACHC,GAAKJ,EAAIE,GAAKM,EAAI,EAGtBJ,GAAK,EASP,OANAC,GAAQ,IACRA,EAAIC,KAAKG,MAAMJ,GACfE,GAAQ,IACRA,EAAID,KAAKG,MAAMF,GACfH,EAAIE,KAAKG,MAAM,IAAML,GAEd,CAACA,EAAGC,EAAGE,GAQT,SAASG,EAAQC,GACtB,IACIX,EAAGE,EAAGC,GADLC,EAAGC,EAAGE,GAAKI,EAOhB,GAJAP,GAAK,IACLC,GAAK,IACLE,GAAK,IAEK,IAANF,EACFL,EAAIE,EAAIC,EAAII,MACP,CACL,MAAMK,EAAU,CAACC,EAAGC,EAAGC,KACjBA,EAAI,IAAGA,GAAK,GACZA,EAAI,IAAGA,GAAK,GACZA,EAAI,EAAI,EAAUF,EAAc,GAATC,EAAID,GAASE,EACpCA,EAAI,GAAcD,EAClBC,EAAI,EAAI,EAAUF,GAAKC,EAAID,IAAM,EAAI,EAAIE,GAAK,EAC3CF,GAEHC,EAAIP,EAAI,GAAMA,GAAK,EAAIF,GAAKE,EAAIF,EAAIE,EAAIF,EACxCQ,EAAI,EAAIN,EAAIO,EAClBd,EAAIY,EAAQC,EAAGC,EAAGV,EAAI,EAAI,GAC1BF,EAAIU,EAAQC,EAAGC,EAAGV,GAClBD,EAAIS,EAAQC,EAAGC,EAAGV,EAAI,EAAI,GAE5B,MAAMY,EAAQC,IACZ,MAAMpB,EAAMS,KAAKG,MAAU,IAAJQ,GAASC,SAAS,IACzC,OAAsB,IAAfrB,EAAI1F,OAAe,IAAM0F,EAAMA,GAGxC,MAAO,IAAImB,EAAMhB,KAAKgB,EAAMd,KAAKc,EAAMb,KAQlC,SAASgB,EAAwBC,EAAQrG,GAC9C,IAAKsG,EAASC,GAAWvG,EAEzB,MAAO,CACLkG,EAAGI,EAAUD,EAAOH,EACpBM,EAAGD,EAAUF,EAAOG,GAUxB,SAAgBC,EAAYC,EAAOC,EAAQC,GACzC,IAAIH,GAAc,EAOlB,OANIC,EAAMR,EAAI,GAAKS,EAAOT,EAAI,EAC5BO,EAAcC,EAAMF,EAAII,EAAWD,EAAOH,EACjCE,EAAMR,EAAI,GAAKS,EAAOT,EAAI,IACnCO,EAAcC,EAAMF,EAAII,EAAWD,EAAOH,GAGrCC,EA0BF,SAASI,EAAaC,GAO3B,OANMA,GACJA,EAAUC,OAAOD,GAASE,QAAQ,IAAK,IACvCF,EAAUG,OAAOH,GAAW,KAE5BA,EAAU,EAELA,EAOF,SAAS9C,EAASnH,GACvB,MAAgD,oBAAzCqK,OAAOC,UAAUhB,SAASiB,KAAKvK,yOC7KxC,IASIwK,EAAU,qBAKVC,EAAU,oBACVC,EAAS,6BACTC,EAAS,eAKTC,EAAS,eAwBTC,EAAU,OAGVC,EAAe,8BAGfC,EAAW,mBAGXC,EAAgB,GACpBA,EAAcR,GAAWQ,EA7CV,kBA8CfA,EA9BqB,wBA8BWA,EA7Bd,qBA8BlBA,EA9Cc,oBA8CWA,EA7CX,iBA8CdA,EA9BiB,yBA8BWA,EA7BX,yBA8BjBA,EA7Bc,sBA6BWA,EA5BV,uBA6BfA,EA5Be,uBA4BWA,EAAcL,GACxCK,EA5CgB,mBA4CWA,EA3CX,mBA4ChBA,EA1CgB,mBA0CWA,EAAcJ,GACzCI,EAzCgB,mBAyCWA,EAxCX,mBAyChBA,EA/Be,uBA+BWA,EA9BJ,8BA+BtBA,EA9BgB,wBA8BWA,EA7BX,yBA6BsC,EACtDA,EArDe,kBAqDWA,EAAcP,GACxCO,EA3CiB,qBA2CW,EAG5B,IAAIC,EAA8B,iBAAVC,GAAsBA,GAAUA,EAAOb,SAAWA,QAAUa,EAGhFC,EAA0B,iBAARC,MAAoBA,MAAQA,KAAKf,SAAWA,QAAUe,KAGxEC,EAAOJ,GAAcE,GAAYG,SAAS,cAATA,GAGjCC,EAA4CC,IAAYA,EAAQC,UAAYD,EAG5EE,EAAaH,GAA4CI,IAAWA,EAAOF,UAAYE,EAGvFC,EAAgBF,GAAcA,EAAWF,UAAYD,EAUzD,SAASM,EAAYC,EAAKC,GAGxB,OADAD,EAAIE,IAAID,EAAK,GAAIA,EAAK,IACfD,EAWT,SAASG,EAAYD,EAAKE,GAGxB,OADAF,EAAIG,IAAID,GACDF,EAuDT,SAASI,EAAYC,EAAOC,EAAUC,EAAaC,GACjD,IAAIC,GAAS,EACTlK,EAAS8J,EAAQA,EAAM9J,OAAS,EAKpC,IAHIiK,GAAajK,IACfgK,EAAcF,IAAQI,MAEfA,EAAQlK,GACfgK,EAAcD,EAASC,EAAaF,EAAMI,GAAQA,EAAOJ,GAE3D,OAAOE,EAyCT,SAASG,EAAaR,GAGpB,IAAIhE,GAAS,EACb,GAAa,MAATgE,GAA0C,mBAAlBA,EAAM5C,SAChC,IACEpB,KAAYgE,EAAQ,IACpB,MAAO5M,IAEX,OAAO4I,EAUT,SAASyE,EAAWb,GAClB,IAAIW,GAAS,EACTvE,EAAS0E,MAAMd,EAAI5H,MAKvB,OAHA4H,EAAI1M,SAAQ,SAAS8M,EAAOW,GAC1B3E,IAASuE,GAAS,CAACI,EAAKX,MAEnBhE,EAWT,SAAS4E,EAAQC,EAAMC,GACrB,OAAO,SAASC,GACd,OAAOF,EAAKC,EAAUC,KAW1B,SAASC,EAAWlB,GAClB,IAAIS,GAAS,EACTvE,EAAS0E,MAAMZ,EAAI9H,MAKvB,OAHA8H,EAAI5M,SAAQ,SAAS8M,GACnBhE,IAASuE,GAASP,KAEbhE,EAIT,IASMiF,EATFC,EAAaR,MAAMtC,UACnB+C,EAAY/B,SAAShB,UACrBgD,EAAcjD,OAAOC,UAGrBiD,EAAalC,EAAK,sBAGlBmC,GACEL,EAAM,SAAShF,KAAKoF,GAAcA,EAAWE,MAAQF,EAAWE,KAAKC,UAAY,KACvE,iBAAmBP,EAAO,GAItCQ,EAAeN,EAAU/D,SAGzBsE,EAAiBN,EAAYM,eAO7BC,EAAiBP,EAAYhE,SAG7BwE,EAAaC,OAAO,IACtBJ,EAAapD,KAAKqD,GAAgBzD,QAzQjB,sBAyQuC,QACvDA,QAAQ,yDAA0D,SAAW,KAI5E6D,EAASpC,EAAgBP,EAAK2C,YAASC,EACvCC,EAAS7C,EAAK6C,OACdC,EAAa9C,EAAK8C,WAClBC,EAAetB,EAAQzC,OAAOgE,eAAgBhE,QAC9CiE,EAAejE,OAAOkE,OACtBC,EAAuBlB,EAAYkB,qBACnCC,EAASrB,EAAWqB,OAGpBC,EAAmBrE,OAAOsE,sBAC1BC,EAAiBZ,EAASA,EAAOa,cAAWZ,EAC5Ca,EAAahC,EAAQzC,OAAOoD,KAAMpD,QAGlC0E,EAAWC,GAAU3D,EAAM,YAC3B4D,EAAMD,GAAU3D,EAAM,OACtB6D,EAAUF,GAAU3D,EAAM,WAC1B8D,EAAMH,GAAU3D,EAAM,OACtB+D,EAAUJ,GAAU3D,EAAM,WAC1BgE,EAAeL,GAAU3E,OAAQ,UAGjCiF,EAAqBC,GAASR,GAC9BS,EAAgBD,GAASN,GACzBQ,EAAoBF,GAASL,GAC7BQ,GAAgBH,GAASJ,GACzBQ,GAAoBJ,GAASH,GAG7BQ,GAAc1B,EAASA,EAAO5D,eAAY2D,EAC1C4B,GAAgBD,GAAcA,GAAYE,aAAU7B,EASxD,SAAS8B,GAAKC,GACZ,IAAIvD,GAAS,EACTlK,EAASyN,EAAUA,EAAQzN,OAAS,EAGxC,IADAzD,KAAKmR,UACIxD,EAAQlK,GAAQ,CACvB,IAAI2N,EAAQF,EAAQvD,GACpB3N,KAAKkN,IAAIkE,EAAM,GAAIA,EAAM,KA2F7B,SAASC,GAAUH,GACjB,IAAIvD,GAAS,EACTlK,EAASyN,EAAUA,EAAQzN,OAAS,EAGxC,IADAzD,KAAKmR,UACIxD,EAAQlK,GAAQ,CACvB,IAAI2N,EAAQF,EAAQvD,GACpB3N,KAAKkN,IAAIkE,EAAM,GAAIA,EAAM,KAyG7B,SAASE,GAASJ,GAChB,IAAIvD,GAAS,EACTlK,EAASyN,EAAUA,EAAQzN,OAAS,EAGxC,IADAzD,KAAKmR,UACIxD,EAAQlK,GAAQ,CACvB,IAAI2N,EAAQF,EAAQvD,GACpB3N,KAAKkN,IAAIkE,EAAM,GAAIA,EAAM,KAuF7B,SAASG,GAAML,GACblR,KAAKwR,SAAW,IAAIH,GAAUH,GA4FhC,SAASO,GAAcrE,EAAOsE,GAG5B,IAAItI,EAAUuI,GAAQvE,IAsrBxB,SAAqBA,GAEnB,OAmFF,SAA2BA,GACzB,OAmIF,SAAsBA,GACpB,QAASA,GAAyB,iBAATA,EApIlBwE,CAAaxE,IAAUyE,GAAYzE,GApFnC0E,CAAkB1E,IAAU0B,EAAerD,KAAK2B,EAAO,aAC1DsC,EAAqBjE,KAAK2B,EAAO,WAAa2B,EAAetD,KAAK2B,IAAU1B,GAzrBhDqG,CAAY3E,GAljB9C,SAAmB4E,EAAGxE,GAIpB,IAHA,IAAIG,GAAS,EACTvE,EAAS0E,MAAMkE,KAEVrE,EAAQqE,GACf5I,EAAOuE,GAASH,EAASG,GAE3B,OAAOvE,EA4iBH6I,CAAU7E,EAAM3J,OAAQ2H,QACxB,GAEA3H,EAAS2F,EAAO3F,OAChByO,IAAgBzO,EAEpB,IAAK,IAAIsK,KAAOX,GACTsE,IAAa5C,EAAerD,KAAK2B,EAAOW,IACvCmE,IAAuB,UAAPnE,GAAmBoE,GAAQpE,EAAKtK,KACpD2F,EAAOhJ,KAAK2N,GAGhB,OAAO3E,EAaT,SAASgJ,GAAYC,EAAQtE,EAAKX,GAChC,IAAIkF,EAAWD,EAAOtE,GAChBe,EAAerD,KAAK4G,EAAQtE,IAAQwE,GAAGD,EAAUlF,UACxC+B,IAAV/B,GAAyBW,KAAOsE,KACnCA,EAAOtE,GAAOX,GAYlB,SAASoF,GAAajF,EAAOQ,GAE3B,IADA,IAAItK,EAAS8J,EAAM9J,OACZA,KACL,GAAI8O,GAAGhF,EAAM9J,GAAQ,GAAIsK,GACvB,OAAOtK,EAGX,OAAQ,EA8BV,SAASgP,GAAUrF,EAAOsF,EAAQC,EAAQC,EAAY7E,EAAKsE,EAAQQ,GACjE,IAAIzJ,EAIJ,GAHIwJ,IACFxJ,EAASiJ,EAASO,EAAWxF,EAAOW,EAAKsE,EAAQQ,GAASD,EAAWxF,SAExD+B,IAAX/F,EACF,OAAOA,EAET,IAAKf,GAAS+E,GACZ,OAAOA,EAET,IAAI0F,EAAQnB,GAAQvE,GACpB,GAAI0F,GAEF,GADA1J,EA2XJ,SAAwBmE,GACtB,IAAI9J,EAAS8J,EAAM9J,OACf2F,EAASmE,EAAMwF,YAAYtP,GAG3BA,GAA6B,iBAAZ8J,EAAM,IAAkBuB,EAAerD,KAAK8B,EAAO,WACtEnE,EAAOuE,MAAQJ,EAAMI,MACrBvE,EAAO4J,MAAQzF,EAAMyF,OAEvB,OAAO5J,EApYI6J,CAAe7F,IACnBsF,EACH,OA6ON,SAAmBQ,EAAQ3F,GACzB,IAAII,GAAS,EACTlK,EAASyP,EAAOzP,OAEpB8J,IAAUA,EAAQO,MAAMrK,IACxB,OAASkK,EAAQlK,GACf8J,EAAMI,GAASuF,EAAOvF,GAExB,OAAOJ,EArPI4F,CAAU/F,EAAOhE,OAErB,CACL,IAAIgK,EAAMC,GAAOjG,GACbkG,EAASF,GAAOzH,GAAWyH,GAAOxH,EAEtC,GAAImE,GAAS3C,GACX,OA0HN,SAAqBmG,EAAQb,GAC3B,GAAIA,EACF,OAAOa,EAAOC,QAEhB,IAAIpK,EAAS,IAAImK,EAAOR,YAAYQ,EAAO9P,QAE3C,OADA8P,EAAOE,KAAKrK,GACLA,EAhIIsK,CAAYtG,EAAOsF,GAE5B,GAp0BY,mBAo0BRU,GAAoBA,GAAO1H,GAAY4H,IAAWjB,EAAS,CAC7D,GAAIzE,EAAaR,GACf,OAAOiF,EAASjF,EAAQ,GAG1B,GADAhE,EA+XN,SAAyBiJ,GACvB,MAAqC,mBAAtBA,EAAOU,aAA8BY,GAAYtB,GAE5D,IAzVcuB,EAwVHtE,EAAa+C,GAvVrBhK,GAASuL,GAASpE,EAAaoE,GAAS,IADjD,IAAoBA,EAzCLC,CAAgBP,EAAS,GAAKlG,IAClCsF,EACH,OA6QR,SAAqBQ,EAAQb,GAC3B,OAAOyB,GAAWZ,EAAQa,GAAWb,GAASb,GA9QjC2B,CAAY5G,EAhD3B,SAAoBiF,EAAQa,GAC1B,OAAOb,GAAUyB,GAAWZ,EAAQvE,GAAKuE,GAASb,GA+ClB4B,CAAW7K,EAAQgE,QAE1C,CACL,IAAKlB,EAAckH,GACjB,OAAOf,EAASjF,EAAQ,GAE1BhE,EA0YN,SAAwBiJ,EAAQe,EAAKc,EAAWxB,GAC9C,IAAIyB,EAAO9B,EAAOU,YAClB,OAAQK,GACN,IArtCiB,uBAstCf,OAAOgB,GAAiB/B,GAE1B,IAvuCU,mBAwuCV,IAvuCU,gBAwuCR,OAAO,IAAI8B,GAAM9B,GAEnB,IA3tCc,oBA4tCZ,OA3QN,SAAuBgC,EAAU3B,GAC/B,IAAIa,EAASb,EAAS0B,GAAiBC,EAASd,QAAUc,EAASd,OACnE,OAAO,IAAIc,EAAStB,YAAYQ,EAAQc,EAASC,WAAYD,EAASE,YAyQ3DC,CAAcnC,EAAQK,GAE/B,IA7tCa,wBA6tCI,IA5tCJ,wBA6tCb,IA5tCU,qBA4tCI,IA3tCH,sBA2tCkB,IA1tClB,sBA2tCX,IA1tCW,sBA0tCI,IAztCG,6BAytCmB,IAxtCzB,uBAwtCyC,IAvtCzC,uBAwtCV,OA/MN,SAAyB+B,EAAY/B,GACnC,IAAIa,EAASb,EAAS0B,GAAiBK,EAAWlB,QAAUkB,EAAWlB,OACvE,OAAO,IAAIkB,EAAW1B,YAAYQ,EAAQkB,EAAWH,WAAYG,EAAWhR,QA6MjEiR,CAAgBrC,EAAQK,GAEjC,KAAK7G,EACH,OArQN,SAAkBmB,EAAK0F,EAAQwB,GAE7B,OAAO5G,EADKoF,EAASwB,EAAUrG,EAAWb,IAAM,GAAQa,EAAWb,GACzCD,EAAa,IAAIC,EAAI+F,aAmQpC4B,CAAStC,EAAQK,EAAQwB,GAElC,IAhvCY,kBAivCZ,IA5uCY,kBA6uCV,OAAO,IAAIC,EAAK9B,GAElB,IAjvCY,kBAkvCV,OAhQN,SAAqBuC,GACnB,IAAIxL,EAAS,IAAIwL,EAAO7B,YAAY6B,EAAO1B,OAAQnH,EAAQ1C,KAAKuL,IAEhE,OADAxL,EAAOyL,UAAYD,EAAOC,UACnBzL,EA6PI0L,CAAYzC,GAErB,KAAKvG,EACH,OApPN,SAAkBoB,EAAKwF,EAAQwB,GAE7B,OAAO5G,EADKoF,EAASwB,EAAU9F,EAAWlB,IAAM,GAAQkB,EAAWlB,GACzCC,EAAa,IAAID,EAAI6F,aAkPpCgC,CAAS1C,EAAQK,EAAQwB,GAElC,IApvCY,kBAqvCV,OA3Oe/O,EA2OIkN,EA1OhBtB,GAAgBxF,OAAOwF,GAActF,KAAKtG,IAAW,GAD9D,IAAqBA,EA/LN6P,CAAe5H,EAAOgG,EAAKX,GAAWC,IAInDG,IAAUA,EAAQ,IAAItB,IACtB,IAAI0D,EAAUpC,EAAMqC,IAAI9H,GACxB,GAAI6H,EACF,OAAOA,EAIT,GAFApC,EAAM3F,IAAIE,EAAOhE,IAEZ0J,EACH,IAAIqC,EAAQxC,EAsQhB,SAAoBN,GAClB,OAnOF,SAAwBA,EAAQ+C,EAAUC,GACxC,IAAIjM,EAASgM,EAAS/C,GACtB,OAAOV,GAAQU,GAAUjJ,EApwB3B,SAAmBmE,EAAO+H,GAKxB,IAJA,IAAI3H,GAAS,EACTlK,EAAS6R,EAAO7R,OAChBuF,EAASuE,EAAM9J,SAEVkK,EAAQlK,GACf8J,EAAMvE,EAAS2E,GAAS2H,EAAO3H,GAEjC,OAAOJ,EA4vB2BgI,CAAUnM,EAAQiM,EAAYhD,IAiOzDmD,CAAenD,EAAQ1D,GAAMoF,IAvQb0B,CAAWrI,GAASuB,GAAKvB,GAUhD,OA5vBF,SAAmBG,EAAOC,GAIxB,IAHA,IAAIG,GAAS,EACTlK,EAAS8J,EAAQA,EAAM9J,OAAS,IAE3BkK,EAAQlK,IAC8B,IAAzC+J,EAASD,EAAMI,GAAQA,EAAOJ,MA+uBpCmI,CAAUP,GAAS/H,GAAO,SAASuI,EAAU5H,GACvCoH,IAEFQ,EAAWvI,EADXW,EAAM4H,IAIRvD,GAAYhJ,EAAQ2E,EAAK0E,GAAUkD,EAAUjD,EAAQC,EAAQC,EAAY7E,EAAKX,EAAOyF,OAEhFzJ,EAkDT,SAASwM,GAAaxI,GACpB,SAAK/E,GAAS+E,KAyYEa,EAzYiBb,EA0YxBsB,GAAeA,KAAcT,MAvYvB4H,GAAWzI,IAAUQ,EAAaR,GAAU4B,EAAahD,GACzD8J,KAAKrF,GAASrD,IAqY/B,IAAkBa,EAtVlB,SAASmG,GAAiB2B,GACxB,IAAI3M,EAAS,IAAI2M,EAAYhD,YAAYgD,EAAYxB,YAErD,OADA,IAAIlF,EAAWjG,GAAQ8D,IAAI,IAAImC,EAAW0G,IACnC3M,EA8GT,SAAS0K,GAAWZ,EAAQiC,EAAO9C,EAAQO,GACzCP,IAAWA,EAAS,IAKpB,IAHA,IAAI1E,GAAS,EACTlK,EAAS0R,EAAM1R,SAEVkK,EAAQlK,GAAQ,CACvB,IAAIsK,EAAMoH,EAAMxH,GAEZqI,EAAWpD,EACXA,EAAWP,EAAOtE,GAAMmF,EAAOnF,GAAMA,EAAKsE,EAAQa,QAClD/D,EAEJiD,GAAYC,EAAQtE,OAAkBoB,IAAb6G,EAAyB9C,EAAOnF,GAAOiI,GAElE,OAAO3D,EAkCT,SAAS4D,GAAWjJ,EAAKe,GACvB,IAqKiBX,EACblN,EAtKAgB,EAAO8L,EAAIwE,SACf,OAsKgB,WADZtR,SADakN,EApKAW,KAsKmB,UAAR7N,GAA4B,UAARA,GAA4B,WAARA,EACrD,cAAVkN,EACU,OAAVA,GAvKDlM,EAAmB,iBAAP6M,EAAkB,SAAW,QACzC7M,EAAK8L,IAWX,SAASkD,GAAUmC,EAAQtE,GACzB,IAAIX,EAj8BN,SAAkBiF,EAAQtE,GACxB,OAAiB,MAAVsE,OAAiBlD,EAAYkD,EAAOtE,GAg8B/BmI,CAAS7D,EAAQtE,GAC7B,OAAO6H,GAAaxI,GAASA,OAAQ+B,EA7tBvC8B,GAAKzF,UAAU2F,MAnEf,WACEnR,KAAKwR,SAAWjB,EAAeA,EAAa,MAAQ,IAmEtDU,GAAKzF,UAAkB,OAtDvB,SAAoBuC,GAClB,OAAO/N,KAAKmW,IAAIpI,WAAe/N,KAAKwR,SAASzD,IAsD/CkD,GAAKzF,UAAU0J,IA1Cf,SAAiBnH,GACf,IAAI7M,EAAOlB,KAAKwR,SAChB,GAAIjB,EAAc,CAChB,IAAInH,EAASlI,EAAK6M,GAClB,MA7YiB,8BA6YV3E,OAA4B+F,EAAY/F,EAEjD,OAAO0F,EAAerD,KAAKvK,EAAM6M,GAAO7M,EAAK6M,QAAOoB,GAqCtD8B,GAAKzF,UAAU2K,IAzBf,SAAiBpI,GACf,IAAI7M,EAAOlB,KAAKwR,SAChB,OAAOjB,OAA6BpB,IAAdjO,EAAK6M,GAAqBe,EAAerD,KAAKvK,EAAM6M,IAwB5EkD,GAAKzF,UAAU0B,IAXf,SAAiBa,EAAKX,GAGpB,OAFWpN,KAAKwR,SACXzD,GAAQwC,QAA0BpB,IAAV/B,EA5aV,4BA4akDA,EAC9DpN,MAoHTqR,GAAU7F,UAAU2F,MAjFpB,WACEnR,KAAKwR,SAAW,IAiFlBH,GAAU7F,UAAkB,OArE5B,SAAyBuC,GACvB,IAAI7M,EAAOlB,KAAKwR,SACZ7D,EAAQ6E,GAAatR,EAAM6M,GAE/B,QAAIJ,EAAQ,KAIRA,GADYzM,EAAKuC,OAAS,EAE5BvC,EAAKkV,MAELzG,EAAOlE,KAAKvK,EAAMyM,EAAO,IAEpB,IAyDT0D,GAAU7F,UAAU0J,IA7CpB,SAAsBnH,GACpB,IAAI7M,EAAOlB,KAAKwR,SACZ7D,EAAQ6E,GAAatR,EAAM6M,GAE/B,OAAOJ,EAAQ,OAAIwB,EAAYjO,EAAKyM,GAAO,IA0C7C0D,GAAU7F,UAAU2K,IA9BpB,SAAsBpI,GACpB,OAAOyE,GAAaxS,KAAKwR,SAAUzD,IAAQ,GA8B7CsD,GAAU7F,UAAU0B,IAjBpB,SAAsBa,EAAKX,GACzB,IAAIlM,EAAOlB,KAAKwR,SACZ7D,EAAQ6E,GAAatR,EAAM6M,GAO/B,OALIJ,EAAQ,EACVzM,EAAKd,KAAK,CAAC2N,EAAKX,IAEhBlM,EAAKyM,GAAO,GAAKP,EAEZpN,MAkGTsR,GAAS9F,UAAU2F,MA/DnB,WACEnR,KAAKwR,SAAW,CACd6E,KAAQ,IAAIpF,GACZjE,IAAO,IAAKmD,GAAOkB,IACnBiF,OAAU,IAAIrF,KA4DlBK,GAAS9F,UAAkB,OA/C3B,SAAwBuC,GACtB,OAAOkI,GAAWjW,KAAM+N,GAAa,OAAEA,IA+CzCuD,GAAS9F,UAAU0J,IAnCnB,SAAqBnH,GACnB,OAAOkI,GAAWjW,KAAM+N,GAAKmH,IAAInH,IAmCnCuD,GAAS9F,UAAU2K,IAvBnB,SAAqBpI,GACnB,OAAOkI,GAAWjW,KAAM+N,GAAKoI,IAAIpI,IAuBnCuD,GAAS9F,UAAU0B,IAVnB,SAAqBa,EAAKX,GAExB,OADA6I,GAAWjW,KAAM+N,GAAKb,IAAIa,EAAKX,GACxBpN,MAgGTuR,GAAM/F,UAAU2F,MApEhB,WACEnR,KAAKwR,SAAW,IAAIH,IAoEtBE,GAAM/F,UAAkB,OAxDxB,SAAqBuC,GACnB,OAAO/N,KAAKwR,SAAiB,OAAEzD,IAwDjCwD,GAAM/F,UAAU0J,IA5ChB,SAAkBnH,GAChB,OAAO/N,KAAKwR,SAAS0D,IAAInH,IA4C3BwD,GAAM/F,UAAU2K,IAhChB,SAAkBpI,GAChB,OAAO/N,KAAKwR,SAAS2E,IAAIpI,IAgC3BwD,GAAM/F,UAAU0B,IAnBhB,SAAkBa,EAAKX,GACrB,IAAImJ,EAAQvW,KAAKwR,SACjB,GAAI+E,aAAiBlF,GAAW,CAC9B,IAAImF,EAAQD,EAAM/E,SAClB,IAAKrB,GAAQqG,EAAM/S,OAASgT,IAE1B,OADAD,EAAMpW,KAAK,CAAC2N,EAAKX,IACVpN,KAETuW,EAAQvW,KAAKwR,SAAW,IAAIF,GAASkF,GAGvC,OADAD,EAAMrJ,IAAIa,EAAKX,GACRpN,MAicT,IAAI+T,GAAanE,EAAmB5B,EAAQ4B,EAAkBrE,QAyhB9D,WACE,MAAO,IAjhBL8H,GAtQJ,SAAoBjG,GAClB,OAAO2B,EAAetD,KAAK2B,IAyX7B,SAAS+E,GAAQ/E,EAAO3J,GAEtB,SADAA,EAAmB,MAAVA,EAnxCY,iBAmxCwBA,KAE1B,iBAAT2J,GAAqBnB,EAAS6J,KAAK1I,KAC1CA,GAAS,GAAKA,EAAQ,GAAK,GAAKA,EAAQ3J,EAmC7C,SAASkQ,GAAYvG,GACnB,IAAI+G,EAAO/G,GAASA,EAAM2F,YAG1B,OAAO3F,KAFqB,mBAAR+G,GAAsBA,EAAK3I,WAAcgD,GAY/D,SAASiC,GAASxC,GAChB,GAAY,MAARA,EAAc,CAChB,IACE,OAAOY,EAAapD,KAAKwC,GACzB,MAAOzN,IACT,IACE,OAAQyN,EAAO,GACf,MAAOzN,KAEX,MAAO,GAyDT,SAAS+R,GAAGnF,EAAOsJ,GACjB,OAAOtJ,IAAUsJ,GAAUtJ,GAAUA,GAASsJ,GAAUA,GAxOrDzG,GA7oCa,qBA6oCDoD,GAAO,IAAIpD,EAAS,IAAI0G,YAAY,MAChDxG,GAAOkD,GAAO,IAAIlD,IAAQtE,GAC1BuE,GAvpCY,oBAupCDiD,GAAOjD,EAAQwG,YAC1BvG,GAAOgD,GAAO,IAAIhD,IAAQvE,GAC1BwE,GAppCY,oBAopCD+C,GAAO,IAAI/C,MACzB+C,GAAS,SAASjG,GAChB,IAAIhE,EAAS2F,EAAetD,KAAK2B,GAC7B+G,EA7pCQ,mBA6pCD/K,EAAsBgE,EAAM2F,iBAAc5D,EACjD0H,EAAa1C,EAAO1D,GAAS0D,QAAQhF,EAEzC,GAAI0H,EACF,OAAQA,GACN,KAAKrG,EAAoB,MAzpCf,oBA0pCV,KAAKE,EAAe,OAAO7E,EAC3B,KAAK8E,EAAmB,MAnqCf,mBAoqCT,KAAKC,GAAe,OAAO9E,EAC3B,KAAK+E,GAAmB,MAhqCf,mBAmqCb,OAAOzH,IAuQX,IAAIuI,GAAU7D,MAAM6D,QA2BpB,SAASE,GAAYzE,GACnB,OAAgB,MAATA,GAqGT,SAAkBA,GAChB,MAAuB,iBAATA,GACZA,GAAS,GAAKA,EAAQ,GAAK,GAAKA,GA/jDb,iBAw9CG0J,CAAS1J,EAAM3J,UAAYoS,GAAWzI,GAiDhE,IAAI2C,GAAWD,GAsLf,WACE,OAAO,GApKT,SAAS+F,GAAWzI,GAGlB,IAAIgG,EAAM/K,GAAS+E,GAAS2B,EAAetD,KAAK2B,GAAS,GACzD,OAAOgG,GAAOzH,GAAWyH,GAAOxH,EA2DlC,SAASvD,GAAS+E,GAChB,IAAIlN,SAAckN,EAClB,QAASA,IAAkB,UAARlN,GAA4B,YAARA,GA2DzC,SAASyO,GAAK0D,GACZ,OAAOR,GAAYQ,GAAUZ,GAAcY,GAtuB7C,SAAkBA,GAChB,IAAKsB,GAAYtB,GACf,OAAOrC,EAAWqC,GAEpB,IAAIjJ,EAAS,GACb,IAAK,IAAI2E,KAAOxC,OAAO8G,GACjBvD,EAAerD,KAAK4G,EAAQtE,IAAe,eAAPA,GACtC3E,EAAOhJ,KAAK2N,GAGhB,OAAO3E,EA4tB8C2N,CAAS1E,GA0ChExF,UA9VA,SAAmBO,GACjB,OAAOqF,GAAUrF,GAAO,GAAM,OC32CzB,SAAS4J,EAAcC,EAASC,EAAYlM,EAAQmM,EAAWC,GAAU,GFmLzE,IAAiBlW,EElLjB8J,EAAOmM,IAAoC,IAAtBnM,EAAOmM,IAA0C,KAAtBnM,EAAOmM,IAAkD,kBAAtBnM,EAAOmM,GAIzFC,EACE/O,EAAS4O,EAAQC,IACnB3L,OAAOoD,KAAKsI,EAAQC,IAAa5W,QAAQ+W,IACvCL,EAAcC,EAAQC,GAAaG,EAAMrM,EAAOmM,GAAYE,EAAMD,MF2KpDlW,EEzKC+V,EAAQC,GF0KiB,mBAAzC3L,OAAOC,UAAUhB,SAASiB,KAAKvK,GEzKd,UAAdgW,EACFD,EAAQC,GAAY5W,QAAQ,CAACgX,EAAaC,KACxChM,OAAOoD,KAAK2I,GAAahX,QAAQ+W,IAC/BL,EAAcC,EAAQI,GAAME,GAAeF,EAAMrM,EAAOqM,GAAME,GAAeF,GAAM,OAIvFrM,EAAOmM,GAAa,GAAGK,OAAOP,EAAQC,IAGxClM,EAAOmM,GAAaF,EAAQC,IAI1B7O,EAAS2C,EAAOmM,KAAe9O,EAAS4O,EAAQC,KAClD3L,OAAOoD,KAAKsI,EAAQC,IAAa5W,QAAQ+W,IACvCL,EAAcC,EAAQC,GAAaG,EAAMrM,EAAOmM,GAAYE,EAAMD,KAxBxEpM,EAAOmM,GAAaM,EAAUR,EAAQC,IAkC1C,SAAgBQ,IACd,IAAIC,OAAEA,EAAMC,KAAEA,GAAS5X,KACvBgX,EAAcW,EAAQ,YAAaC,EAAM,aACzCZ,EAAcW,EAAQ,oBAAqBC,EAAM,qBACjDZ,EAAcW,EAAQ,kBAAmBC,EAAM,mBAC/CZ,EAAcW,EAAQ,kBAAmBC,EAAM,mBAC/CZ,EAAcW,EAAQ,SAAUC,EAAM,UACtCZ,EAAcW,EAAQ,UAAWC,EAAM,WACvCZ,EAAcW,EAAQ,SAAUC,EAAM,UACtCZ,EAAcW,EAAQ,UAAWC,EAAM,WAEvCA,EAAKC,OAAOvX,QAAQwX,IAClB,OAAQA,EAAW5X,MACjB,IAAK,MACL,IAAK,OACL,IAAK,UACL,IAAK,cACL,IAAK,IACL,IAAK,UACC0X,EAAKG,OAA4B,YAAnBH,EAAKG,MAAM7X,KAC3B8W,EAAcW,EAAQ,gBAAiBC,EAAM,SAE7CZ,EAAcW,EAAQ,aAAcC,EAAM,SAGxCA,EAAKI,OAA4B,SAAnBJ,EAAKI,MAAM9X,KAC3B8W,EAAcW,EAAQ,aAAcC,EAAM,SAE1CZ,EAAcW,EAAQ,gBAAiBC,EAAM,SAEjD,IAAK,QACHZ,EAAcW,EAAQ,YAAaC,EAAM,gBAK/CK,EAAUxM,KAAKzL,MAQjB,SAAgBiY,IACd,IAAIN,OAAEA,EAAMC,KAAEA,GAAS5X,KAEvB4X,EAAKC,OAAOvX,QAAQ,CAACwX,EAAYI,KAE/B,OADAlB,EAAcW,EAAQ,QAASG,EAAY,SACnCA,EAAW5X,MACjB,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,QACL,IAAK,UACL,IAAK,SACL,IAAK,UACL,IAAK,UACL,IAAK,WACH8W,EAAcW,EAAQG,EAAW5X,KAAM0X,EAAKC,OAAQK,GACpD,MACF,IAAK,cACL,IAAK,IACHlB,EAAcW,EAAQ,cAAeC,EAAKC,OAAQK,MC3GnD,SAASC,EAAyBnP,GACvC,MAAMuB,EAAEA,EAACM,EAAEA,GAAM7B,GACX4O,KAAEA,EAAIQ,UAAEA,GAAcpY,MACtB+X,MAAEA,EAAKC,MAAEA,GAAUJ,GACnB3P,OAAEA,EAAME,KAAEA,EAAID,OAAEA,EAAME,KAAEA,EAAIiQ,gBAAEA,EAAeC,gBAAEA,GAAoBF,EAAUG,SAEnF,IAAIC,GAAgB,EA8BpB,OA/B2BjO,EAAIpC,GAAQoC,EAAItC,GAAU4C,EAAIzC,GAAQyC,EAAI3C,IAKjD,YAAd8P,EAAM9X,MAAoC,SAAd6X,EAAM7X,MACpCmY,EAAgB/X,QAAQ,CAACyI,EAAM4E,KACzBpD,EAAIxB,EAAKwB,IACXiO,EAAe7K,KAIfpD,EAAI8N,EAAgB,GAAG9N,IACzBiO,EAAe,IAEM,SAAdR,EAAM9X,MAAiC,YAAd6X,EAAM7X,MACxCoY,EAAgBhY,QAAQ,CAACyI,EAAM4E,KACzB9C,EAAI9B,EAAK8B,IACX2N,EAAe7K,KAIf9C,EAAIyN,EAAgB,GAAGzN,IACzB2N,EAAe,IAEM,SAAdR,EAAM9X,MAAmB6X,EAAM7X,MAMrCsY,EAOF,SAASC,EAAwBzP,GACtC,MAAMuB,EAAEA,EAACM,EAAEA,GAAM7B,GACX9H,KAAEA,EAAImD,OAAEA,EAAMC,OAAEA,EAAMkB,YAAEA,GAAgBxF,KAAKoY,UAAUM,UACtD/N,EAASC,GAAWvG,GACpBsU,EAAWC,GAAatU,EACzBb,EAASmG,KAAKiP,MAAMjP,KAAKkP,IAAIvO,EAAII,GAAUf,KAAKkP,IAAIjO,EAAID,IAE9D,IAAI4N,GAAgB,EAEpB,GAH2B/U,GAAUkV,GAAalV,GAAUmV,EAGpC,CAEtB,IAAIG,EAEFA,EADEnP,KAAKoP,MAAMzO,EAAII,EAASC,EAAUC,GAAK,EACzBjB,KAAKoP,MAAMzO,EAAII,EAASC,EAAUC,GAElCjB,KAAKqP,IAAMrP,KAAKqP,GAAKrP,KAAKkP,IAAIlP,KAAKoP,MAAMzO,EAAII,EAASC,EAAUC,KAE9EkO,GAAkB,GAAKvT,GAAeoE,KAAKqP,GAAM,IACnDF,IAAmB,GAAKvT,GAAeoE,KAAKqP,GAAM,IAElDF,EAAgB,EAAInP,KAAKqP,IAAMF,GAAkB,GAAKvT,GAAeoE,KAAKqP,GAAM,KAGlF/X,EAAKZ,QAAQ,CAACyI,EAAM4E,KACdoL,EAAgBhQ,EAAKmQ,UACvBV,EAAe7K,KAOrB,OAAO6K,EAOF,SAASW,EAA0BnQ,GACxC,MAAMuB,EAAEA,EAACM,EAAEA,GAAM7B,GACXoQ,WAAEA,EAAUjV,UAAEA,GAAcnE,KAAKoY,WACjC/T,OAAEA,EAAMC,OAAEA,GAAWH,GACpBwG,EAASC,GAAWvG,EACrBZ,EAASmG,KAAKiP,MAAMjP,KAAKkP,IAAIvO,EAAII,GAAUf,KAAKkP,IAAIjO,EAAID,IAE9D,IAAI4N,GAAgB,EAEpB,GAH2B/U,GAAUa,EAGb,CAEtB,IAAIyU,EAEFA,EADEnP,KAAKoP,MAAMzO,EAAII,EAASC,EAAUC,GAAK,EACzBjB,KAAKoP,MAAMzO,EAAII,EAASC,EAAUC,GAElCjB,KAAKqP,IAAMrP,KAAKqP,GAAKrP,KAAKkP,IAAIlP,KAAKoP,MAAMzO,EAAII,EAASC,EAAUC,KAGlFuO,EAAW,GAAGC,aAAa/Y,QAAQ,CAACyI,EAAM4E,EAAO2L,KAC/C,MAAMC,cAAEA,EAAaL,QAAEA,GAAYnQ,EACtB,GAAT4E,GACEoL,GAAiB,EAAInP,KAAKqP,GAAKM,EAAgB,GAAKR,EAAgBG,EAAUK,EAAgB,KAChGf,EAAe,GAGbO,EAAgBG,EAAUK,EAAgB,GAAKR,GAAiBG,EAAUK,EAAgB,IAC5Ff,EAAec,EAAI7V,OAASkK,KAQpC,OAAO6K,ECrHT,SAAgBgB,IACd,MAAM5B,KAAEA,EAAI6B,YAAEA,GAAgBzZ,MACxBqG,MAAEA,EAAKC,OAAEA,EAAMtF,QAAEA,GAAY4W,GAC7BtW,QAAEA,EAAOC,QAAEA,EAAOC,WAAEA,EAAUC,QAAEA,EAAOC,UAAEA,GAAcV,GACvDgI,OAAEA,EAAM9H,KAAEA,EAAIC,aAAEA,EAAYuY,aAAEA,GAAiBD,GAC7ClP,EAAGoP,EAAS9O,EAAG+O,GAAY5Q,EAEnC,IAGI6Q,EAAUC,EAHVC,EAAyB,EAAVzY,EAA2B,EAAbE,EAAiBC,EAAUN,EACxD6Y,EAA0B,EAAV1Y,EAIhBoY,IACFM,GAAiBtY,EAAUG,WAAaN,GAG1CL,EAAKZ,QAAQ,CAACyI,EAAM4E,EAAO2L,KACzB,GAAiB,eAAbvQ,EAAK7I,MAAsC,KAAb6I,EAAK7I,KAAa,CAClD,MAAM+Z,EAAgBlR,EAAKmR,OAAS,EAAI,EACxCF,GAAiBtY,EAAUG,WAAaoY,EAAgB1Y,EAAU0Y,EAAgB,OAE9EtM,EAAQ,GAAK2L,EAAI7V,OACnBuW,GAAiBtY,EAAUG,WAE3BmY,GAAiBtY,EAAUG,WAAaN,IAO5CsY,EAFEF,EApByB,GAoBUI,GAAgB1T,EAE1CsT,EAtBgB,GAuBlBA,EAvBkB,GAuBiBI,GAAgB,EAEjDJ,EAzBgB,GAyBmBI,EAG1CJ,GAAWtT,EAAQ,EAEVA,EAAQ0T,EAGR,EAMbD,EAFEF,EArCyB,GAqCUI,GAAiB1T,EAE3CsT,EAvCgB,GAwClBA,EAxCkB,GAwCiBI,GAAiB,EAElDJ,EA1CgB,GA0CmBI,EAG1CJ,GAAWtT,EAAS,EAEXA,EAAS0T,EAGT,EAIfha,KAAKyZ,YAAc,IACdzZ,KAAKyZ,YACRI,SAAAA,EACAC,SAAAA,EACAC,aAAAA,EACAC,cAAAA,EACAN,aAAAA,GAWG,SAASS,EAAmB3B,GACjC,IAAqB,GAAjBA,EAAoB,OAExB,MAAMxQ,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,EAASqB,YAAEA,GAAgBzZ,MAC5CgY,MAAEA,EAAKD,MAAEA,EAAK/W,QAAEA,GAAY4W,GAC1BrN,EAAGoP,EAAS9O,EAAG+O,GAAYH,EAAYzQ,QACzC9I,KAAEA,EAAIiC,MAAEA,GAAUnB,EAAQc,aACxBU,YAAa4X,GAAqBjY,GACpC8F,OACJA,EAAME,KACNA,EAAIkS,aACJA,EAAY1R,SACZA,EAAQ2R,WACRA,EAAUC,SACVA,EAAQrS,OACRA,EAAME,KACNA,EAAIoS,aACJA,EAAY5R,SACZA,EAAQ6R,WACRA,EAAUC,SACVA,EAAQrC,gBACRA,EAAeC,gBACfA,GACEF,EAAUG,SACRoC,EAAqBlB,EAAYvY,KAAK0Z,KAAK7R,GAAqB,eAAbA,EAAK7I,MAAsC,KAAb6I,EAAK7I,MAE5F,IAAI2a,EAAkBC,EAAmBC,EAarCC,EAAcC,EAAcC,EAEhC,GAbkB,YAAdlD,EAAM9X,MAAoC,SAAd6X,EAAM7X,MACpC2a,EAAmBxC,EAAgBG,GAAcrV,KACjD2X,EAAoBzC,EAAgBG,GAAcjO,EAClDwQ,EAAoB1C,EAAgBG,GAAc3N,GAC3B,SAAdmN,EAAM9X,MAAiC,YAAd6X,EAAM7X,MACxC2a,EAAmBvC,EAAgBE,GAAcrV,KACjD2X,EAAoBxC,EAAgBE,GAAcjO,EAClDwQ,EAAoBzC,EAAgBE,GAAc3N,GAC3B,SAAdmN,EAAM9X,MAAmB6X,EAAM7X,KAKxB,YAAd8X,EAAM9X,MAAoC,SAAd6X,EAAM7X,KAAiB,CACrD,MAAMib,GAAcT,GAAaxS,EAAS0R,GAAWa,EAAc7R,GAAUwS,QAAQ,GAC/EC,EAAc/C,EAAgB,GAAG/N,EACjC+Q,EAAqBvD,EAAMzU,UAAU5B,UAAUC,SAC/C4Z,EAAqBvD,EAAM1U,UAAU5B,UAAUC,SAErDqG,EAAQH,KAAUyT,EAAH,KACf,MAAME,EAAkBxT,EAAQyT,YAAYN,GAAY9U,MAAQ,EAAI+T,EAC9DsB,EAAmBJ,EAAqB,EAAIlB,EAElDpS,EAAQH,KAAU0T,EAAH,KACf,MAAMI,EAAkB3T,EAAQyT,YAAYZ,GAAkBxU,MAAQ,EAAI+T,EACpEwB,EAAmBL,EAAqB,EAAInB,EAElDpa,KAAKyZ,YAAYzQ,OAAS,CAAEuB,EAAGuQ,EAAmBjQ,EAAG+O,GAErD5R,EAAQH,KAAU7G,EAAQU,UAAUC,SAArB,KACf3B,KAAKyZ,YAAYtY,aAAeyI,KAAK/F,IAAI7D,KAAKyZ,YAAYtY,aAAc6G,EAAQyT,YAAYZ,GAAkBxU,OAC9GrG,KAAKyZ,YAAYC,aAAemB,EAEhCG,EAAe,CACbG,WAAAA,EACAK,gBAAAA,EACAE,iBAAAA,EACAL,YAAAA,EACAQ,YAAajC,EACbkC,YAAa7T,EACb8T,YAAanC,EACboC,YAAa7T,EACb8T,YAAarC,EACbsC,WAAYrB,EACZc,gBAAAA,EACAC,iBAAAA,EACAO,YAAarB,EACbsB,YAAarB,EACbsB,YAAavB,EACbwB,YAAapU,EACbqU,YAAazB,EACb0B,YAAapU,GAGH,QAARlI,EACF+a,EAAe,CACb1S,GAAIuS,EACJtS,GAAIN,EACJO,GAAIqS,EACJpS,GAAIN,GAEW,UAARlI,IACT+a,EAAe,CACb1Q,EAAGtC,EAASoS,EAAe7B,EAC3B3N,EAAGzC,EACH/B,MAAOgU,EACP/T,OAAQsC,SAGP,GAAkB,SAAdoP,EAAM9X,MAAiC,YAAd6X,EAAM7X,KAAoB,CAC5D,MAAMgc,GAAc3B,GAAaZ,EAAU1R,GAAUqS,EAAc3R,GAAUyS,QAAQ,GAC/EgB,EAAc/D,EAAgB,GAAGxN,EACjCyQ,EAAqBvD,EAAMzU,UAAU5B,UAAUC,SAC/C4Z,EAAqBvD,EAAM1U,UAAU5B,UAAUC,SAErDqG,EAAQH,KAAUyT,EAAH,KACf,MAAME,EAAkBxT,EAAQyT,YAAYZ,GAAkBxU,MAAQ,EAAI+T,EACpEsB,EAAmBJ,EAAqB,EAAIlB,EAElDpS,EAAQH,KAAU0T,EAAH,KACf,MAAMI,EAAkB3T,EAAQyT,YAAYS,GAAY7V,MAAQ,EAAI+T,EAC9DwB,EAAmBL,EAAqB,EAAInB,EAElDpa,KAAKyZ,YAAYzQ,OAAS,CAAEuB,EAAGoP,EAAS9O,EAAGkQ,GAE3C/S,EAAQH,KAAU7G,EAAQU,UAAUC,SAArB,KACf3B,KAAKyZ,YAAYtY,aAAeyI,KAAK/F,IAAI7D,KAAKyZ,YAAYtY,aAAc6G,EAAQyT,YAAYZ,GAAkBxU,OAC9GrG,KAAKyZ,YAAYC,aAAemB,EAEhCG,EAAe,CACbG,WAAYN,EACZW,gBAAAA,EACAE,iBAAAA,EACAL,YAAaP,EACbe,YAAad,EACbe,YAAa7T,EACb8T,YAAahB,EACbiB,YAAa7T,EACb8T,YAAalB,EACbmB,WAAAA,EACAP,gBAAAA,EACAC,iBAAAA,EACAO,YAAaxC,EACbyC,YAAAA,EACAC,YAAa1C,EACb2C,YAAapU,EACbqU,YAAa5C,EACb6C,YAAapU,GAGH,QAARlI,EACFgb,EAAe,CACb3S,GAAIN,EACJO,GAAIuS,EACJtS,GAAIN,EACJO,GAAIqS,GAEW,UAAR7a,IACTgb,EAAe,CACb3Q,EAAGtC,EACH4C,EAAGzC,EAAOoS,EAAehC,EACzBnS,MAAOsC,EACPrC,OAAQkU,QAGW,SAAdxC,EAAM9X,MAAmB6X,EAAM7X,KAGtCya,GACEvC,EAAUqE,iBAAiB9X,MAC7BqW,EAAasB,YAAclE,EAAUqE,iBAAiB9X,IAAI+X,YAI9DjD,EAAYkD,gBAAkB,CAC5B3B,aAAAA,EACAC,aAAAA,EACAC,aAAAA,GAUG,SAAS0B,EAAwBpE,GACtC,IAAqB,GAAjBA,EAAoB,OAExB,MAAMxQ,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,EAASqB,YAAEA,GAAgBzZ,KAClD,IAAImB,EAAesY,EAAYtY,aAE/B6G,EAAQH,KAAU+P,EAAK5W,QAAQU,UAAUC,SAA1B,KACfyW,EAAUyE,UAAUvc,QAAQyI,IAC1B,MAAM7I,KAAEA,EAAI4c,KAAEA,EAAI5b,KAAEA,EAAI4F,UAAEA,EAASrE,MAAEA,EAAK0C,OAAEA,GAAW4D,GAC/C7I,KAAM6c,EAAY3X,KAAM4X,EAAYpb,MAAOqb,GAAgB9X,GAC7DoF,EAAEA,EAACM,EAAEA,EAAG3J,KAAMkM,GAAUlM,EAAKsX,GAEnC,GAAqB,iBAAVpL,EAAoB,OAE/B,MAAMjK,EAAO,GAAG2Z,MAASra,EAAMya,OAASza,EAAMya,OAAO9P,GAASA,IACxD+P,EAAYnV,EAAQyT,YAAYtY,GAAMkD,MAC5ClF,EAAeyI,KAAK/F,IAAI1C,EAAcgc,GAEtC1D,EAAYvY,KAAKd,KAAK,CACpBF,KAAAA,EACA4c,KAAAA,EACA3Z,KAAAA,EACAvB,MAAOkF,EAAUlF,MACjB2I,EAAAA,EACAM,EAAAA,EACAkS,WAAAA,EACAC,WAAAA,EACAC,YAAAA,MAIJjd,KAAKyZ,YAAYtY,aAAeA,EAS3B,SAASic,EAAuB5E,GACrC,IAAqB,GAAjBA,EAAoB,OAExB,MAAMxQ,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,EAASqB,YAAEA,GAAgBzZ,KAClD,IAAImB,EAAesY,EAAYtY,aAE/B6G,EAAQH,KAAU+P,EAAK5W,QAAQU,UAAUC,SAA1B,KACfyW,EAAUiF,SAAS7E,GAAclY,QAAQgd,IACvCA,EAAQhd,QAAQwX,IACd,MAAM5X,KAAEA,EAAI4c,KAAEA,EAAI5b,KAAEA,EAAI4F,UAAEA,EAASrE,MAAEA,GAAUqV,EACzC3U,EAAO,GAAG2Z,MAASra,EAAMya,OAASza,EAAMya,OAAOhc,GAAQA,IACvDic,EAAYnV,EAAQyT,YAAYtY,GAAMkD,MAC5ClF,EAAeyI,KAAK/F,IAAI1C,EAAcgc,GAEtC1D,EAAYvY,KAAKd,KAAK,CACpBF,KAAAA,EACA4c,KAAAA,EACA3Z,KAAAA,EACAvB,MAAOkF,EAAUlF,YAKvB5B,KAAKyZ,YAAYtY,aAAeA,EAS3B,SAASoc,EAAuB/E,GACrC,IAAqB,GAAjBA,EAAoB,OAExB,MAAMxQ,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,EAASqB,YAAEA,GAAgBzZ,MAC5C8c,KAAEA,EAAI5c,KAAEA,EAAIgB,KAAEA,EAAImD,OAAEA,EAAMC,OAAEA,EAAM7B,MAAEA,GAAU2V,EAAUM,UACtDoE,KAAMU,EAAQpQ,MAAEA,EAAKtG,UAAEA,EAAS2W,aAAEA,EAAYvE,QAAEA,EAAOwE,MAAEA,GAAUxc,EAAKsX,GAC1ErV,EAAO,GAAGqa,MAAa/a,EAAMya,OAASza,EAAMya,OAAO9P,GAASA,IAElEpF,EAAQH,KAAU+P,EAAK5W,QAAQU,UAAUC,SAA1B,KACf,MAAMwb,EAAYnV,EAAQyT,YAAYtY,GAAMkD,MACtClF,EAAeyI,KAAK/F,IAAI4V,EAAYtY,aAAcgc,GACxDnd,KAAKyZ,YAAYtY,aAAeA,EAChCnB,KAAKyZ,YAAYC,aAAeoD,EAEhCrD,EAAYvY,KAAKd,KAAK,CACpBF,KAAAA,EACA4c,KAAMU,EACNra,KAAAA,EACAvB,MAAOkF,EAAUlF,MACjByC,OAAAA,EACAC,OAAAA,EACAmZ,aAAAA,EACAvE,QAAAA,EACAwE,MAAAA,IAUG,SAASC,EAAyBnF,GACvC,IAAqB,GAAjBA,EAAoB,OAExB,MAAMxQ,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,EAASqB,YAAEA,GAAgBzZ,KAClD,IAAImB,EAAesY,EAAYtY,aAE/B6G,EAAQH,KAAU+P,EAAK5W,QAAQU,UAAUC,SAA1B,KACfyW,EAAUgB,WAAW9Y,QAAQyI,IAC3B,MAAM7I,KAAEA,EAAI4c,KAAEA,EAAI5b,KAAEA,EAAImY,aAAEA,EAAYvS,UAAEA,EAASrE,MAAEA,EAAK0C,OAAEA,GAAW4D,GAC7D7I,KAAM6c,EAAY3X,KAAM4X,EAAYpb,MAAOqb,GAAgB9X,EAC7DiI,EAAQlM,EAAKsX,IACbjO,EAAEA,EAACM,EAAEA,GAAMwO,EAAab,GAAc3R,SAE5C,GAAqB,iBAAVuG,EAAoB,OAE/B,MAAMjK,EAAO,GAAG2Z,MAASra,EAAMya,OAASza,EAAMya,OAAO9P,GAASA,IACxD+P,EAAYnV,EAAQyT,YAAYtY,GAAMkD,MAC5ClF,EAAeyI,KAAK/F,IAAI1C,EAAcgc,GAEtC1D,EAAYvY,KAAKd,KAAK,CACpBF,KAAAA,EACA4c,KAAAA,EACA3Z,KAAAA,EACAvB,MAAOkF,EAAUlF,MACjB2I,EAAAA,EACAM,EAAAA,EACAkS,WAAAA,EACAC,WAAAA,EACAC,YAAAA,MAIJjd,KAAKyZ,YAAYtY,aAAeA,EAChCnB,KAAKyZ,YAAYC,aAAetB,EAAUjU,UAAUyZ,aAAapF,GAAcrV,KAO1E,SAAS0a,EAA+BrF,GAC7C,IAAqB,GAAjBA,EAAoB,OAExB,MAAMxQ,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,EAASqB,YAAEA,GAAgBzZ,MAC5CE,KAAEA,EAAI4c,KAAEA,EAAIgB,KAAEA,GAAS1F,EAAUqE,kBACjCsB,MAAEA,EAAKC,IAAEA,EAAGC,IAAEA,EAAGC,KAAEA,EAAIhE,OAAEA,EAAMtY,MAAEA,GAAUkc,EAAKtF,GAChD2F,EAAS,OAAOJ,EACpBK,EAAO,OAAOJ,EACdK,EAAO,OAAOJ,EACdK,EAAQ,OAAOJ,EACfK,EAAU,OAAOrE,EACnB,IAAIiD,EACFhc,EAAesY,EAAYtY,aAE7B6G,EAAQH,KAAU+P,EAAK5W,QAAQU,UAAUC,SAA1B,KAEfwb,EAAYnV,EAAQyT,YAAY0C,GAAQ9X,MACxClF,EAAeyI,KAAK/F,IAAI1C,EAAcgc,GAEtCA,EAAYnV,EAAQyT,YAAY2C,GAAM/X,MACtClF,EAAeyI,KAAK/F,IAAI1C,EAAcgc,GAEtCA,EAAYnV,EAAQyT,YAAY4C,GAAMhY,MACtClF,EAAeyI,KAAK/F,IAAI1C,EAAcgc,GAEtCA,EAAYnV,EAAQyT,YAAY6C,GAAOjY,MACvClF,EAAeyI,KAAK/F,IAAI1C,EAAcgc,GAElCjD,IACFiD,EAAYnV,EAAQyT,YAAY8C,GAASlY,MACzClF,EAAeyI,KAAK/F,IAAI1C,EAAcgc,IAGxC1D,EAAYvY,KAAKd,KAAK,CACpBF,KAAAA,EACA4c,KAAAA,EACAiB,MAAOI,EACPH,IAAKI,EACLH,IAAKI,EACLH,KAAMI,EACNpE,OAAQqE,EACR3c,MAAAA,IAGF5B,KAAKyZ,YAAYtY,aAAeA,EC/bnB,SAASqd,IACtB,IAAIC,EAAY,GAChBze,KAAK4X,KAAKC,OAAOvX,QAAQwX,IACA,eAAnBA,EAAW5X,MAA4C,KAAnB4X,EAAW5X,MAC5Cue,EAAuB,cAC1BA,EAAuB,YAAI,IAE7BA,EAAuB,YAAEre,KAAK0X,IACF,UAAnBA,EAAW5X,MACfue,EAAkB,SACrBA,EAAkB,OAAI,IAExB3G,EAAW5W,KAAKyF,KAAK,CAAC+X,EAAGjV,IACA,aAAnBqO,EAAWnR,KACN+X,EAAEtR,MAAQ3D,EAAE2D,MAEZ3D,EAAE2D,MAAQsR,EAAEtR,OAGvBqR,EAAkB,OAAEre,KAAK0X,KAEpB2G,EAAU3G,EAAW5X,QACxBue,EAAU3G,EAAW5X,MAAQ,IAE/Bue,EAAU3G,EAAW5X,MAAME,KAAK0X,MAGpC9X,KAAKye,UAAYA,EAEoBze,KAAKye,UC7B7B,SAASE,IACtB,IAAI5d,OAAEA,EAAM8W,OAAEA,GAAW7X,KAAK4X,KAE9BC,EAAOvX,QAAQ,CAACwX,EAAYI,KACH,OAAnBJ,EAAW5X,MAAoC,UAAnB4X,EAAW5X,MAAuC,WAAnB4X,EAAW5X,MAAwC,YAAnB4X,EAAW5X,KACxG4X,EAAW5W,KAAKZ,QAAQ,CAACse,EAAUC,KACjCD,EAAS9X,UAAY8X,EAAS9X,WAAa,GACtC8X,EAAS9X,UAAUlF,QACtBgd,EAAS9X,UAAUlF,MAAQb,EAAO8d,EAAY9d,EAAO0C,YAIzDqU,EAAWhR,UAAYgR,EAAWhR,WAAa,GAC1CgR,EAAWhR,UAAUlF,QACxBkW,EAAWhR,UAAUlF,MAAQb,EAAOmX,EAAcnX,EAAO0C,YCdlD,SAASqb,IACtB,GAAI9e,KAAK4X,KAAKjV,OAAO1B,KAAM,CACzB,IAAI+G,QAAEA,EAAO4P,KAAEA,GAAS5X,MACpBqG,MAAEA,EAAK/E,QAAEA,EAAOqB,OAAEA,EAAMkV,OAAEA,GAAWD,GACnC1X,KAAM6e,EAAY7d,KAAM8d,EAAUlc,WAAEA,EAAUC,YAAEA,EAAWF,YAAEA,EAAWtB,QAAEA,EAAOqB,UAAEA,EAASlB,UAAEA,GAAciB,GAC9GhB,SAAEA,EAAUL,QAAS2d,GAAgBvd,EACrCwd,EAAc,EACdC,EAAiB,EACjBC,EAAa,GACbC,EAAa,GACbC,EAAcxc,EACdyc,EAAc,GACdC,EAAiBnZ,EAAQ/E,EAAQ,GAAKA,EAAQ,GAE9Cme,EAAiB,GACjBC,EAAa7H,EAAO+C,KAAK9C,IAC3B2H,EAAiB3H,EACS,OAAnBA,EAAW5X,MAAoC,UAAnB4X,EAAW5X,MAAuC,WAAnB4X,EAAW5X,MAAwC,YAAnB4X,EAAW5X,OAc7Gqf,EAXGP,IACCU,EACYD,EAAeve,KAAK8L,IAAI4R,GAC7BA,EAAS9B,MAGJjF,EAAO7K,IAAI8K,GAChBA,EAAWgF,OAOxB9U,EAAQH,KAAUlG,EAAH,KACX+d,EACFH,EAAYjf,QAAQwc,IAClB2C,EAAeve,KAAKZ,QAAQse,IAC1B,GAAI9B,GAAQ8B,EAAS9B,KAAM,CACzB,IAAI6C,EACJ,GAAkB,WAAdZ,EACF,OAAQU,EAAevf,MACrB,IAAK,MACHyf,EAAc,SACdL,EAA4B,EAAdzc,EACd,MACF,IAAK,SACL,IAAK,UACL,IAAK,WACH8c,EAAc,OAKpB,IAAI7C,KAAEA,EAAIhW,UAAEA,GAAc8X,EACtBnD,EAAczb,KAAKgI,QAAQyT,YAAYqB,GAAQ,IAAIzW,MACnDuZ,EAAYN,EAAcL,EAAc1d,EAAUka,EAElDoE,EAAM,CACRd,WAAYY,EACZ7C,KAAAA,EACArB,YAAAA,EACA7Z,MAAOkF,EAAUlF,OAGfud,EAAiBS,EAAYJ,GAC/BJ,EAAWhf,KAAKif,GAChBF,EAAiBS,EACjBP,EAAa,CAACQ,KAEdV,GAAkBS,EAClBV,EAActV,KAAK/F,IAAIqb,EAAaC,GACpCE,EAAWjf,KAAKyf,SAMxBN,EAAYjf,QAAQwc,IAClBjF,EAAOvX,QAAQwX,IACb,IAAI6H,EACJ,GAAkB,WAAdZ,EACF,OAAQjH,EAAW5X,MACjB,IAAK,MACL,IAAK,QACL,IAAK,cACL,IAAK,IACL,IAAK,UACHyf,EAAc,OACd,MACF,IAAK,OACHA,EAAc,OACd,MACF,IAAK,UACHA,EAAc,SACdL,EAA4B,EAAdzc,EAKpB,GAAIia,GAAQhF,EAAWgF,KAAM,CAC3B,IAAIA,KAAEA,EAAIhW,UAAEA,GAAcgR,EACtB2D,EAAczb,KAAKgI,QAAQyT,YAAYqB,GAAQ,IAAIzW,MACnDuZ,EAAYN,EAAcL,EAAc1d,EAAUka,EAClDoE,EAAM,CACRd,WAAYY,EACZ7C,KAAAA,EACArB,YAAAA,EACA7Z,MAAOkF,EAAUlF,OAGfud,EAAiBS,EAAYJ,GAC/BJ,EAAWhf,KAAKif,GAChBF,EAAiBS,EACjBP,EAAa,CAACQ,KAEdV,GAAkBS,EAClBV,EAActV,KAAK/F,IAAIqb,EAAaC,GACpCE,EAAWjf,KAAKyf,SAOtBR,EAAW5b,QACb2b,EAAWhf,KAAKif,GAGlBrf,KAAKgf,WAAa,CAChBI,WAAAA,EACAF,YAAaA,EAAc3d,EAC3Bue,aAAcV,EAAW3b,OAASmG,KAAK/F,IAAId,EAAapB,IAAayd,EAAW3b,OAAS,GAAKlC,EAAUqB,QAG1G5C,KAAKgf,WAAa,CAChBI,WAAY,GACZF,YAAa,EACbY,aAAc,GAIoB9f,KAAKgf,WC5I9B,SAASe,IACtB,IAAItB,EAAYhH,EAAUzX,KAAKye,YAC3BzW,QAAEA,EAAO4P,KAAEA,EAAIoH,WAAEA,EAAU5G,UAAEA,GAAcpY,MAC3CqG,MAAEA,EAAKC,OAAEA,EAAMhF,QAAEA,EAAO0W,MAAEA,EAAKD,MAAEA,GAAUH,GAE7C3W,KAAM+e,EACN9f,KAAM+f,EACN/e,KAAMgf,EACNjd,YAAakd,EACbtc,IAAKuc,EACLtc,IAAKuc,EACLtc,YAAauc,EACbpd,SAAUqd,EACVjd,UAAW4Y,EACX3Y,SAAUid,EACV9c,SAAU+c,EACV9c,cAAe+c,GACb1I,GAGF/W,KAAM0f,EACNzgB,KAAM0gB,EACN1f,KAAM2f,EACN5d,YAAa6d,EACbjd,IAAKkd,EACLjd,IAAKkd,EACLjd,YAAakd,EACb/d,SAAUge,EACV5d,UAAW6X,EACX5X,SAAU4d,EACVzd,SAAU0d,EACVzd,cAAe0d,GACbtJ,GAEE9W,KAAMqgB,EAAe5f,UAAW6f,EAAoBne,IAAKoe,EAAcre,KAAMse,GAAkBlB,GAEnGtf,KAAMygB,EACNhgB,UAAWigB,EACXve,IAAKwe,EACL3d,OAAQ4d,EACRC,UAAWC,EACX7E,OAAQ8E,GACN9F,GACEjb,KAAMghB,EAAelgB,UAAWmgB,EAAgBze,OAAQ0e,EAAiB3e,eAAgB4e,EAAgBN,UAAWO,GAAuB7B,GAC3Ivf,KAAMqhB,EAAevgB,UAAWwgB,GAAmB9B,GACnDxf,KAAMuhB,EAAoBzgB,UAAW0gB,EAAqBjf,eAAgBkf,GAAqBZ,UAAWa,IAA4BjC,GAEtIzf,KAAM2hB,GAAelhB,UAAWmhB,GAAoBzf,IAAK0f,GAAc3f,KAAM4f,IAAkB7B,GAC/FjgB,KAAM+hB,GAAgBthB,UAAWuhB,GAAqB7f,IAAK8f,GAAepB,UAAWqB,GAAqBjG,OAAQkG,IAAqBjI,GACvIla,KAAMoiB,GAAethB,UAAWuhB,GAAgB7f,OAAQ8f,GAAiB/f,eAAgBggB,GAAgB1B,UAAW2B,IAAuBtC,GAC3IlgB,KAAMyiB,GAAe3hB,UAAW4hB,IAAmBvC,GACnDngB,KAAM2iB,GAAoB7hB,UAAW8hB,GAAqBrgB,eAAgBsgB,GAAqBhC,UAAWiC,IAA4B1C,GAEtI1f,SAAUqiB,IAAsBzC,GAChC5f,SAAU4Z,IAAuBoG,GACjC3f,UAAWiiB,IAAuB/B,GAClClgB,UAAWkiB,IAAmB3B,GAC9BvgB,UAAWmiB,IAAwB1B,GAEnC9gB,SAAUyiB,IAAsBvB,IAChClhB,SAAU2Z,IAAuB2H,IACjCjhB,UAAWqiB,IAAuBf,IAClCthB,UAAWsiB,IAAmBX,IAC9B3hB,UAAWuiB,IAAwBV,GAGrC5b,GAAS3G,EAAQ,GACjB6G,GAAO9B,EAAQ/E,EAAQ,GACvB4G,GAAS5B,EAAShF,EAAQ,GAAK0d,EAAWc,aAC1C1X,GAAO9G,EAAQ,GAEnB,GAAImd,EAAUxX,aAAewX,EAAUxX,YAAYxD,OAAQ,CACzD,IAAIxC,KAAEA,EAAIqF,OAAEA,EAAM5D,OAAEA,EAAMX,UAAEA,GAAc0c,EAAuB,YAAE,GAAG9Z,KAClE3C,UAAEA,GAAcD,EAEhBd,IACFiH,IAAU5B,EAAS5D,EAASV,GAIhC,IAKEwiB,GACAC,GACAC,GACA9b,GACA4R,GACAmK,GACAC,GACAC,GACAlc,GACA0R,GACAyK,GACApK,GACAD,GACAsK,GACAxK,GACAD,GApBE0K,GAAa/c,GACbgd,GAAa/c,GAEbgd,IAAc,EAChBC,IAAc,EA0DhB,SAASC,GAAaC,EAAO,KAC3B,IAAIC,EAAgB,GAChBC,EAAgB,GAChBC,EAAa,GAEjBja,OAAOoD,KAAK8P,GAAWne,QAAQJ,IACjB,OAARA,IACFue,EAAe,IAAEne,QAAQwX,IACnBA,EAAWjF,OACRyS,EAAcxN,EAAWjF,SAC5ByS,EAAcxN,EAAWjF,OAAS,IAGpCyS,EAAcxN,EAAWjF,OAAOzS,KAAK0X,EAAW5W,QAE3CokB,EAAcxN,EAAWgF,QAC5BwI,EAAcxN,EAAWgF,MAAQ,IAGnCwI,EAAcxN,EAAWgF,MAAM1c,KAAK0X,EAAW5W,SAInDqkB,EAAcrlB,GAAQolB,GAGZ,QAARplB,GAA0B,WAARA,GAA6B,eAARA,GACzCue,EAAUve,GAAMI,QAAQwX,IACjByN,EAAcrlB,KACjBqlB,EAAcrlB,GAAQ,IAGxBqlB,EAAcrlB,GAAME,KAAK0X,EAAW5W,UAK1CqK,OAAOoD,KAAK4W,GAAejlB,QAAQJ,IACrB,OAARA,GACFqL,OAAOoD,KAAK4W,EAAcrlB,IAAOI,QAAQyN,IACvC,GAAIuX,EAAcvX,GAAKtK,OAAS,EAAG,CACjC,IAAIgiB,EAAeH,EAAcvX,GAAK2X,OAAO,CAACD,EAAcE,KAC/B,GAAvBF,EAAahiB,OACfgiB,EAAeE,EAEfA,EAAQrlB,QAAQ,CAACse,EAAUC,KACzB4G,EAAa5G,IAAcD,IAGxB6G,GACN,IACHD,EAAaA,EAAWhO,OAAOiO,OAC1B,CACL,IAAIE,EAAUL,EAAcvX,GAAK,GACjCyX,EAAaA,EAAWhO,OAAOmO,MAKzB,QAARzlB,GACFqlB,EAAcrlB,GAAMI,QAAQqlB,IAC1B,IAAIC,EAAaD,EAAQD,OAAO,CAACxkB,EAAM0d,KACrCA,EAAWtT,OAAOsT,GACbiH,MAAMjH,IAAgC,iBAAZA,GAC7B1d,EAAKd,KAAKwe,GAEL1d,GACN,IAEHskB,EAAaA,EAAWhO,OAAOoO,KAIvB,WAAR1lB,GACFqlB,EAAcrlB,GAAMI,QAAQqlB,IAC1B,IAAIC,EAAaD,EAAQD,OAAO,CAACxkB,EAAM0d,KACrCA,EAAWtT,OAAOsT,EAASyG,IACtBQ,MAAMjH,IAAgC,iBAAZA,GAC7B1d,EAAKd,KAAKwe,GAEL1d,GACN,IAEHskB,EAAaA,EAAWhO,OAAOoO,KAIvB,eAAR1lB,GACFqlB,EAAcrlB,GAAMI,QAAQqlB,IAC1B,IAAIC,EAAaD,EAAQD,OAAO,CAACxkB,EAAM0d,KACrCA,EAAWtT,OAAOsT,EAAS,IACtBiH,MAAMjH,IAAgC,iBAAZA,GAC7B1d,EAAKd,KAAKwe,GAEL1d,GACN,IAEHskB,EAAaA,EAAWhO,OAAOoO,OAKrC,IAAIE,EAAmB,GACnB/hB,EAAsB,KAARshB,EAAc/E,EAAmBW,EAC/Cpd,EAAc,KAARwhB,EAAcjF,EAAWW,EAC/Bjd,EAAc,KAARuhB,EAAchF,EAAWW,EAC/B+E,EAAUnc,KAAK/F,OAAO2hB,GACtBQ,EAAUpc,KAAK9F,OAAO0hB,GACtBS,EAAY,EACZC,EAAgB,EAChBC,EAAQ,EACRC,EAAW,EAGfviB,EAAa,QAAPA,EAAgBA,EAAMyH,OAAOzH,GACnCC,EAAa,QAAPA,EAAgBA,EAAMwH,OAAOxH,GAGxB,QAAPD,GAAwB,QAAPC,GAEjBiiB,EADS,QAAPliB,EACQkiB,GAAW,GAAKC,GAAW,EAAI,EAAID,EAEnCliB,EAGVmiB,EADS,QAAPliB,EACQiiB,GAAW,GAAKC,GAAW,EAAI,EAAIA,EAEnCliB,EAEZmiB,EAAYF,EAAUC,IAItBD,EAAUliB,EACVmiB,EAAUliB,EACVmiB,EAAYF,EAAUC,GAMxB,IAAIK,IAAaN,EAAU,GAAKC,EAAU,GA+B1C,IA5BEG,EADEF,GAAa,IACP,IAECA,GAAa,IACd,IAECA,GAAa,IACd,GAECA,GAAa,GACd,EAECA,GAAa,EACd,GAECA,GAAa,GACd,IAECA,GAAa,IACd,KAECA,GAAa,KACd,KAGA,KAIHE,EAAQ,GACbA,GAAS,GACTC,GAAY,GAKd,GAAW,QAAPviB,GAAwB,QAAPC,EACnB,GAAIiiB,GAAW,GAAKC,GAAW,EAAG,CAGhC,IAFAC,GAAwBG,EACxBF,EAAgBtc,KAAK0c,KAAKL,EAAYliB,GAC/BmiB,EAAgBC,GAAU,GAC/BD,GAAiB,EAEnBA,GAAgCE,EAChCH,EAAYC,EAAgBniB,EAG5BgiB,EAAUC,EAAUC,OAEf,GAAIF,GAAW,GAAKC,GAAW,EAAG,CAGvC,IAFAC,GAAwBG,EACxBF,EAAgBtc,KAAK2c,MAAMN,EAAYliB,GAChCmiB,EAAgBC,GAAU,GAC/BD,GAAiB,EAEnBA,GAAgCE,EAChCH,EAAYC,EAAgBniB,EAG5BiiB,EAAUD,EAAUE,MAEf,CAGL,IAFAA,GAAwBG,EACxBF,EAAgBtc,KAAK0c,KAAKL,EAAYliB,GAC/BmiB,EAAgBC,GAAU,GAC/BD,GAAiB,EAEnBA,GAAgCE,EAGhCN,EAAiB1lB,KAAK,GAEtB,IAAIc,EAAO,EACX,KAAOA,EAAO6kB,GACZ7kB,GAAQglB,EACRJ,EAAiB1lB,KAAKc,GAKxB,IAHA6kB,EAAU7kB,EAEVA,EAAO,EACAA,EAAO8kB,GACZ9kB,GAAQglB,EACRJ,EAAiBU,QAAQtlB,GAE3B8kB,EAAU9kB,EACV+kB,EAAYF,EAAUC,EAO1B,GAAW,QAAPniB,GAA+B,iBAAPC,EAC1B,GAAIiiB,GAAW,GAAKC,GAAW,EAAG,CAGhC,IAFAC,GAAwBG,EACxBF,EAAgBtc,KAAK0c,KAAKL,EAAYliB,GAC/BmiB,EAAgBC,GAAU,GAC/BD,GAAiB,EAEnBA,GAAgCE,EAChCH,EAAYC,EAAgBniB,EAG5BgiB,EAAUC,EAAUC,OAEf,GAAIF,GAAW,GAAKC,GAAW,EACpCC,GAAwBG,EACxBF,EAAgB5a,QAAQ2a,EAAYliB,GAAaqX,QAAQ,IACzD8K,GAAgCE,EAChCH,EAAYC,EAAgBniB,MAIvB,CAGL,IAFAkiB,GAAwBG,EACxBF,EAAgBtc,KAAK0c,KAAKL,EAAYliB,GAC/BmiB,EAAgBC,GAAU,GAC/BD,GAAiB,EAEnBA,GAAgCE,EAGhCN,EAAiB1lB,KAAK,GAEtB,IAAIc,EAAO,EACX,KAAOA,EAAO6kB,GACZ7kB,GAAQglB,EACRJ,EAAiB1lB,KAAKc,GAKxB,IAHA6kB,EAAU7kB,EAEVA,EAAO,EACAA,EAAOglB,EAAgBF,GAC5B9kB,GAAQglB,EACRJ,EAAiBU,QAAQtlB,GAE3B4kB,EAAiBU,QAAQR,GAEzBC,EAAYF,EAAUC,EAO1B,GAAkB,iBAAPniB,GAA0B,QAAPC,EAG5B,GAAIiiB,GAAW,GAAKC,GAAW,EAC7BC,GAAwBG,EACxBF,EAAgB5a,QAAQ2a,EAAYliB,GAAaqX,QAAQ,IACzD8K,GAAgCE,EAChCH,EAAYC,EAAgBniB,OAIvB,GAAIgiB,GAAW,GAAKC,GAAW,EAAG,CAGvC,IAFAC,GAAwBG,EACxBF,EAAgBtc,KAAK2c,MAAMN,EAAYliB,GAChCmiB,EAAgBC,GAAU,GAC/BD,GAAiB,EAEnBA,GAAgCE,EAChCH,EAAYC,EAAgBniB,EAG5BiiB,EAAUD,EAAUE,MAEf,CAGL,IAFAA,GAAwBG,EACxBF,EAAgBtc,KAAK0c,KAAKL,EAAYliB,GAC/BmiB,EAAgBC,GAAU,GAC/BD,GAAiB,EAEnBA,GAAgCE,EAGhCN,EAAiB1lB,KAAK,GAEtB,IAAIc,EAAO,EACX,KAAOA,EAAOglB,EAAgBH,GAC5B7kB,GAAQglB,EACRJ,EAAiB1lB,KAAKc,GAKxB,IAHA4kB,EAAiB1lB,KAAK2lB,GAEtB7kB,EAAO,EACAA,EAAO8kB,GACZ9kB,GAAQglB,EACRJ,EAAiBU,QAAQtlB,GAE3B8kB,EAAU9kB,EAEV+kB,EAAYF,EAAUC,EAO1B,GAAkB,iBAAPniB,GAAiC,iBAAPC,EAGnC,GAAIiiB,GAAW,GAAKC,GAAW,EAC7BE,EAAgB5a,QAAQ2a,EAAYliB,GAAaqX,QAAQ,SAGpD,GAAI2K,GAAW,GAAKC,GAAW,EACpCE,EAAgB5a,QAAQ2a,EAAYliB,GAAaqX,QAAQ,QAGpD,CAIL,IAHA6K,GAAwBG,EACxBF,EAAgBtc,KAAK0c,KAAKL,EAAYliB,GAE/BmiB,EAAgBC,GAAU,GAC/BD,GAAiB,EAEnBA,GAAgCE,EAGhCN,EAAiB1lB,KAAK,GAEtB,IAAIc,EAAO,EACX,KAAOA,EAAOglB,EAAgBH,GAC5B7kB,GAAQglB,EACRJ,EAAiB1lB,KAAKc,GAKxB,IAHA4kB,EAAiB1lB,KAAK2lB,GAEtB7kB,EAAO,EACAA,EAAOglB,EAAgBF,GAC5B9kB,GAAQglB,EACRJ,EAAiBU,QAAQtlB,GAE3B4kB,EAAiBU,QAAQR,GAEzBC,EAAYF,EAAUC,EAO1B,GAAIK,EACF,IAAK,IAAII,EAAI,EAAGA,GAAK1iB,EAAa0iB,IAAK,CACrC,IAAIvlB,EAAO8kB,EAAUE,EAAgBO,EACrCvlB,EAAOA,EAAKka,QAAQgL,EAAS5b,WAAW/G,OAAS,GACjDqiB,EAAiB1lB,KAAKkL,OAAOpK,IAkBjC,MAdY,KAARmkB,GACF/K,GAAa2L,EACblB,GAAWgB,EACXxL,GAAWyL,EACXb,GAAckB,IAEd5L,GAAawL,EACbnB,GAAWiB,EACXrL,GAAWsL,EACXd,GAAcmB,GAKTP,EAjcT1N,EAAUG,SAAW,CACnBtQ,OAAQ,KACRE,KAAM,KACND,OAAQ,KACRE,KAAM,KAEN8c,YAAa,KACbC,YAAa,KAEbX,MAAO,KACPC,aAAc,KACdC,cAAe,KACf9b,SAAU,KACV4R,aAAc,KACdmK,MAAO,KACPC,aAAc,KACdC,cAAe,KACflc,SAAU,KACV0R,aAAc,KAEdyK,SAAU,KACVpK,SAAU,KACVD,WAAY,KACZsK,SAAU,KACVxK,SAAU,KACVD,WAAY,KAEZjC,gBAAiB,GACjBqO,eAAgB,GAChBC,eAAgB,GAChBC,oBAAqB,GACrBC,eAAgB,GAEhBvO,gBAAiB,GACjBwO,eAAgB,GAChBC,eAAgB,GAChBC,oBAAqB,GACrBC,eAAgB,IAgalB,IAAIC,GAAiC,YAAbjH,EAA0BC,EAAYkF,GAAa,KACvE+B,GAAiC,YAAbvG,EAA0BC,EAAYuE,GAAa,KAG3Epd,EAAQH,KAAU0T,GAAH,KACf,IAAI6L,GAAqB,EACrBC,GAAsB,EACtBC,GAAoBJ,GAAkBxB,OAAO,CAAC4B,EAAmB1I,EAAUC,KAC7E,IAAI1b,EAAO6e,EAAmBA,EAAiBpD,GAAYA,EAG3D,OAFAwI,GAAqBxd,KAAK/F,IAAImE,EAAQyT,YAAYtY,GAAMkD,MAAO+gB,IAC/DE,EAAkBlnB,KAAK+C,GAChBmkB,GACN,IAEHzF,EAAmBvW,OAAOuW,GAGxBwF,GADsB,GAApBxF,EACoBtG,GAGpB3R,KAAKkP,IAAIsO,GAAqBxd,KAAK2d,IAAK1F,EAAmBjY,KAAKqP,GAAM,MAAQrP,KAAKkP,IAAIyC,GAAqB3R,KAAK4d,IAAK3F,EAAmBjY,KAAKqP,GAAM,MAIxJjR,EAAQH,KAAUyT,GAAH,KACf,IAAImM,GAAqB,EACrBC,GAAoBP,GAAkBzB,OAAO,CAACgC,EAAmB9I,EAAUC,KAC7E,IAAI1b,EAAOigB,GAAmBA,GAAiBxE,GAAYA,EAG3D,OAFA6I,GAAqB7d,KAAK/F,IAAImE,EAAQyT,YAAYtY,GAAMkD,MAAOohB,IAC/DC,EAAkBtnB,KAAK+C,GAChBukB,GACN,IAECC,GAAiB,EAEnBA,GADe,YAAb1H,GACeE,EAAmB+G,GAAkBzjB,OAErCyjB,GAAkBzjB,OAAS,EAG9C,IAAImkB,GAAiB,EAyDrB,GAvDEA,GADe,YAAbhH,GACeE,EAAmBqG,GAAkB1jB,OAErC0jB,GAAkB1jB,OAAS,EAI1Cuc,GAAa0B,IACfxZ,IAAUmf,GAAsBzF,GAE9B5B,GAAaiC,IACG,SAAbrB,GAAwBsE,IAA6B,YAAbtE,KAC3C1Y,IAAUia,GAGV+C,GACElF,GAAasC,IACfpa,IAAUgc,GAAiB,GAGzBlE,GAAawC,IACfta,IAAUic,GAAsB,GAKhCxD,GAAaiC,KACfxa,IAAQgc,GAAoBtB,IAE9Bla,GAAWV,GAASE,GAEpBoS,GAAe5Q,KAAK2c,MAAM3d,GAAWgf,IACrCxf,GAAOF,GAASsS,GAAeoN,GAC/Bhf,GAAWV,GAASE,GAIhB4a,KACF/a,IAAUwf,GAAqBvE,IAE7BvC,GAAa0C,KACG,SAAbpD,GAAwBkF,IAA6B,YAAblF,KAC3ChY,IAAUsb,IAGV4B,GACExE,GAAa+C,KACfzb,IAAUqc,GAAiB,GAGzB3D,GAAaiD,KACf3b,IAAUsc,GAAsB,GAKhCvE,GAAasB,EAAe,CAC9BtZ,EAAQH,KAAUmc,GAAH,KACf,IAAI6D,EAAqB7f,EAAQyT,YAAYgG,GAAepb,MAC5D8B,IAAQ0f,EAAqBrG,EA2E/B,GAzEA7Y,GAAWR,GAAOF,GAElBoS,GAAezQ,KAAK2c,MAAM5d,GAAWgf,IACrCxf,GAAOF,GAASoS,GAAesN,GAC/Bhf,GAAWR,GAAOF,GAID,SAAb2Y,GAAyBsE,IAC3BiC,GAAkBzB,OAAO,CAACpM,EAAKvQ,EAAM4E,KACnC,GAAa,GAATA,EACF2L,EAAIlZ,KAAK,CACPyK,EAAG3C,SAEA,CACL,IAAI4f,EAAWle,KAAKkP,IAAIqO,GAAkBxZ,EAAQ,GAAKwZ,GAAkBxZ,IAAU/E,GAAY6R,GAE/FnB,EAAIlZ,KAAK,CACPyK,EAAGyO,EAAI3L,EAAQ,GAAG9C,EAAIid,IAkB1B,OAdY,GAAR/e,IACFyb,GAAQlL,EAAI3L,GAAO9C,GAIjB8C,EAAQ,GAAKwZ,GAAkB1jB,SACjC2E,GAAOkR,EAAI3L,GAAO9C,EAClBjC,GAAWV,GAASE,GAGpBqc,GAAeD,GAAQpc,GACvBsc,GAAgBxc,GAASsc,IAGpBlL,GACN,IAIY,SAAb2G,GAAyBkF,IAC3B+B,GAAkBxB,OAAO,CAACpM,EAAKvQ,EAAM4E,KACnC,GAAa,GAATA,EACF2L,EAAIlZ,KAAK,CACPmK,EAAGtC,SAEA,CACL,IAAI6f,EAAWle,KAAKkP,IAAIoO,GAAkBvZ,GAASuZ,GAAkBvZ,EAAQ,IAAMhF,GAAY2R,GAC/FhB,EAAIlZ,KAAK,CACPmK,EAAG+O,EAAI3L,EAAQ,GAAGpD,EAAIud,IAkB1B,OAdY,GAAR/e,IACF4b,GAAQrL,EAAI3L,GAAOpD,GAIjBoD,EAAQ,GAAKuZ,GAAkBzjB,SACjC0E,GAAOmR,EAAI3L,GAAOpD,EAClB5B,GAAWR,GAAOF,GAGlB2c,GAAezc,GAAOwc,GACtBE,GAAgBF,GAAQ1c,IAGnBqR,GACN,IAIY,SAAbsH,EAAsB,CACxB,IAAImH,EAAU/C,GA2Bd,GA1BIrE,GAAaqC,KACf+E,GAAWN,IAIbrP,EAAUG,SAASD,gBAAkBoP,GAAkBhC,OAAO,CAACpN,EAAiBvP,EAAM4E,KACpF,GAAa,GAATA,EACF2K,EAAgBlY,KAAK,CACnB+C,KAAM4F,EACNwB,EAAGwd,EACHld,EAAG3C,SAEA,CACL,IAAI4f,EAAWle,KAAKkP,IAAIqO,GAAkBxZ,EAAQ,GAAKwZ,GAAkBxZ,IAAU/E,GAAY6R,GAE/FnC,EAAgBlY,KAAK,CACnB+C,KAAM4F,EACNwB,EAAGwd,EACHld,EAAGyN,EAAgB3K,EAAQ,GAAG9C,EAAIid,IAItC,OAAOxP,GACN,IAGCqI,GAAaiD,GAAoB,CACnC,IAAImE,EAAU/C,GACVrE,GAAaqC,KACf+E,GAAWN,GAAqBvE,IAE9BvC,GAAa0C,IACX8B,KACF4C,GAAWxE,IAIfnL,EAAUG,SAASyO,oBAAsB5O,EAAUG,SAASD,gBAAgBoN,OAAO,CAACsB,EAAqBje,EAAM4E,KAC7GqZ,EAAoB5mB,KAAK,CACvB4nB,OAAQD,EACRE,OAAQlf,EAAK8B,EACbqd,KAAM/f,GACNggB,KAAMpf,EAAK8B,IAENmc,GACN,IAIL,GAAIrG,GAAa0C,GAAe,CAC9B,IAAI0E,EAAU/C,GAEVG,GACExE,GAAaqC,KACf+E,GAAWN,GAAqBvE,IAGlC6E,EAAUpD,GAAQT,GAAiB,EAAIX,GAGzCnL,EAAUG,SAASuO,eAAiB1O,EAAUG,SAASD,gBAAgBoN,OAAO,CAACoB,EAAgB/d,EAAM4E,KACnGmZ,EAAe1mB,KAAK,CAClB4nB,OAAQD,EACRE,OAAQlf,EAAK8B,EACbqd,KAAMH,EAAUxE,GAChB4E,KAAMpf,EAAK8B,IAENic,GACN,IAIL,GAAInG,GAAa+C,GAAe,CAC9B,IAAIqE,EAAU/C,GACVG,IACExE,GAAaqC,KACf+E,GAAWN,GAAqBvE,IAE9BvC,GAAa0C,KACf0E,GAAWxE,IAEbwE,GAAWzD,GAAiB,GAE5ByD,EAAUpD,GAGZvM,EAAUG,SAASwO,eAAiB,CAClCiB,OAAQD,EACRE,OAAQ/f,GACRggB,KAAMH,EACNI,KAAM/f,GAAOic,GAAqB,GAKtC,GAAI1D,GAAaiC,GAAe,CAC9B,IAAImF,EAAU/C,GACVG,IACExE,GAAaqC,KACf+E,GAAWN,GAAqBvE,IAE9BvC,GAAa0C,KACf0E,GAAWxE,IAET5C,GAAa+C,KACfqE,GAAWzD,GAAiB,IAG9ByD,EAAUpD,GAGZvM,EAAUG,SAAS0O,eAAiB,CAClC9jB,KAAM4f,GACNxY,EAAGwd,EACHld,EAAGzC,GAAO0a,KAMhB,GAAiB,SAAb7C,EAAsB,CACxB,IAAImI,EAAUnD,GA2Bd,GA1BIjF,GAAa0B,IACf0G,GAAWf,IAIbjP,EAAUG,SAASF,gBAAkBiP,GAAkB5B,OAAO,CAACrN,EAAiBtP,EAAM4E,KACpF,GAAa,GAATA,EACF0K,EAAgBjY,KAAK,CACnB+C,KAAM4F,EACNwB,EAAGtC,GACH4C,EAAGud,QAEA,CACL,IAAIN,EAAWle,KAAKkP,IAAIoO,GAAkBvZ,GAASuZ,GAAkBvZ,EAAQ,IAAMhF,GAAY2R,GAE/FjC,EAAgBjY,KAAK,CACnB+C,KAAM4F,EACNwB,EAAG8N,EAAgB1K,EAAQ,GAAGpD,EAAIud,EAClCjd,EAAGud,IAIP,OAAO/P,GACN,IAGC2H,GAAawC,EAAoB,CACnC,IAAI4F,EAAUnD,GACVjF,GAAa0B,IACf0G,GAAWf,GAAsBzF,GAE/B5B,GAAaiC,GACXiD,KACFkD,GAAWjG,GAIf/J,EAAUG,SAASqO,oBAAsBxO,EAAUG,SAASF,gBAAgBqN,OAAO,CAACkB,EAAqB7d,EAAM4E,KAC7GiZ,EAAoBxmB,KAAK,CACvB4nB,OAAQjf,EAAKwB,EACb0d,OAAQG,EACRF,KAAMnf,EAAKwB,EACX4d,KAAM/f,KAEDwe,GACN,IAIL,GAAI5G,GAAaiC,EAAe,CAC9B,IAAImG,EAAUnD,GACVC,GACEvE,GAAaqC,KACfoF,GAAWf,GAAsBzF,GAGnCwG,EAAU5D,GAAQN,GAAiB,EAAIX,GAGzCnL,EAAUG,SAASmO,eAAiBtO,EAAUG,SAASF,gBAAgBqN,OAAO,CAACgB,EAAgB3d,EAAM4E,KACnG+Y,EAAetmB,KAAK,CAClB4nB,OAAQjf,EAAKwB,EACb0d,OAAQG,EACRF,KAAMnf,EAAKwB,EACX4d,KAAMC,EAAUjG,IAEXuE,GACN,IAIL,GAAI1G,GAAasC,EAAe,CAC9B,IAAI8F,EAAUnD,GACVC,IACElF,GAAa0B,IACf0G,GAAWf,GAAsBzF,GAE/B5B,GAAaiC,IACfmG,GAAWjG,GAEbiG,GAAWlE,GAAiB,GAE5BkE,EAAU5D,GAGZpM,EAAUG,SAASoO,eAAiB,CAClCqB,OAAQ/f,GACRggB,OAAQG,EACRF,KAAM/f,GAAO8b,GAAqB,EAClCkE,KAAMC,GAKV,GAAIpI,GAAasB,EAAe,CAC9B,IAAI8G,EAAUnD,GACVE,IACEnF,GAAa0B,IACf0G,GAAWf,GAAsBzF,GAE/B5B,GAAaiC,IACfmG,GAAWjG,GAETnC,GAAasC,IACf8F,GAAWlE,GAAiB,IAG9BkE,EAAU5D,GAGZpM,EAAUG,SAASsO,eAAiB,CAClC1jB,KAAMse,EACNlX,EAAGpC,GAAOqZ,EACV3W,EAAGud,IAMT,GAAiB,YAAbxH,EAAyB,CA4C3B,GA1CAxI,EAAUG,SAASD,gBAAkBoP,GAAkBhC,OAAO,CAACpN,EAAiBvP,EAAM4E,KACpF,IAAIoa,EAAU/C,GAqBd,OApBIrE,GAAaqC,KACf+E,GAAWN,IAGT3G,EACFxI,EAAgBlY,KAAK,CACnBa,MAAM,EACNkC,KAAM4F,EACNwB,EAAGwd,EACHld,EAAG3C,GAASsS,IAAgB7M,EAAQ,GAAK6M,GAAe,IAG1DlC,EAAgBlY,KAAK,CACnBa,MAAM,EACNkC,KAAM4F,EACNwB,EAAGwd,EACHld,EAAG3C,GAASsS,GAAe7M,IAIxB2K,GACN,IAEC6K,IAAuBA,GAAoB1f,SAC7C2U,EAAUG,SAASD,gBAAkBF,EAAUG,SAASD,gBAAgBtL,IAAI,CAACjE,EAAM4E,KACjF,IAAI0a,EAASlF,GAAoBvI,KAAKkH,GAC7BA,IAAcnU,GASvB,OALE5E,EAAK9H,OADHonB,EAMGtf,KAKP4X,GAAaiD,GAAoB,CACnC,IAAImE,EAAU/C,GACVrE,GAAaqC,KACf+E,GAAWN,GAAqBvE,IAE9BvC,GAAa0C,IACX8B,KACF4C,GAAWxE,IAKf,IAAI+E,EAAuB,EAEzBA,EADExH,EACqBgD,GAAsBqD,GAAkB1jB,OAAS0jB,GAAkB1jB,OAAS,EAE5E0jB,GAAkB1jB,OAG3C,IAAK,IAAIkK,EAAQ,EAAGA,EAAQ2a,EAAsB3a,IAC5CmT,GAAoBgD,GACtB1L,EAAUG,SAASyO,oBAAoB5mB,KAAK,CAC1Ca,MAAM,EACN+mB,OAAQD,EACRE,OAAQ/f,GAASsS,GAAe7M,EAAQ6M,GAAe,EACvD0N,KAAM/f,GACNggB,KAAMjgB,GAASsS,GAAe7M,EAAQ6M,GAAe,IAGvDpC,EAAUG,SAASyO,oBAAoB5mB,KAAK,CAC1Ca,MAAM,EACN+mB,OAAQD,EACRE,OAAQ/f,GAASsS,GAAe7M,EAChCua,KAAM/f,GACNggB,KAAMjgB,GAASsS,GAAe7M,IAuBtC,GAjBIoW,IAA2BA,GAAwBtgB,SACrD2U,EAAUG,SAASyO,oBAAsB5O,EAAUG,SAASyO,oBAAoBha,IAAI,CAACjE,EAAM4E,KACzF,IAAI0a,EAAStE,GAAwBnJ,KAAKkH,GACjCA,IAAcnU,GASvB,OALE5E,EAAK9H,OADHonB,EAMGtf,KAKP4X,GAAa0C,GAAe,CAC9B,IAAI0E,EAAU/C,GAEVG,GACExE,GAAaqC,KACf+E,GAAWN,GAAqBvE,IAGlC6E,EAAUpD,GAAQL,GAAiB,EAAIf,GAIzC,IAAIgF,EAAkB,EAEpBA,EADEzH,EACgB0C,GAAiB2D,GAAkB1jB,OAAS0jB,GAAkB1jB,OAAS,EAEvE0jB,GAAkB1jB,OAGtC,IAAK,IAAIkK,EAAQ,EAAGA,EAAQ4a,EAAiB5a,IACvCmT,GAAoB0C,GACtBpL,EAAUG,SAASuO,eAAe1mB,KAAK,CACrCa,MAAM,EACN+mB,OAAQD,EACRE,OAAQ/f,GAASsS,GAAe7M,EAAQ6M,GAAe,EACvD0N,KAAMH,EAAUxE,GAChB4E,KAAMjgB,GAASsS,GAAe7M,EAAQ6M,GAAe,IAGvDpC,EAAUG,SAASuO,eAAe1mB,KAAK,CACrCa,MAAM,EACN+mB,OAAQD,EACRE,OAAQ/f,GAASsS,GAAe7M,EAChCua,KAAMH,EAAUxE,GAChB4E,KAAMjgB,GAASsS,GAAe7M,IAuBtC,GAjBI8V,IAAsBA,GAAmBhgB,SAC3C2U,EAAUG,SAASuO,eAAiB1O,EAAUG,SAASuO,eAAe9Z,IAAI,CAACjE,EAAM4E,KAC/E,IAAI0a,EAAS5E,GAAmB7I,KAAKkH,GAC5BA,IAAcnU,GASvB,OALE5E,EAAK9H,OADHonB,EAMGtf,KAKP4X,GAAa+C,GAAe,CAC9B,IAAIqE,EAAU/C,GACVG,IACExE,GAAaqC,KACf+E,GAAWN,GAAqBvE,IAE9BvC,GAAa0C,KACf0E,GAAWxE,IAEbwE,GAAWzD,GAAiB,GAE5ByD,EAAUpD,GAGZvM,EAAUG,SAASwO,eAAiB,CAClCiB,OAAQD,EACRE,OAAQ/f,GACRggB,KAAMH,EACNI,KAAM/f,GAAOic,GAAqB,GAKtC,GAAI1D,GAAaiC,GAAe,CAC9B,IAAImF,EAAU/C,GACVG,IACExE,GAAaqC,KACf+E,GAAWN,GAAqBvE,IAE9BvC,GAAa0C,KACf0E,GAAWxE,IAET5C,GAAa+C,KACfqE,GAAWzD,GAAiB,IAG9ByD,EAAUpD,GAGZvM,EAAUG,SAAS0O,eAAiB,CAClC9jB,KAAM4f,GACNxY,EAAGwd,EACHld,EAAGzC,GAAO0a,KAMhB,GAAiB,YAAb7C,EAAyB,CA2C3B,GAzCA7H,EAAUG,SAASF,gBAAkBiP,GAAkB5B,OAAO,CAACrN,EAAiBtP,EAAM4E,KACpF,IAAIya,EAAUnD,GAoBd,OAnBIjF,GAAa0B,IACf0G,GAAWf,IAGTlH,EACF9H,EAAgBjY,KAAK,CACnBa,MAAM,EACNkC,KAAM4F,EACNwB,EAAGtC,GAASoS,IAAgB1M,EAAQ,GAAK0M,GAAe,EACxDxP,EAAGud,IAGL/P,EAAgBjY,KAAK,CACnBa,MAAM,EACNkC,KAAM4F,EACNwB,EAAGtC,GAASoS,GAAe1M,EAC3B9C,EAAGud,IAGA/P,GACN,IAEC0J,GAAuBA,EAAoBte,SAC7C2U,EAAUG,SAASF,gBAAkBD,EAAUG,SAASF,gBAAgBrL,IAAI,CAACjE,EAAM4E,KACjF,IAAI0a,EAAStG,EAAoBnH,KAAKkH,GAC7BA,IAAcnU,GASvB,OALE5E,EAAK9H,OADHonB,EAMGtf,KAKPiX,GAAawC,EAAoB,CACnC,IAAI4F,EAAUnD,GACVjF,GAAa0B,IACf0G,GAAWf,GAAsBzF,GAE/B5B,GAAaiC,GACXiD,KACFkD,GAAWjG,GAKf,IAAIqG,EAAuB,EAEzBA,EADErI,EACqBuC,GAAsBwE,GAAkBzjB,OAASyjB,GAAkBzjB,OAAS,EAE5EyjB,GAAkBzjB,OAG3C,IAAK,IAAIkK,EAAQ,EAAGA,EAAQ6a,EAAsB7a,IAC5CwS,GAAoBuC,GACtBtK,EAAUG,SAASqO,oBAAoBxmB,KAAK,CAC1Ca,MAAM,EACN+mB,OAAQ/f,GAASoS,GAAe1M,EAAQ0M,GAAe,EACvD4N,OAAQG,EACRF,KAAMjgB,GAASoS,GAAe1M,EAAQ0M,GAAe,EACrD8N,KAAM/f,KAGRgQ,EAAUG,SAASqO,oBAAoBxmB,KAAK,CAC1Ca,MAAM,EACN+mB,OAAQ/f,GAASoS,GAAe1M,EAChCsa,OAAQG,EACRF,KAAMjgB,GAASoS,GAAe1M,EAC9Bwa,KAAM/f,KAuBd,GAjBIua,IAA2BA,GAAwBlf,SACrD2U,EAAUG,SAASqO,oBAAsBxO,EAAUG,SAASqO,oBAAoB5Z,IAAI,CAACjE,EAAM4E,KACzF,IAAI0a,EAAS1F,GAAwB/H,KAAKkH,GACjCA,IAAcnU,GASvB,OALE5E,EAAK9H,OADHonB,EAMGtf,KAKPiX,GAAaiC,EAAe,CAC9B,IAAImG,EAAUnD,GAEVC,IACElF,GAAa0B,IACf0G,GAAWf,GAAsBzF,GAE/B5B,GAAaiC,IACfmG,GAAWjG,IAGbiG,EAAU5D,GAAQN,GAAiB,EAIrC,IAAIuE,EAAkB,EAEpBA,EADEtI,EACgBiC,EAAiB8E,GAAkBzjB,OAASyjB,GAAkBzjB,OAAS,EAEvEyjB,GAAkBzjB,OAGtC,IAAK,IAAIkK,EAAQ,EAAGA,EAAQ8a,EAAiB9a,IACvCwS,GAAoBiC,EACtBhK,EAAUG,SAASmO,eAAetmB,KAAK,CACrCa,MAAM,EACN+mB,OAAQ/f,GAASoS,GAAe1M,EAAQ0M,GAAe,EACvD4N,OAAQG,EACRF,KAAMjgB,GAASoS,GAAe1M,EAAQ0M,GAAe,EACrD8N,KAAMC,EAAUjG,IAGlB/J,EAAUG,SAASmO,eAAetmB,KAAK,CACrCa,MAAM,EACN+mB,OAAQ/f,GAASoS,GAAe1M,EAChCsa,OAAQG,EACRF,KAAMjgB,GAASoS,GAAe1M,EAC9Bwa,KAAMC,EAAUjG,IAuBxB,GAjBIE,GAAsBA,EAAmB5e,SAC3C2U,EAAUG,SAASmO,eAAiBtO,EAAUG,SAASmO,eAAe1Z,IAAI,CAACjE,EAAM4E,KAC/E,IAAI0a,EAAShG,EAAmBzH,KAAKkH,GAC5BA,IAAcnU,GASvB,OALE5E,EAAK9H,OADHonB,EAMGtf,KAKPiX,GAAasC,EAAe,CAC9B,IAAI8F,EAAUnD,GACVC,IACElF,GAAa0B,IACf0G,GAAWf,GAAsBzF,GAE/B5B,GAAaiC,IACfmG,GAAWjG,GAEbiG,GAAWlE,GAAiB,GAE5BkE,EAAU5D,GAGZpM,EAAUG,SAASoO,eAAiB,CAClCqB,OAAQ/f,GACRggB,OAAQG,EACRF,KAAM/f,GAAO8b,GAAqB,EAClCkE,KAAMC,GAKV,GAAIpI,GAAasB,EAAe,CAC9B,IAAI8G,EAAUnD,GACVC,IACElF,GAAa0B,IACf0G,GAAWf,GAAsBzF,GAE/B5B,GAAaiC,IACfmG,GAAWjG,GAETnC,GAAasC,IACf8F,GAAWlE,GAAiB,IAG9BkE,EAAU5D,GAGZpM,EAAUG,SAASsO,eAAiB,CAClC1jB,KAAMse,EACNlX,EAAGpC,GAAOqZ,EACV3W,EAAGud,IAKThQ,EAAUG,SAAStQ,OAASA,GAC5BmQ,EAAUG,SAASpQ,KAAOA,GAC1BiQ,EAAUG,SAASrQ,OAASA,GAC5BkQ,EAAUG,SAASnQ,KAAOA,GAE1BgQ,EAAUG,SAAS2M,YAAcA,GACjC9M,EAAUG,SAAS4M,YAAcA,GAEjC/M,EAAUG,SAASiM,MAAQA,GAC3BpM,EAAUG,SAASkM,aAAeA,GAClCrM,EAAUG,SAASmM,cAAgBA,GACnCtM,EAAUG,SAAS3P,SAAWA,GAC9BwP,EAAUG,SAASiC,aAAeA,GAClCpC,EAAUG,SAASoM,MAAQA,GAC3BvM,EAAUG,SAASqM,aAAeA,GAClCxM,EAAUG,SAASsM,cAAgBA,GACnCzM,EAAUG,SAAS5P,SAAWA,GAC9ByP,EAAUG,SAAS8B,aAAeA,GAElCjC,EAAUG,SAASuM,SAAWA,GAC9B1M,EAAUG,SAASmC,SAAWA,GAC9BtC,EAAUG,SAASkC,WAAaA,GAChCrC,EAAUG,SAASwM,SAAWA,GAC9B3M,EAAUG,SAASgC,SAAWA,GAC9BnC,EAAUG,SAAS+B,WAAaA,GAEIta,KAAKoY,UAAUG,SCn4CtC,SAASmQ,IACtB,IAAI1gB,QAAEA,EAAO4P,KAAEA,EAAIoH,WAAEA,EAAU5G,UAAEA,GAAcpY,MAC3CqG,MAAEA,EAAKC,OAAEA,EAAMhF,QAAEA,EAAO6C,UAAEA,EAASwkB,WAAEA,GAAe/Q,GACpDvT,OAAEA,EAAMC,OAAEA,EAAMP,YAAEA,EAAab,SAAU0lB,GAAkBzkB,GACzDlD,KAAM4nB,EAAmBnnB,UAAWonB,GAA2BF,GAC/DjnB,SAAUonB,EAAuBrmB,OAAQsmB,GAAwBF,GAClEne,EAASC,GAAWvG,EAEzB+T,EAAUjU,UAAY,CACpBE,OAAQ,GACRC,OAAQ,EACR2kB,gBAAiB,GACjBrL,aAAc,GACdsL,cAAeJ,GAGK,iBAAXne,IACTA,EAAUtE,EAAQ6E,EAAaP,IAEX,iBAAXC,IACTA,GAAWtE,EAAS0Y,EAAWc,aAAexe,EAAQ,IAAM4J,EAAaN,IAEtD,iBAAVtG,IACTA,GAAWgC,EAAS0Y,EAAWc,aAAexe,EAAQ,IAAM4J,EAAa5G,GAAW,GAGtF8T,EAAUjU,UAAUE,OAAS,CAACsG,EAASC,GACvCwN,EAAUjU,UAAUG,OAASA,EAE7B,IAAI6kB,EAAiB,EAAIvf,KAAKqP,GAAM0P,EAAWllB,OAC3Csa,EAAQnU,KAAKqP,GAAK,EAElBK,EAAM,GACV,IAAK,IAAImN,EAAI,EAAGA,EAAI1iB,EAAa0iB,IAAK,CACpC,IAAI2C,GAASrlB,EAAc0iB,GAAK1iB,EAChCuV,EAAImN,GAAKkC,EAAWjD,OAAO,CAACpM,EAAKvQ,EAAM4E,KACrC,IAAI0b,EAAW,CACb9e,EAAGjG,EAASsF,KAAK4d,IAAIzJ,EAAQoL,EAAgBxb,GAASyb,EACtDve,EAAGvG,EAASsF,KAAK2d,IAAIxJ,EAAQoL,EAAgBxb,GAASyb,GAGxD,OADA9P,EAAIlZ,KAAKqK,EAAwB4e,EAAUjR,EAAUjU,UAAUE,SACxDiV,GACN,IAELlB,EAAUjU,UAAU8kB,gBAAkB3P,EAEtClB,EAAUjU,UAAUyZ,aAAe+K,EAAWjD,OAAO,CAACpM,EAAKvQ,EAAM4E,KAC/D,IAAI2b,EAAQ,CACV/e,GAAIjG,EAASykB,EAAwB,EAAIC,GAAuBpf,KAAK4d,IAAIzJ,EAAQoL,EAAgBxb,GACjG9C,GAAIvG,EAASykB,EAAwB,EAAIC,GAAuBpf,KAAK2d,IAAIxJ,EAAQoL,EAAgBxb,IAE/F9G,EAAW4D,EAAwB6e,EAAOlR,EAAUjU,UAAUE,QASlE,OAPA2D,EAAQH,KAAUkhB,EAAH,KAEfzP,EAAIlZ,KAAK,CACP+C,KAAM4F,EACNugB,MAAAA,EACAziB,SAAAA,IAEKyS,GACN,IAEsCtZ,KAAKoY,UAAUjU,UChE3C,SAASolB,IACtB,IAAI3R,KAAEA,EAAIQ,UAAEA,EAASqG,UAAEA,GAAcze,MACjCgY,MAAEA,EAAKD,MAAEA,GAAUH,EACnB4R,EAAY/K,EAAe,KAE3BxW,OACFA,EAAME,KACNA,EAAID,OACJA,EAAME,KACNA,EAAIoc,MACJA,EAAKC,aACLA,EAAYC,cACZA,EAAa9b,SACbA,EAAQ4R,aACRA,EAAYmK,MACZA,EAAKC,aACLA,EAAYC,cACZA,EAAalc,SACbA,EAAQ0R,aACRA,EAAYyK,SACZA,EAAQpK,SACRA,EAAQD,WACRA,EAAUsK,SACVA,EAAQxK,SACRA,EAAQD,WACRA,EAAUjC,gBACVA,EAAeC,gBACfA,GACEF,EAAUG,SAEVkR,EAAY,EACZC,EAAkB,EAClBC,EAAW,EAEX5D,EAAwB,SAAd/N,EAAM9X,KAAkB6kB,EAAWD,EAC7CkB,EAAwB,SAAdhO,EAAM9X,KAAkBqa,EAAWG,EAC7CuL,EAA0B,SAAdjO,EAAM9X,KAAkBoa,EAAaG,EACjDmP,EAAqC,SAAd5R,EAAM9X,KAAkB0kB,EAAeH,EAC9DoF,EAAsC,SAAd7R,EAAM9X,KAAkB2kB,EAAgBH,EAChEoF,EAAiC,SAAd9R,EAAM9X,KAAkByI,EAAWC,EACtDmhB,EAAwC,YAAd/R,EAAM9X,KAAqBma,EAAeG,EACpEwP,EAAiC,YAAdhS,EAAM9X,KAAqB8X,EAAM9W,KAAO6W,EAAM7W,KAEjEqkB,EAAgB,GACpBiE,EAAUlpB,QAAQwX,IACZA,EAAWjF,OACR0S,EAAczN,EAAWjF,SAC5B0S,EAAczN,EAAWjF,OAAS,IAGpC0S,EAAczN,EAAWjF,OAAOzS,KAAK0X,KAEhCyN,EAAczN,EAAWgF,QAC5ByI,EAAczN,EAAWgF,MAAQ,IAGnCyI,EAAczN,EAAWgF,MAAM1c,KAAK0X,MAIxC,IAAIuF,EAAW,GAEf,IAAK,IAAIoJ,EAAI,EAAGwD,EAAMD,EAAiBvmB,OAAQgjB,EAAIwD,EAAKxD,IAAK,CAC3D,IAAIyD,EAAkB,GAEtB3e,OAAOoD,KAAK4W,GAAejlB,QAAQyN,IACjCmc,EAAgB9pB,KAAKqX,EAAU8N,EAAcxX,OAE/CsP,EAASjd,KAAK8pB,GAGhB7M,EAAS/c,QAAQ,CAAC6pB,EAAYC,KAC5BD,EAAW7pB,QAAQ,CAACgd,EAAS+M,KAC3B/M,EAAQhd,QAAQ,CAACwX,EAAYI,KAC3B,IAAImQ,GAAS,EASb,GARIvQ,EAAWgK,WAAahK,EAAWgK,UAAUre,SAC/C4kB,EAASvQ,EAAWgK,UAAUlH,KAAKkH,GAC1BA,GAAawI,mBAGxBxS,EAAW7W,KAAOonB,EAClBvQ,EAAW5W,KAAO4W,EAAW5W,KAAKkpB,GAEf,GAAflS,EAAkB,CACpB,IAAItT,YAAEA,EAAWC,YAAEA,EAAWC,SAAEA,EAAQC,OAAEA,GAAW+S,EAC9B,iBAAZhT,GACLA,EAAWF,IACbkT,EAAWhT,SAAWF,GAEpBE,EAAWD,IACbiT,EAAWhT,SAAWD,GAGD,GAAnBulB,IACFT,GAAY7R,EAAWhT,WAGF,GAAnBslB,GACFV,SAIJ5R,EAAWhT,SAAWwY,EAAQ,GAAGxY,WAId,GAAnBslB,IAEAT,GADc,GAAZU,EACU,EAAI/M,EAAQ,GAAGvY,OAEfuY,EAAQ,GAAGvY,YAQ7B0kB,EADEE,EAAWD,EAAkBK,GAClBA,EAA0BJ,GAAYD,EAEvC,EAIdrM,EAAS/c,QAAQ,CAAC6pB,EAAYC,KAC5BD,EAAW7pB,QAAQ,CAACgd,EAAS+M,KAC3B/M,EAAQhd,QAAQ,CAACwX,EAAYI,KAC3B,IAAItT,YAAEA,EAAWE,SAAEA,GAAagT,EACb,GAAfI,GAAgC,QAAZpT,EACtBgT,EAAWhT,SAAW2kB,EAAY7kB,EAAcA,EAAc6kB,GAE9D3R,EAAWhT,SAAWwY,EAAQ,GAAGxY,SACjCgT,EAAW/S,OAASuY,EAAQ,GAAGvY,QAGV,GAAnBqlB,GAAuC,GAAflS,GAAgC,QAAZpT,IAC9C6kB,GAAY7R,EAAWhT,gBAMb,YAAdkT,EAAM9X,KACRmd,EAAS/c,QAAQ,CAAC6pB,EAAYC,KAC5B,IAAI7f,EAAI8N,EAAgB+R,GAAiB7f,EAAIof,EAAW,EAExDQ,EAAW7pB,QAAQ,CAACgd,EAAS+M,KAC3B9f,GAAK+S,EAAQ,GAAGvY,OAASuY,EAAQ,GAAGxY,SAAW,EAE/C,IAAIylB,EAAY,EACZC,EAAY,EAEZzE,GAAW,GAAKC,GAAW,EAC7BuE,EAAYriB,EACH6d,GAAW,GAAKC,GAAW,EACpCwE,EAAYpiB,GAEZmiB,EAAY/F,EACZgG,EAAYhG,GAGdlH,EAAQhd,QAAQ,CAACwX,EAAYI,KAC3BJ,EAAWvN,EAAIA,EAGf,IAAIkgB,EAAY,EAEZ1E,GAAW,GAAKC,GAAW,EACN,GAAnBlO,EAAW5W,MACb4W,EAAWjN,EAAI3C,EACfuiB,EAAY,IAEZ3S,EAAWjN,EAAI0f,EACfE,EAAaX,GAAoBhS,EAAW5W,KAAO8kB,GAAYC,EAC/DsE,GAAaE,GAEN1E,GAAW,GAAKC,GAAW,EACb,GAAnBlO,EAAW5W,MACb4W,EAAWjN,EAAIzC,EACfqiB,EAAY,IAEZ3S,EAAWjN,EAAI2f,EACfC,EAAaX,GAAoBlgB,KAAKkP,IAAIhB,EAAW5W,MAAQ0I,KAAKkP,IAAIiN,IAAaE,EACnFuE,GAAaC,GAGX3S,EAAW5W,KAAO,GACpB4W,EAAWjN,EAAI0f,EACfE,EAAab,EAAuB9R,EAAW5W,KAAQ6kB,EACvDwE,GAAaE,GACJ3S,EAAW5W,KAAO,GAC3B4W,EAAWjN,EAAI2f,EACfC,EAAaZ,EAAwBjgB,KAAKkP,IAAIhB,EAAW5W,MAAS0I,KAAKkP,IAAIkN,GAC3EwE,GAAaC,IAEb3S,EAAWjN,EAAI2Z,EACfiG,EAAY,GAIhB3S,EAAW2S,UAAYA,IAGzBlgB,GAAK+S,EAAQ,GAAGxY,SAAW,MAI/BuY,EAAS/c,QAAQ,CAAC6pB,EAAYC,KAC5B,IAAIvf,EAAIyN,EAAgB8R,GAAiBvf,EAAI8e,EAAW,EAExDQ,EAAW7pB,QAAQ,CAACgd,EAAS+M,KAC3Bxf,GAAKyS,EAAQ,GAAGvY,OAASuY,EAAQ,GAAGxY,SAAW,EAE/C,IAAI4lB,EAAY,EACZC,EAAY,EAEZ5E,GAAW,GAAKC,GAAW,EAC7B0E,EAAYziB,EACH8d,GAAW,GAAKC,GAAW,EACpC2E,EAAYxiB,GAEZuiB,EAAY/F,EACZgG,EAAYhG,GAGdrH,EAAQhd,QAAQ,CAACwX,EAAYI,KAC3BJ,EAAWjN,EAAIA,EAGf,IAAI4f,EAAY,EAEZ1E,GAAW,GAAKC,GAAW,GAC7BlO,EAAWvN,EAAImgB,EACfD,EAAaX,GAAoBhS,EAAW5W,KAAO8kB,GAAYC,EAC/DyE,GAAaD,GACJ1E,GAAW,GAAKC,GAAW,GACpClO,EAAWvN,EAAIogB,EACfF,EAAaX,GAAoBlgB,KAAKkP,IAAIhB,EAAW5W,MAAQ0I,KAAKkP,IAAIiN,IAAaE,EACnF0E,GAAaF,GAET3S,EAAW5W,KAAO,GACpB4W,EAAWvN,EAAImgB,EACfD,EAAab,EAAuB9R,EAAW5W,KAAQ6kB,EACvD2E,GAAaD,IAEb3S,EAAWvN,EAAIogB,EACfF,EAAaZ,EAAwBjgB,KAAKkP,IAAIhB,EAAW5W,MAAS0I,KAAKkP,IAAIkN,GAC3E2E,GAAaF,GAIjB3S,EAAW2S,UAAYA,IAGzB5f,GAAKyS,EAAQ,GAAGxY,SAAW,MAKjCsT,EAAUiF,SAAWA,EAEmBjF,EAAUiF,SCrQrC,SAASuN,IACtB,IAAIhT,KAAEA,EAAIQ,UAAEA,EAASqG,UAAEA,GAAcze,MACjCgY,MAAEA,GAAUJ,EACZiT,EAAapT,EAAUgH,EAAgB,OAEvCxW,OACFA,EAAME,KACNA,EAAID,OACJA,EAAME,KACNA,EAAIoc,MACJA,EAAKC,aACLA,EAAYC,cACZA,EAAa9b,SACbA,EAAQ+b,MACRA,EAAKC,aACLA,EAAYC,cACZA,EAAalc,SACbA,EAAQmc,SACRA,EAAQpK,SACRA,EAAQD,WACRA,EAAUsK,SACVA,EAAQxK,SACRA,EAAQD,WACRA,EAAUjC,gBACVA,EAAeC,gBACfA,GACEF,EAAUG,SAEVwN,EAAwB,SAAd/N,EAAM9X,KAAkB6kB,EAAWD,EAC7CkB,EAAwB,SAAdhO,EAAM9X,KAAkBqa,EAAWG,EAC7CuL,EAA0B,SAAdjO,EAAM9X,KAAkBoa,EAAaG,EACjDmP,EAAqC,SAAd5R,EAAM9X,KAAkB0kB,EAAeH,EAC9DoF,EAAsC,SAAd7R,EAAM9X,KAAkB2kB,EAAgBH,EAChEoF,EAAiC,SAAd9R,EAAM9X,KAAkByI,EAAWC,EAEtDiU,EAAY,GAGdA,EADgB,YAAd7E,EAAM9X,KACI2qB,EAAWnF,OAAO,CAACoF,EAAchT,KAC3CA,EAAW5W,KAAO4W,EAAW5W,KAAKwkB,OAAO,CAACC,EAAS/G,EAAUC,KAC3D,IAAItU,EAAGM,EAAGvE,EA8BV,MA7BuB,iBAAZsY,IACTrU,EAAI8N,EAAgBwG,GAAWtU,EAG/BqU,GADAA,EAAWA,EAAWmH,EAAUA,EAAUnH,GACpBoH,EAAUA,EAAUpH,EAEtCmH,GAAW,GAAKC,GAAW,GAC7B1f,EAAUwjB,GAAoBlL,EAAWoH,GAAYC,EACrDpb,EAAI3C,EAAS5B,GACJyf,GAAW,GAAKC,GAAW,GACpC1f,EAAUwjB,GAAoBlgB,KAAKkP,IAAI8F,GAAYhV,KAAKkP,IAAIiN,IAAaE,EACzEpb,EAAIzC,EAAO9B,GAEPsY,EAAW,GACbtY,EAAUsjB,EAAuBhL,EAAYmH,EAC7Clb,EAAI2Z,EAAQle,IAEZA,EAAUujB,EAAwBjgB,KAAKkP,IAAI8F,GAAahV,KAAKkP,IAAIkN,GACjEnb,EAAI2Z,EAAQle,IAKlBqf,EAAQvlB,KAAK,CACXmK,EAAAA,EACAM,EAAAA,EACA3J,KAAM0d,EACNtY,OAAAA,IAEKqf,GACN,IAEHmF,EAAa1qB,KAAK0X,GAEXgT,GACN,IAESD,EAAWnF,OAAO,CAACoF,EAAchT,KAC3CA,EAAW5W,KAAO4W,EAAW5W,KAAKwkB,OAAO,CAACC,EAAS/G,EAAUC,KAC3D,IAAItU,EAAGM,EAAGvE,EA+BV,MA7BuB,iBAAZsY,IACT/T,EAAIyN,EAAgBuG,GAAWhU,EAG/B+T,GADAA,EAAWA,EAAWmH,EAAUA,EAAUnH,GACpBoH,EAAUA,EAAUpH,EAEtCmH,GAAW,GAAKC,GAAW,GAC7B1f,EAAUwjB,GAAoBlL,EAAWoH,GAAYC,EACrD1b,EAAItC,EAAS3B,GACJyf,GAAW,GAAKC,GAAW,GACpC1f,EAAUwjB,GAAoBlgB,KAAKkP,IAAI8F,GAAYhV,KAAKkP,IAAIiN,IAAaE,EACzE1b,EAAIpC,EAAO7B,GAEPsY,EAAW,GACbtY,EAAUsjB,EAAuBhL,EAAYmH,EAC7Cxb,EAAIoa,EAAQre,IAEZA,EAAUujB,EAAwBjgB,KAAKkP,IAAI8F,GAAahV,KAAKkP,IAAIkN,GACjEzb,EAAIoa,EAAQre,IAKlBqf,EAAQvlB,KAAK,CACXmK,EAAAA,EACAM,EAAAA,EACA3J,KAAM0d,EACNtY,OAAAA,IAEKqf,GACN,IAEHmF,EAAa1qB,KAAK0X,GAEXgT,GACN,IAGL1S,EAAUyE,UAAYA,EAEmBzE,EAAUyE,UCzHtC,SAASkO,IACtB,IAAInT,KAAEA,EAAIoH,WAAEA,EAAUP,UAAEA,GAAcze,MAClCqG,MAAEA,EAAKC,OAAEA,EAAMhF,QAAEA,GAAYsW,EAC7Bc,EAAWjB,EAAUgH,EAAe,IAAE,KACtCvd,KAAEA,EAAImD,OAAEA,EAAMC,OAAEA,GAAWoU,GAC1B/N,EAASC,GAAWvG,GACpB2mB,EAASC,GAAW3mB,EAErB4mB,EAAWhqB,EAAKwkB,OAAO,CAACyF,EAAKvM,IAC/BuM,GAA0B,OAAnBvM,EAASxR,MAAiB,EAAIwR,EAASxR,MAE7C,GAEmB,iBAAXzC,IACTA,EAAUtE,EAAQ6E,EAAaP,IAEX,iBAAXC,IACTA,GAAWtE,EAAS0Y,EAAWc,aAAexe,EAAQ,IAAM4J,EAAaN,IAErD,iBAAXogB,IACTA,GAAY1kB,EAAS0Y,EAAWc,aAAexe,EAAQ,IAAM4J,EAAa8f,GAAY,GAElE,iBAAXC,IACTA,GAAY3kB,EAAS0Y,EAAWc,aAAexe,EAAQ,IAAM4J,EAAa+f,GAAY,GAGxF,IAAIG,EAAWlqB,EAAKsW,OAAO,IAAI7Q,KAAK,CAAC+X,EAAGjV,IAC/BA,EAAE2D,MAAQsR,EAAEtR,OAGrBsL,EAASwS,SAAWA,EACpBxS,EAASrU,OAAS,CAACsG,EAASC,GAC5B8N,EAASpU,OAAS,CAAC0mB,EAASC,GAC5BvS,EAASqN,QAAUqF,EAAS,GAAGhe,MAC/BsL,EAASsN,QAAUoF,EAASA,EAAS3nB,OAAS,GAAG2J,MAEjDpN,KAAKoY,UAAUM,SAAWA,EAEc1Y,KAAKoY,UAAUM,SCtC1C,SAAS2S,IACtB,IAAIzT,KAAEA,EAAIQ,UAAEA,EAASqG,UAAEA,GAAcze,MACjCmE,UAAEA,EAASwkB,WAAEA,EAAU9Q,OAAEA,GAAWD,EACpC0T,EAAc7T,EAAUgH,EAAiB,QAEzC5a,IAAEA,GAAQM,GACVG,OAAEA,GAAW8T,EAAUjU,UAEvB4hB,EAAU,EACdlO,EAAOvX,QAAQwX,IACbiO,EAAUnc,KAAK/F,IAAIkiB,KAAYjO,EAAW5W,QAE5C6kB,EAAiB,QAAPliB,EAAgBkiB,EAAUliB,EAEpC,IAAI0V,EAAiB,EAAI3P,KAAKqP,GAAM0P,EAAWllB,OAC3Csa,EAAQnU,KAAKqP,GAAK,EAEtBqS,EAAYhrB,QAAQirB,IAClBA,EAAUlS,aAAekS,EAAUrqB,KAAKwkB,OAAO,CAACpM,EAAKsF,EAAUC,KAC7D,IAAIuK,EAAQxK,EAAWmH,EACnBuD,EAAQ,CACV/e,EAAGjG,EAASsF,KAAK4d,IAAIzJ,EAAQxE,EAAgBsF,GAAauK,EAC1Dve,EAAGvG,EAASsF,KAAK2d,IAAIxJ,EAAQxE,EAAgBsF,GAAauK,GAS5D,OAPA9P,EAAIlZ,KAAK,CACPc,KAAM0d,EACN0K,MAAAA,EACA/P,cAAAA,EACAL,QAASK,EAAgBsF,IAGpBvF,GACN,MAGLlB,EAAUgB,WAAakS,EAEmBtrB,KAAKoY,UAAUgB,WCrC5C,SAASoS,IACtB,IAAI5T,KAAEA,EAAIQ,UAAEA,EAASqG,UAAEA,GAAcze,MACjCiI,OACFA,EAAME,KACNA,EAAID,OACJA,EAAME,KACNA,EAAIoc,MACJA,EAAKC,aACLA,EAAYC,cACZA,EAAa9b,SACbA,EAAQ+b,MACRA,EAAKC,aACLA,EAAYC,cACZA,EAAalc,SACbA,EAAQmc,SACRA,EAAQpK,SACRA,EAAQD,WACRA,EAAUsK,SACVA,EAAQxK,SACRA,EAAQD,WACRA,GACElC,EAAUG,SAEVkT,EAAgBhU,EAAUgH,EAAmB,SAEjDrG,EAAUsT,aAAeD,EAAcze,IAAI8K,IACzC,IAEI6T,EAAWC,EAAWC,EACtBC,EAAMC,EAAMC,EACZC,EAAaC,EAAaC,GAJ1BjrB,KAAEA,EAAIoD,OAAEA,EAAMwC,UAAEA,GAAcgR,GAC5BlW,MAAOwqB,GAAqBtlB,EAKlC,GAAsB,iBAAXxC,EAAqB,CAC9BqnB,EAAYrnB,EAAO,GACnBsnB,EAAYtnB,EAAO,GACnBunB,EAAcF,EAAYC,EAE1B,IAAIR,EAAWlqB,EAAKsW,OAAO,IAAI7Q,KAAK,CAAC+X,EAAGjV,IAC/BiV,EAAE2N,EAAI5iB,EAAE4iB,GAEjBP,EAAOV,EAASA,EAAS3nB,OAAS,GAAG4oB,EACrCN,EAAOX,EAAS,GAAGiB,EAAIjB,EAAS,GAAGiB,EAAI,EACvCL,EAASF,EAAOC,EAGlB,GAAgC,iBAArBK,EAA+B,CACxC,IAAKE,EAAaC,GAAeH,EACjCH,EAAc/iB,EAAQqjB,GACtBL,EAAchjB,EAAQojB,GACtBH,EAAgB,CAACF,EAAY,GAAKC,EAAY,GAAID,EAAY,GAAKC,EAAY,GAAID,EAAY,GAAKC,EAAY,IAEhHpU,EAAWrV,MAAMb,MAAQ,UAuD3B,OApDAkW,EAAW5W,KAAOA,EAAKsW,OAAO,IAAIxK,IAAI4R,IACpC,IACI4N,EAAWC,GADXliB,EAAEA,EAACM,EAAEA,EAACwhB,EAAEA,GAAMzN,EA8BlB,GAzBE6N,EADE3H,GAAY,GAAKA,GAAY,EACnB5c,EAAUU,GAAYiC,EAAI6P,GAAaD,EAC1CC,GAAY,GAAKA,GAAY,EAC1BtS,EAAQQ,GAAYgB,KAAKkP,IAAIjO,GAAKjB,KAAKkP,IAAIgM,IAAcrK,EAEjE5P,EAAI,EACM2Z,EAASC,EAAe5Z,EAAKia,EAE7BN,EAASE,EAAgB9a,KAAKkP,IAAIjO,GAAMjB,KAAKkP,IAAI4B,GAK/D8R,EADEzH,GAAY,GAAKA,GAAY,EACnB9c,EAAUU,GAAY4B,EAAIgQ,GAAaD,EAC1CC,GAAY,GAAKA,GAAY,EAC1BpS,EAAQQ,GAAYiB,KAAKkP,IAAIvO,GAAKX,KAAKkP,IAAIiM,IAAczK,EAEjE/P,EAAI,EACMoa,EAASC,EAAera,EAAKwa,EAE7BJ,EAASE,EAAgBjb,KAAKkP,IAAIvO,GAAMX,KAAKkP,IAAIyB,GAGjEqE,EAAS4N,UAAYA,EACrB5N,EAAS6N,UAAYA,EAEC,iBAAXnoB,EAAqB,CAC9Bsa,EAASyN,EAAIA,GAAQ,EACrB,IAAIjD,GAASiD,EAAIN,GAAQC,EACzBpN,EAASta,OAASsnB,EAAYC,EAAczC,OAE5CxK,EAASta,OAASA,EAGpB,GAAgC,iBAArB8nB,EAA+B,CACxCxN,EAASyN,EAAIA,GAAQ,EACrB,IAAIjD,GAASiD,EAAIN,GAAQC,EACrBU,EAAW,CAACR,EAAY,GAAKC,EAAc,GAAK/C,EAAO8C,EAAY,GAAKC,EAAc,GAAK/C,EAAO8C,EAAY,GAAKC,EAAc,GAAK/C,GAE1IxK,EAAShd,MAAQoI,EAAQ0iB,QAEzB9N,EAAShd,MAAQwqB,EAGnB,OAAOxN,IAGF9G,IAGmC9X,KAAKoY,UAAUsT,aC9G9C,SAASX,IACtB,IAAInT,KAAEA,EAAIoH,WAAEA,EAAU5G,UAAEA,EAASqG,UAAEA,GAAcze,MAC7CqG,MAAEA,EAAKC,OAAEA,EAAMuR,OAAEA,EAAMvW,QAAEA,GAAYsW,EAErC+U,EAAclO,EAAkB,OAAE,IAClCvd,KAAEA,EAAMmF,MAAOumB,EAAatmB,OAAQumB,EAAYtmB,IAAEA,EAAGC,KAAEA,EAAIC,MAAEA,EAAKC,OAAEA,EAAM7C,IAAEA,EAAGC,IAAEA,EAAGV,IAAEA,EAAGuD,KAAEA,EAAIvC,MAAEA,EAAKwC,YAAEA,EAAWnE,MAAEA,EAAKqE,UAAEA,GAAc6lB,EAE1I1kB,EAAS3G,EAAQ,GACjB6G,EAAO9B,EAAQ/E,EAAQ,GACvB4G,EAAS5G,EAAQ,GACjB8G,EAAO9B,EAAShF,EAAQ,GAAK0d,EAAWc,aACxCN,EAAiBrX,EAAOF,EACxB6kB,EAAkB1kB,EAAOF,EAE7BrE,EAAMA,EAAM,IAAM,IAAMA,EACxBC,EAAMA,EAAM,EAAI,EAAIA,EAEpBmE,EAAiB,QAARzB,EAAiByB,EAASA,EAASuX,EAAiBtU,EAAa1E,GAC1E2B,EAAgB,QAAT1B,EAAkB0B,EAAOA,EAAOqX,EAAiBtU,EAAazE,GACrEyB,EAAgB,QAAP3B,EAAgB2B,EAASA,EAAS4kB,EAAkB5hB,EAAa3E,GAC1E6B,EAAiB,QAAV1B,EAAmB0B,EAAOA,EAAO0kB,EAAkB5hB,EAAaxE,GACvE8Y,EAAgC,QAAfoN,EAAwBzkB,EAAOF,EAASuX,EAAiBtU,EAAa0hB,GACvFE,EAAkC,QAAhBD,EAAyBzkB,EAAOF,EAAS4kB,EAAkB5hB,EAAa2hB,GAE1F,IAUIE,EAAQC,EAVRC,GAAoBH,GAAmB5rB,EAAKuC,OAAS,GAAKL,GAAOlC,EAAKuC,OAE1EvC,EAAKZ,QAAQse,IACXA,EAASxR,MAAQwR,EAASxR,MAAQvJ,EAAMA,EAAM+a,EAASxR,MACvDwR,EAASxR,MAAQwR,EAASxR,MAAQtJ,EAAMA,EAAM8a,EAASxR,MAEvDwR,EAASvY,MAAQmZ,GAAkBZ,EAASxR,MAAQvJ,GACpD+a,EAAStY,OAAS2mB,IAID,QAAfrmB,GACFmmB,EAAS9kB,EACT+kB,EAAS9kB,GACe,SAAftB,GACTmmB,EAAS5kB,EACT6kB,EAAS9kB,IAGP6kB,EADU,aAARpmB,EACOsB,EAAS/G,EAAKA,EAAKuC,OAAS,GAAG4C,MAAQ,EAAInF,EAAK,GAAGmF,MAAQ,EAE3D4B,EAEX+kB,EAAS9kB,GAGXhH,EAAKZ,QAAQ,CAACse,EAAUC,KACtB,IAwHIqO,EAxHA5D,EAAQ,GAEO,QAAf1iB,EACU,cAARD,EACEkY,EAAY,GAAK3d,EAAKuC,OACX,UAATW,GACFklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,UAC3B,WAATlC,IACTklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,WAG/CgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAS7rB,EAAK2d,EAAY,GAAGxY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SACzEgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,UAE9B,aAARK,IACQ,GAAbkY,EACW,UAATza,GACFklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,UAC3B,WAATlC,IACTklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,WAG/CgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAS7rB,EAAK2d,EAAY,GAAGxY,MAAOwE,EAAGmiB,IACvD1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,WAGzB,SAAfM,EACG,cAARD,EACEkY,EAAY,GAAK3d,EAAKuC,OACX,UAATW,GACFklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,UAC3B,WAATlC,IACTklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,WAG/CgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAS7rB,EAAK2d,EAAY,GAAGxY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SACzEgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,UAE9B,aAARK,IACQ,GAAbkY,EACW,UAATza,GACFklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,UAC3B,WAATlC,IACTklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,WAG/CgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAS7rB,EAAK2d,EAAY,GAAGxY,MAAOwE,EAAGmiB,IACvD1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,WAIrC,cAARK,EACEkY,EAAY,GAAK3d,EAAKuC,OACX,UAATW,GACFklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,UAC3B,WAATlC,IACTklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAQ,EAAGwE,EAAGmiB,EAASpO,EAAStY,WAGpEgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAQ,EAAInF,EAAK2d,EAAY,GAAGxY,MAAQ,EAAGwE,EAAGmiB,EAASpO,EAAStY,SAClGgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAQ,EAAInF,EAAK2d,EAAY,GAAGxY,MAAQ,EAAGwE,EAAGmiB,EAASpO,EAAStY,UAEnF,aAARK,IACQ,GAAbkY,EACW,UAATza,GACFklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,IAC3B1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,IAC5C1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,UAC3B,WAATlC,IACTklB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAQ,EAAGwE,EAAGmiB,IAChD1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,WAG/CgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAQ,EAAInF,EAAK2d,EAAY,GAAGxY,MAAQ,EAAGwE,EAAGmiB,IAChF1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAQ,EAAInF,EAAK2d,EAAY,GAAGxY,MAAQ,EAAGwE,EAAGmiB,IAChF1D,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAASnO,EAASvY,MAAOwE,EAAGmiB,EAASpO,EAAStY,SAC9DgjB,EAAMlpB,KAAK,CAAEmK,EAAGwiB,EAAQliB,EAAGmiB,EAASpO,EAAStY,WAKnDsY,EAAS0K,MAAQA,EAKb4D,EAFQ,cAARvmB,EACEkY,EAAY,GAAK3d,EAAKuC,OACD,UAATW,EAAoBwa,EAASvY,MAAQuY,EAASvY,MAAQ,GAErDuY,EAASvY,MAAQnF,EAAK2d,EAAY,GAAGxY,OAAS,EAG9C,GAAbwY,EACqB,UAATza,EAAoBwa,EAASvY,MAAQuY,EAASvY,MAAQ,GAErDuY,EAASvY,MAAQnF,EAAK2d,EAAY,GAAGxY,OAAS,EAI3C,UAAlB5D,EAAMoE,SAEN+X,EAASuO,UADQ,QAAfvmB,EACmB,CAAE2D,EAAGwiB,EAASG,EAAc,EAAGriB,EAAGmiB,EAASpO,EAAStY,OAAS,GAC1D,SAAfM,EACY,CAAE2D,EAAGwiB,EAASG,EAAc,EAAGriB,EAAGmiB,EAASpO,EAAStY,OAAS,GAE7D,CAAEiE,EAAGwiB,EAASnO,EAASvY,MAAQ,EAAGwE,EAAGmiB,EAASpO,EAAStY,OAAS,GAIrFsY,EAASuO,UADQ,QAAfvmB,EACmB,CAAE2D,EAAGwiB,EAASG,EAAczqB,EAAMC,OAAQmI,EAAGmiB,EAASpO,EAAStY,OAAS,GACrE,SAAfM,EACY,CAAE2D,EAAGwiB,EAASG,EAAczqB,EAAMC,OAAQmI,EAAGmiB,EAASpO,EAAStY,OAAS,GAExE,CAAEiE,EAAGwiB,EAASnO,EAASvY,MAAQ,EAAI6mB,EAAc,EAAIzqB,EAAMC,OAAQmI,EAAGmiB,EAASpO,EAAStY,OAAS,GAItHuY,EAAY,IAAM3d,EAAKuC,SACN,UAAfmD,GACFmmB,EAASA,EAASnO,EAASvY,MAAQ,EAAInF,EAAK2d,EAAY,GAAGxY,MAAQ,EACnE2mB,EAASA,EAASpO,EAAStY,OAASlD,GAEpC4pB,EAASA,EAASpO,EAAStY,OAASlD,KAK1CgV,EAAUuU,YAAcA,EAEmB3sB,KAAKoY,UAAUuU,YC1N7C,SAASS,IACtB,IAAIxV,KAAEA,EAAIQ,UAAEA,EAAS4G,WAAEA,GAAehf,MAChCsG,OAAQ+mB,EAAW/rB,QAAEA,EAAOuW,OAAEA,GAAWD,EAC3C0V,EAAoBzV,EAAO0V,OAAOzV,GACV,eAAnBA,EAAW5X,MAA4C,KAAnB4X,EAAW5X,MAGxD,GAAgC,GAA5BotB,EAAkB7pB,OAAa,OAEnC,IAwCI+pB,EAAiBC,EAAqBC,EAAoBC,GAxC1D7Q,KACFA,EAAI5c,KACJA,EAAIgB,KACJA,EAAI0D,YACJA,EAAWC,YACXA,EAAWC,SACXA,EAAQgC,UACRA,EACAQ,SAAUsmB,EACVrmB,QAASsmB,EACTlpB,IAAK6kB,GACH8D,EAAkB,IAClB1rB,MAAEA,EAAKsF,YAAEA,EAAWjF,QAAEA,EAAOkF,OAAEA,EAAMC,aAAEA,EAAYC,SAAEA,EAAQL,YAAEA,GAAgBF,GAC7E7F,KAAM6sB,EAAc/rB,UAAWgsB,GAAkBH,GACjD3sB,KAAM+sB,EAAajsB,UAAWksB,GAAiBJ,GAC/C5sB,KAAMitB,EAAS5nB,OAAQmkB,EAAW/nB,OAAQyrB,EAAWjtB,KAAMktB,EAASrsB,UAAWssB,GAAiB7E,GAClGvhB,OACFA,EAAME,KACNA,EAAID,OACJA,EAAME,KACNA,EAAIoc,MACJA,EAAKC,aACLA,EAAYC,cACZA,EAAa9b,SACbA,EAAQ+b,MACRA,EAAKC,aACLA,EAAYC,cACZA,EAAaxK,aACbA,EAAY1R,SACZA,EAAQmc,SACRA,EAAQpK,SACRA,EAAQD,WACRA,EAAUsK,SACVA,EAAQxK,SACRA,GAAQD,WACRA,GAAUjC,gBACVA,GAAeC,gBACfA,IACEF,EAAUG,SAGV+V,GAAW,EACXC,GAAUC,EAAAA,EAwEd,GAtEgB,QAAZ1pB,IACFA,EAAWuV,EAAezV,EAAcA,EAAcyV,EACtDvV,EAAWA,EAAW,EAAIA,EAAW,EAAIA,GAG3C0oB,EAAkBtsB,EAAKwkB,OAAO,CAACjJ,EAAkBmC,EAAUC,KACzD,MACEd,EACAC,EACAC,EACAC,EACAhE,GACE0E,EACJ0P,GAAW1kB,KAAK/F,IAAIma,EAAKsQ,IACzBC,GAAU3kB,KAAK9F,IAAIka,EAAKuQ,IAExB,IAAIE,EAAkB,GA+BtB,OA7BAA,EAAgB1Q,MAAQA,EACxB0Q,EAAgBzQ,IAAMA,EACtByQ,EAAgBxQ,IAAMA,EACtBwQ,EAAgBvQ,KAAOA,EACvBuQ,EAAgBvU,OAASA,EACzBuU,EAAgB7sB,MAAQmc,EAAQC,EAAM7W,EAASvF,EAC/C6sB,EAAgBvnB,YAAc6W,EAAQC,EAAM5W,EAAeF,EAC3DunB,EAAgBxsB,QAAU8b,EAAQC,EAAM/b,EAAUoF,EAClDonB,EAAgBznB,YAAcA,EAE9BynB,EAAgBC,YAAc,CAC5B1G,OAAQ3P,GAAgBwG,GAAWtU,EACnC0d,OAAQ/f,EAAUU,GAAYqV,EAAMvD,GAAaD,EACjDyN,KAAM7P,GAAgBwG,GAAWtU,EACjC4d,KAAMjgB,EAAUU,GAAYgB,KAAK/F,IAAIka,EAAOC,GAAOtD,GAAaD,GAElEgU,EAAgBE,cAAgB,CAC9B3G,OAAQ3P,GAAgBwG,GAAWtU,EACnC0d,OAAQ/f,EAAUU,GAAYsV,EAAOxD,GAAaD,EAClDyN,KAAM7P,GAAgBwG,GAAWtU,EACjC4d,KAAMjgB,EAAUU,GAAYgB,KAAK9F,IAAIia,EAAOC,GAAOtD,GAAaD,GAElEgU,EAAgBG,UAAY,CAC1BrkB,EAAGX,KAAK2c,MAAMlO,GAAgBwG,GAAWtU,EAAIzF,EAAW,GACxD+F,EAAG3C,EAAUU,GAAYgB,KAAK/F,IAAIka,EAAOC,GAAOtD,GAAaD,EAC7DpU,MAAOvB,EACPwB,OAASsC,EAAWgB,KAAKkP,IAAIiF,EAAQC,GAAQvD,GAE/CgC,EAAiBrc,KAAKquB,GACfhS,GACN,IAECqR,IACFL,EAAsB,CACpBvsB,KAAMotB,GACNtG,OAAQ/f,EACRggB,OAAQ/f,EAAUU,GAAY0lB,GAAW5T,GAAaD,EACtDyN,KAAM/f,EACNggB,KAAMjgB,EAAUU,GAAY0lB,GAAW5T,GAAaD,IAIpDuT,IACFN,EAAqB,CACnBxsB,KAAMqtB,GACNvG,OAAQ/f,EACRggB,OAAQ/f,EAAUU,GAAY2lB,GAAU7T,GAAaD,EACrDyN,KAAM/f,EACNggB,KAAMjgB,EAAUU,GAAY2lB,GAAU7T,GAAaD,IAInDyT,EAAS,CACX,IAAInI,EAAUnc,KAAK/F,OAAOuqB,GACtBpI,EAAUpc,KAAK9F,OAAOsqB,GACtBS,EAAQ9I,EAAUC,EAEtB2H,EAAiB,CACfzsB,KAAM,GACN4tB,WAAY7mB,EACZyU,WAAY,KACZqS,SAAU5mB,EACV6mB,SAAU,MAEZrB,EAAezsB,KAAOssB,EAAgB9H,OAAO,CAACpM,EAAKvQ,EAAM4E,KACvD,IAAI/L,MAAEA,EAAKgtB,UAAEA,GAAc7lB,GACvBwB,EAAEA,EAAClE,MAAEA,GAAUuoB,EACf/jB,EAAIwiB,EAAc/rB,EAAQ,GAAK0d,EAAWc,aAAeuO,EAAarsB,UAAY,EAClFsE,EAAqB,GAAZmkB,EAA+B,GAAZA,GAAmB2D,EAAQzgB,GAASqY,GAAY6I,EAahF,OAXAvV,EAAIlZ,KAAK,CACPwB,MAAAA,EACA2I,EAAAA,EACAM,EAAAA,EACAxE,MAAAA,EACAC,OAAAA,IAGFqnB,EAAejR,WAAa7R,EAC5B8iB,EAAeqB,SAAWnkB,EAEnByO,GACN,IAGLtZ,KAAKoY,UAAUqE,iBAAmB,CAChCvc,KAAAA,EACA4c,KAAAA,EACAgB,KAAM0P,EACNlmB,SAAUmmB,EACVwB,SAAUvB,EACV/oB,IAAKgpB,GAGqC3tB,KAAKoY,UAAUqE,iBClK9C,SAASyS,IACtB,IAAI9W,UAAEA,EAASqG,UAAEA,GAAcze,MAC3BiI,OAAEA,EAAMC,OAAEA,EAAMmS,aAAEA,EAAYG,aAAEA,GAAiBpC,EAAUG,SAE3D4W,EAAgB1X,EAAUgH,EAAmB,SAEjDrG,EAAUgX,aAAeD,EAAcniB,IAAI8K,IACzC,IAEIgU,EAAMC,EAAMC,EACZC,EAAaC,EAAaC,GAH1BjrB,KAAEA,EAAI4F,UAAEA,GAAcgR,GACtBlW,MAAEA,EAAK6F,SAAEA,GAAaX,EAItBskB,EAAWlqB,EAAKsW,OAAO,IAAI7Q,KAAK,CAAC+X,EAAGjV,IAC/BiV,EAAE,GAAKjV,EAAE,IAElBqiB,EAAOV,EAASA,EAAS3nB,OAAS,GAAG,GACrCsoB,EAAOX,EAAS,GAAG,GACnBY,EAASF,EAAOC,EAEhB,IAAKO,EAAaC,GAAe3qB,EAsBjC,OArBAqqB,EAAc/iB,EAAQqjB,GACtBL,EAAchjB,EAAQojB,GACtBH,EAAgB,CAACF,EAAY,GAAKC,EAAY,GAAID,EAAY,GAAKC,EAAY,GAAID,EAAY,GAAKC,EAAY,IAEhHpU,EAAW5W,KAAOA,EAAK8L,IAAI4R,IACzB,IACI4N,EAAWC,GADVliB,EAAGM,EAAGwhB,GAAKzN,EAGhB4N,EAAYvkB,EAASsC,EAAI8P,EACzBoS,EAAYvkB,GAAU2C,EAAI,GAAK2P,EAC/BoE,EAAS4N,UAAYA,EACrB5N,EAAS6N,UAAYA,EAErBJ,EAAIA,GAAQ,EACZ,IAAIjD,GAASiD,EAAIN,GAAQC,EACrBU,EAAW,CAACR,EAAY,GAAKC,EAAc,GAAK/C,EAAO8C,EAAY,GAAKC,EAAc,GAAK/C,EAAO8C,EAAY,GAAKC,EAAc,GAAK/C,GAG1I,OAFAxK,EAAShd,MAAQoI,EAAQ0iB,GACzB9N,EAASnX,SAAWA,EACbmX,IAGF9G,IAEmC9X,KAAKoY,UAAUgX,aC9C7D,SAASC,EAAkB3Q,EAAGjV,GAC5B,OAAOiV,EAAE4Q,SAAW7lB,EAAE6lB,OAAS,EAAI,EAOrC,SAASC,EAAYhlB,EAAGilB,GACtB,OAAOjlB,EAAIilB,EAAEjlB,EAOf,SAASklB,EAAW5kB,EAAG2kB,GACrB,OAAO5lB,KAAK/F,IAAIgH,EAAG2kB,EAAE3kB,GCjBvB,SAAS6kB,EAAMC,GACb,IAAIxE,EAAM,EACNyE,EAAWD,EAAKC,SAChBnJ,EAAImJ,GAAYA,EAASnsB,OAC7B,GAAKgjB,EACA,OAASA,GAAK,GAAG0E,GAAOyE,EAASnJ,GAAGrZ,WADjC+d,EAAM,EAEdwE,EAAKviB,MAAQ+d,ECMA,SAAS0E,EAAU3uB,EAAM0uB,GACtC,IAEID,EAEAG,EACAC,EACAtJ,EACAzU,EAPAzF,EAAO,IAAIyjB,EAAK9uB,GAChB+uB,GAAU/uB,EAAKkM,QAAUb,EAAKa,MAAQlM,EAAKkM,OAE3C8iB,EAAQ,CAAC3jB,GAQb,IAFgB,MAAZqjB,IAAkBA,EAAWO,GAE1BR,EAAOO,EAAM9Z,OAElB,GADI6Z,IAAQN,EAAKviB,OAASuiB,EAAKzuB,KAAKkM,QAC/B2iB,EAASH,EAASD,EAAKzuB,SAAW8Q,EAAI+d,EAAOtsB,QAEhD,IADAksB,EAAKC,SAAW,IAAI9hB,MAAMkE,GACrByU,EAAIzU,EAAI,EAAGyU,GAAK,IAAKA,EACxByJ,EAAM9vB,KAAK0vB,EAAQH,EAAKC,SAASnJ,GAAK,IAAIuJ,EAAKD,EAAOtJ,KACtDqJ,EAAMR,OAASK,EACfG,EAAMM,MAAQT,EAAKS,MAAQ,EAKjC,OAAO7jB,EAAK8jB,WAAWC,GAOzB,SAASH,EAAgBrmB,GACvB,OAAOA,EAAE8lB,SAGX,SAASW,EAASZ,GAChBA,EAAKzuB,KAAOyuB,EAAKzuB,KAAKA,KAGjB,SAASovB,EAAcX,GAC5B,IAAIrpB,EAAS,EACb,GAAGqpB,EAAKrpB,OAASA,SACTqpB,EAAOA,EAAKL,SAAYK,EAAKrpB,SAAWA,GAG3C,SAAS0pB,EAAK9uB,GACnBlB,KAAKkB,KAAOA,EACZlB,KAAKowB,MACLpwB,KAAKsG,OAAS,EACdtG,KAAKsvB,OAAS,KAGhBU,EAAKxkB,UAAYqkB,EAAUrkB,UAAY,CACrCuH,YAAaid,EACbN,MDzDa,WACb,OAAO1vB,KAAKwwB,UAAUd,ICyDtBe,KCnEa,SAASC,GACtB,IAAiBC,EAAwBf,EAAUnJ,EAAGzU,EAAlD2d,EAAO3vB,KAAe4wB,EAAO,CAACjB,GAClC,GAEE,IADAgB,EAAUC,EAAKC,UAAWD,EAAO,GAC1BjB,EAAOgB,EAAQva,OAEpB,GADAsa,EAASf,GAAOC,EAAWD,EAAKC,SAClB,IAAKnJ,EAAI,EAAGzU,EAAI4d,EAASnsB,OAAQgjB,EAAIzU,IAAKyU,EACtDmK,EAAKxwB,KAAKwvB,EAASnJ,UAGhBmK,EAAKntB,QACd,OAAOzD,MDyDPwwB,UEpEa,SAASE,GAEtB,IADA,IAA4Cd,EAAUnJ,EAAGzU,EAArD2d,EAAO3vB,KAAMkwB,EAAQ,CAACP,GAAOiB,EAAO,GACjCjB,EAAOO,EAAM9Z,OAElB,GADAwa,EAAKxwB,KAAKuvB,GAAOC,EAAWD,EAAKC,SACnB,IAAKnJ,EAAI,EAAGzU,EAAI4d,EAASnsB,OAAQgjB,EAAIzU,IAAKyU,EACtDyJ,EAAM9vB,KAAKwvB,EAASnJ,IAGxB,KAAOkJ,EAAOiB,EAAKxa,OACjBsa,EAASf,GAEX,OAAO3vB,MF0DPqwB,WGrEa,SAASK,GAEtB,IADA,IAAiCd,EAAUnJ,EAAvCkJ,EAAO3vB,KAAMkwB,EAAQ,CAACP,GACnBA,EAAOO,EAAM9Z,OAElB,GADAsa,EAASf,GAAOC,EAAWD,EAAKC,SAClB,IAAKnJ,EAAImJ,EAASnsB,OAAS,EAAGgjB,GAAK,IAAKA,EACpDyJ,EAAM9vB,KAAKwvB,EAASnJ,IAGxB,OAAOzmB,MH8DPmrB,IItEa,SAAS/d,GACtB,OAAOpN,KAAKwwB,WAAU,SAASb,GAI7B,IAHA,IAAIxE,GAAO/d,EAAMuiB,EAAKzuB,OAAS,EAC3B0uB,EAAWD,EAAKC,SAChBnJ,EAAImJ,GAAYA,EAASnsB,SACpBgjB,GAAK,GAAG0E,GAAOyE,EAASnJ,GAAGrZ,MACpCuiB,EAAKviB,MAAQ+d,MJiEfxkB,KKvEa,SAASmqB,GACtB,OAAO9wB,KAAKqwB,YAAW,SAASV,GAC1BA,EAAKC,UACPD,EAAKC,SAASjpB,KAAKmqB,OLqEvBC,KMxEa,SAAS/S,GAItB,IAHA,IAAID,EAAQ/d,KACRgxB,EAcN,SAA6BtS,EAAGjV,GAC9B,GAAIiV,IAAMjV,EAAG,OAAOiV,EACpB,IAAIuS,EAASvS,EAAEwS,YACXC,EAAS1nB,EAAEynB,YACX1B,EAAI,KACR9Q,EAAIuS,EAAO7a,MACX3M,EAAI0nB,EAAO/a,MACX,KAAOsI,IAAMjV,GACX+lB,EAAI9Q,EACJA,EAAIuS,EAAO7a,MACX3M,EAAI0nB,EAAO/a,MAEb,OAAOoZ,EA1BQ4B,CAAoBrT,EAAOC,GACtCkS,EAAQ,CAACnS,GACNA,IAAUiT,GACfjT,EAAQA,EAAMuR,OACdY,EAAM9vB,KAAK2d,GAGb,IADA,IAAIsT,EAAInB,EAAMzsB,OACPua,IAAQgT,GACbd,EAAMvgB,OAAO0hB,EAAG,EAAGrT,GACnBA,EAAMA,EAAIsR,OAEZ,OAAOY,GN4DPgB,UOzEa,WAEb,IADA,IAAIvB,EAAO3vB,KAAMkwB,EAAQ,CAACP,GACnBA,EAAOA,EAAKL,QACjBY,EAAM9vB,KAAKuvB,GAEb,OAAOO,GPqEPoB,YQ1Ea,WACb,IAAIpB,EAAQ,GAIZ,OAHAlwB,KAAKywB,MAAK,SAASd,GACjBO,EAAM9vB,KAAKuvB,MAENO,GRsEPqB,OS3Ea,WACb,IAAIA,EAAS,GAMb,OALAvxB,KAAKqwB,YAAW,SAASV,GAClBA,EAAKC,UACR2B,EAAOnxB,KAAKuvB,MAGT4B,GTqEPC,MU5Ea,WACb,IAAIjlB,EAAOvM,KAAMwxB,EAAQ,GAMzB,OALAjlB,EAAKkkB,MAAK,SAASd,GACbA,IAASpjB,GACXilB,EAAMpxB,KAAK,CAAC8S,OAAQyc,EAAKL,OAAQtkB,OAAQ2kB,OAGtC6B,GVsEP/d,KAtCF,WACE,OAAOoc,EAAU7vB,MAAMqwB,WAAWE,KWxC7B,IAAI/c,EAAQ1F,MAAMtC,UAAUgI,MCEpB,WAASie,GAGtB,IAFA,IAAwEtnB,EAAG3J,EAAvEimB,EAAI,EAAGzU,GAAKyf,EDDX,SAAiBlkB,GAKtB,IAJA,IACIlD,EACAoc,EAFAiL,EAAInkB,EAAM9J,OAIPiuB,GACLjL,EAAI7c,KAAK+nB,SAAWD,IAAM,EAC1BrnB,EAAIkD,EAAMmkB,GACVnkB,EAAMmkB,GAAKnkB,EAAMkZ,GACjBlZ,EAAMkZ,GAAKpc,EAGb,OAAOkD,ECXmBqkB,CAAQpe,EAAM/H,KAAKgmB,KAAWhuB,OAAQouB,EAAI,GAE7DpL,EAAIzU,GACT7H,EAAIsnB,EAAQhL,GACRjmB,GAAKsxB,EAAatxB,EAAG2J,KAAMsc,GAC1BjmB,EAAIuxB,EAAaF,EAAIG,EAAYH,EAAG1nB,IAAKsc,EAAI,GAGpD,OAAOjmB,EAGT,SAASwxB,EAAYH,EAAG1nB,GACtB,IAAIsc,EAAGwL,EAEP,GAAIC,EAAgB/nB,EAAG0nB,GAAI,MAAO,CAAC1nB,GAGnC,IAAKsc,EAAI,EAAGA,EAAIoL,EAAEpuB,SAAUgjB,EAC1B,GAAI0L,EAAYhoB,EAAG0nB,EAAEpL,KACdyL,EAAgBE,EAAcP,EAAEpL,GAAItc,GAAI0nB,GAC7C,MAAO,CAACA,EAAEpL,GAAItc,GAKlB,IAAKsc,EAAI,EAAGA,EAAIoL,EAAEpuB,OAAS,IAAKgjB,EAC9B,IAAKwL,EAAIxL,EAAI,EAAGwL,EAAIJ,EAAEpuB,SAAUwuB,EAC9B,GAAIE,EAAYC,EAAcP,EAAEpL,GAAIoL,EAAEI,IAAK9nB,IACpCgoB,EAAYC,EAAcP,EAAEpL,GAAItc,GAAI0nB,EAAEI,KACtCE,EAAYC,EAAcP,EAAEI,GAAI9nB,GAAI0nB,EAAEpL,KACtCyL,EAAgBG,GAAcR,EAAEpL,GAAIoL,EAAEI,GAAI9nB,GAAI0nB,GACnD,MAAO,CAACA,EAAEpL,GAAIoL,EAAEI,GAAI9nB,GAM1B,MAAM,IAAImoB,MAGZ,SAASH,EAAYzT,EAAGjV,GACtB,IAAI8oB,EAAK7T,EAAEpV,EAAIG,EAAEH,EAAGkpB,EAAK/oB,EAAEc,EAAImU,EAAEnU,EAAGkoB,EAAKhpB,EAAEoB,EAAI6T,EAAE7T,EACjD,OAAO0nB,EAAK,GAAKA,EAAKA,EAAKC,EAAKA,EAAKC,EAAKA,EAG5C,SAASX,EAAapT,EAAGjV,GACvB,IAAI8oB,EAAK7T,EAAEpV,EAAIG,EAAEH,EAAI,KAAMkpB,EAAK/oB,EAAEc,EAAImU,EAAEnU,EAAGkoB,EAAKhpB,EAAEoB,EAAI6T,EAAE7T,EACxD,OAAO0nB,EAAK,GAAKA,EAAKA,EAAKC,EAAKA,EAAKC,EAAKA,EAG5C,SAASP,EAAgBxT,EAAGmT,GAC1B,IAAK,IAAIpL,EAAI,EAAGA,EAAIoL,EAAEpuB,SAAUgjB,EAC9B,IAAKqL,EAAapT,EAAGmT,EAAEpL,IACrB,OAAO,EAGX,OAAO,EAGT,SAASsL,EAAaF,GACpB,OAAQA,EAAEpuB,QACR,KAAK,EAAG,MAOH,CACL8G,GAFmBmU,EANUmT,EAAE,IAQ1BtnB,EACLM,EAAG6T,EAAE7T,EACLvB,EAAGoV,EAAEpV,GATL,KAAK,EAAG,OAAO8oB,EAAcP,EAAE,GAAIA,EAAE,IACrC,KAAK,EAAG,OAAOQ,GAAcR,EAAE,GAAIA,EAAE,GAAIA,EAAE,IAI/C,IAAuBnT,EAQvB,SAAS0T,EAAc1T,EAAGjV,GACxB,IAAIhB,EAAKiW,EAAEnU,EAAG7B,EAAKgW,EAAE7T,EAAG6nB,EAAKhU,EAAEpV,EAC3BqpB,EAAKlpB,EAAEc,EAAGqoB,EAAKnpB,EAAEoB,EAAGgoB,EAAKppB,EAAEH,EAC3BwpB,EAAMH,EAAKlqB,EAAIsqB,EAAMH,EAAKlqB,EAAIsqB,EAAMH,EAAKH,EACzC7oB,EAAID,KAAKqpB,KAAKH,EAAMA,EAAMC,EAAMA,GACpC,MAAO,CACLxoB,GAAI9B,EAAKkqB,EAAKG,EAAMjpB,EAAImpB,GAAO,EAC/BnoB,GAAInC,EAAKkqB,EAAKG,EAAMlpB,EAAImpB,GAAO,EAC/B1pB,GAAIO,EAAI6oB,EAAKG,GAAM,GAIvB,SAASR,GAAc3T,EAAGjV,EAAG+lB,GAC3B,IAAI/mB,EAAKiW,EAAEnU,EAAG7B,EAAKgW,EAAE7T,EAAG6nB,EAAKhU,EAAEpV,EAC3BqpB,EAAKlpB,EAAEc,EAAGqoB,EAAKnpB,EAAEoB,EAAGgoB,EAAKppB,EAAEH,EAC3B4pB,EAAK1D,EAAEjlB,EAAG4oB,EAAK3D,EAAE3kB,EAAGuoB,EAAK5D,EAAElmB,EAC3B+pB,EAAK5qB,EAAKkqB,EACVW,EAAK7qB,EAAKyqB,EACVK,EAAK7qB,EAAKkqB,EACVY,EAAK9qB,EAAKyqB,EACVM,EAAKZ,EAAKH,EACVgB,EAAKN,EAAKV,EACViB,EAAKlrB,EAAKA,EAAKC,EAAKA,EAAKgqB,EAAKA,EAC9BkB,EAAKD,EAAKhB,EAAKA,EAAKC,EAAKA,EAAKC,EAAKA,EACnCgB,EAAKF,EAAKT,EAAKA,EAAKC,EAAKA,EAAKC,EAAKA,EACnCU,EAAKR,EAAKC,EAAKF,EAAKG,EACpBO,GAAMR,EAAKM,EAAKL,EAAKI,IAAY,EAALE,GAAUrrB,EACtCurB,GAAMR,EAAKC,EAAKF,EAAKG,GAAMI,EAC3BG,GAAMX,EAAKM,EAAKP,EAAKQ,IAAY,EAALC,GAAUprB,EACtCwrB,GAAMb,EAAKK,EAAKJ,EAAKG,GAAMK,EAC3BK,EAAIH,EAAKA,EAAKE,EAAKA,EAAK,EACxBrC,EAAI,GAAKa,EAAKqB,EAAKC,EAAKC,EAAKC,GAC7BE,EAAIL,EAAKA,EAAKE,EAAKA,EAAKvB,EAAKA,EAC7BppB,IAAM6qB,GAAKtC,EAAIjoB,KAAKqpB,KAAKpB,EAAIA,EAAI,EAAIsC,EAAIC,KAAO,EAAID,GAAKC,EAAIvC,GACjE,MAAO,CACLtnB,EAAG9B,EAAKsrB,EAAKC,EAAK1qB,EAClBuB,EAAGnC,EAAKurB,EAAKC,EAAK5qB,EAClBA,EAAGA,GCjHP,SAAS+qB,GAAM5qB,EAAGiV,EAAG8Q,GACnB,IAAoBjlB,EAAG8oB,EACHxoB,EAAG0oB,EADnBf,EAAK/oB,EAAEc,EAAImU,EAAEnU,EACbkoB,EAAKhpB,EAAEoB,EAAI6T,EAAE7T,EACb+oB,EAAKpB,EAAKA,EAAKC,EAAKA,EACpBmB,GACFP,EAAK3U,EAAEpV,EAAIkmB,EAAElmB,EAAG+pB,GAAMA,EACtBE,EAAK9pB,EAAEH,EAAIkmB,EAAElmB,EACT+pB,GADYE,GAAMA,IAEpBhpB,GAAKqpB,EAAKL,EAAKF,IAAO,EAAIO,GAC1B/oB,EAAIjB,KAAKqpB,KAAKrpB,KAAK/F,IAAI,EAAG0vB,EAAKK,EAAKrpB,EAAIA,IACxCilB,EAAEjlB,EAAId,EAAEc,EAAIA,EAAIioB,EAAK3nB,EAAI4nB,EACzBjD,EAAE3kB,EAAIpB,EAAEoB,EAAIN,EAAIkoB,EAAK5nB,EAAI2nB,IAEzBjoB,GAAKqpB,EAAKP,EAAKE,IAAO,EAAIK,GAC1B/oB,EAAIjB,KAAKqpB,KAAKrpB,KAAK/F,IAAI,EAAGwvB,EAAKO,EAAKrpB,EAAIA,IACxCilB,EAAEjlB,EAAImU,EAAEnU,EAAIA,EAAIioB,EAAK3nB,EAAI4nB,EACzBjD,EAAE3kB,EAAI6T,EAAE7T,EAAIN,EAAIkoB,EAAK5nB,EAAI2nB,KAG3BhD,EAAEjlB,EAAImU,EAAEnU,EAAIilB,EAAElmB,EACdkmB,EAAE3kB,EAAI6T,EAAE7T,GAIZ,SAASypB,GAAW5V,EAAGjV,GACrB,IAAI8oB,EAAK7T,EAAEpV,EAAIG,EAAEH,EAAI,KAAMkpB,EAAK/oB,EAAEc,EAAImU,EAAEnU,EAAGkoB,EAAKhpB,EAAEoB,EAAI6T,EAAE7T,EACxD,OAAO0nB,EAAK,GAAKA,EAAKA,EAAKC,EAAKA,EAAKC,EAAKA,EAG5C,SAAS8B,GAAM5E,GACb,IAAIjR,EAAIiR,EAAK6E,EACT/qB,EAAIkmB,EAAKiB,KAAK4D,EACdV,EAAKpV,EAAEpV,EAAIG,EAAEH,EACbkpB,GAAM9T,EAAEnU,EAAId,EAAEH,EAAIG,EAAEc,EAAImU,EAAEpV,GAAKwqB,EAC/BrB,GAAM/T,EAAE7T,EAAIpB,EAAEH,EAAIG,EAAEoB,EAAI6T,EAAEpV,GAAKwqB,EACnC,OAAOtB,EAAKA,EAAKC,EAAKA,EAGxB,SAASzC,GAAKyE,GACZz0B,KAAKw0B,EAAIC,EACTz0B,KAAK4wB,KAAO,KACZ5wB,KAAK00B,SAAW,KAGX,SAASC,GAAYlD,GAC1B,KAAMzf,EAAIyf,EAAQhuB,QAAS,OAAO,EAElC,IAAIib,EAAGjV,EAAG+lB,EAAGxd,EAAG4iB,EAAIC,EAAIpO,EAAGwL,EAAGZ,EAAGyD,EAAIC,EAIrC,IADArW,EAAI+S,EAAQ,IAAMlnB,EAAI,EAAGmU,EAAE7T,EAAI,IACzBmH,EAAI,GAAI,OAAO0M,EAAEpV,EAIvB,GADAG,EAAIgoB,EAAQ,GAAI/S,EAAEnU,GAAKd,EAAEH,EAAGG,EAAEc,EAAImU,EAAEpV,EAAGG,EAAEoB,EAAI,IACvCmH,EAAI,GAAI,OAAO0M,EAAEpV,EAAIG,EAAEH,EAG7B+qB,GAAM5qB,EAAGiV,EAAG8Q,EAAIiC,EAAQ,IAGxB/S,EAAI,IAAIsR,GAAKtR,GAAIjV,EAAI,IAAIumB,GAAKvmB,GAAI+lB,EAAI,IAAIQ,GAAKR,GAC/C9Q,EAAEkS,KAAOpB,EAAEkF,SAAWjrB,EACtBA,EAAEmnB,KAAOlS,EAAEgW,SAAWlF,EACtBA,EAAEoB,KAAOnnB,EAAEirB,SAAWhW,EAGtBsW,EAAM,IAAKvO,EAAI,EAAGA,EAAIzU,IAAKyU,EAAG,CAC5B4N,GAAM3V,EAAE8V,EAAG/qB,EAAE+qB,EAAGhF,EAAIiC,EAAQhL,IAAK+I,EAAI,IAAIQ,GAAKR,GAK9CyC,EAAIxoB,EAAEmnB,KAAMS,EAAI3S,EAAEgW,SAAUI,EAAKrrB,EAAE+qB,EAAElrB,EAAGyrB,EAAKrW,EAAE8V,EAAElrB,EACjD,GACE,GAAIwrB,GAAMC,EAAI,CACZ,GAAIT,GAAWrC,EAAEuC,EAAGhF,EAAEgF,GAAI,CACxB/qB,EAAIwoB,EAAGvT,EAAEkS,KAAOnnB,EAAGA,EAAEirB,SAAWhW,IAAK+H,EACrC,SAASuO,EAEXF,GAAM7C,EAAEuC,EAAElrB,EAAG2oB,EAAIA,EAAErB,SACd,CACL,GAAI0D,GAAWjD,EAAEmD,EAAGhF,EAAEgF,GAAI,EACxB9V,EAAI2S,GAAKT,KAAOnnB,EAAGA,EAAEirB,SAAWhW,IAAK+H,EACrC,SAASuO,EAEXD,GAAM1D,EAAEmD,EAAElrB,EAAG+nB,EAAIA,EAAEqD,gBAEdzC,IAAMZ,EAAET,MAOjB,IAJApB,EAAEkF,SAAWhW,EAAG8Q,EAAEoB,KAAOnnB,EAAGiV,EAAEkS,KAAOnnB,EAAEirB,SAAWjrB,EAAI+lB,EAGtDoF,EAAKL,GAAM7V,IACH8Q,EAAIA,EAAEoB,QAAUnnB,IACjBorB,EAAKN,GAAM/E,IAAMoF,IACpBlW,EAAI8Q,EAAGoF,EAAKC,GAGhBprB,EAAIiV,EAAEkS,KAIU,IAAlBlS,EAAI,CAACjV,EAAE+qB,GAAIhF,EAAI/lB,GAAW+lB,EAAIA,EAAEoB,QAAUnnB,GAAGiV,EAAEte,KAAKovB,EAAEgF,GAGtD,IAH0DhF,EAAIyF,EAAQvW,GAGjE+H,EAAI,EAAGA,EAAIzU,IAAKyU,GAAG/H,EAAI+S,EAAQhL,IAAMlc,GAAKilB,EAAEjlB,EAAGmU,EAAE7T,GAAK2kB,EAAE3kB,EAE7D,OAAO2kB,EAAElmB,EC/GJ,SAAS4rB,GAASC,GACvB,OAAY,MAALA,EAAY,KAAOC,GAASD,GAG9B,SAASC,GAASD,GACvB,GAAiB,mBAANA,EAAkB,MAAM,IAAI7C,MACvC,OAAO6C,ECNF,SAASE,KACd,OAAO,EAGT,YAAwB9qB,GACtB,OAAO,WACL,OAAOA,GCFX,SAAS+qB,GAAcxrB,GACrB,OAAOF,KAAKqpB,KAAKnpB,EAAEsD,OAuCrB,SAASmoB,GAAWjxB,GAClB,OAAO,SAASqrB,GACTA,EAAKC,WACRD,EAAKrmB,EAAIM,KAAK/F,IAAI,GAAIS,EAAOqrB,IAAS,KAK5C,SAAS6F,GAAal0B,EAAS+vB,GAC7B,OAAO,SAAS1B,GACd,GAAIC,EAAWD,EAAKC,SAAU,CAC5B,IAAIA,EACAnJ,EAGAjmB,EAFAwR,EAAI4d,EAASnsB,OACb6F,EAAIhI,EAAQquB,GAAQ0B,GAAK,EAG7B,GAAI/nB,EAAG,IAAKmd,EAAI,EAAGA,EAAIzU,IAAKyU,EAAGmJ,EAASnJ,GAAGnd,GAAKA,EAEhD,GADA9I,EAAIm0B,GAAY/E,GACZtmB,EAAG,IAAKmd,EAAI,EAAGA,EAAIzU,IAAKyU,EAAGmJ,EAASnJ,GAAGnd,GAAKA,EAChDqmB,EAAKrmB,EAAI9I,EAAI8I,IAKnB,SAASmsB,GAAepE,GACtB,OAAO,SAAS1B,GACd,IAAIL,EAASK,EAAKL,OAClBK,EAAKrmB,GAAK+nB,EACN/B,IACFK,EAAKplB,EAAI+kB,EAAO/kB,EAAI8mB,EAAI1B,EAAKplB,EAC7BolB,EAAK9kB,EAAIykB,EAAOzkB,EAAIwmB,EAAI1B,EAAK9kB,IC3EpB,YAAS8kB,GACtBA,EAAKpnB,GAAKqB,KAAKG,MAAM4lB,EAAKpnB,IAC1BonB,EAAKnnB,GAAKoB,KAAKG,MAAM4lB,EAAKnnB,IAC1BmnB,EAAKlnB,GAAKmB,KAAKG,MAAM4lB,EAAKlnB,IAC1BknB,EAAKjnB,GAAKkB,KAAKG,MAAM4lB,EAAKjnB,ICJb,YAAS4mB,EAAQ/mB,EAAIC,EAAIC,EAAIC,GAO1C,IANA,IACIinB,EADAO,EAAQZ,EAAOM,SAEfnJ,GAAK,EACLzU,EAAIke,EAAMzsB,OACV4tB,EAAI/B,EAAOliB,QAAU3E,EAAKF,GAAM+mB,EAAOliB,QAElCqZ,EAAIzU,IACX2d,EAAOO,EAAMzJ,IAASje,GAAKA,EAAImnB,EAAKjnB,GAAKA,EACzCinB,EAAKpnB,GAAKA,EAAIonB,EAAKlnB,GAAKF,GAAMonB,EAAKviB,MAAQikB,ECN/C,IACIqE,GAAU,CAACtF,OAAQ,GACnBuF,GAAY,GAEhB,SAASC,GAAU9rB,GACjB,OAAOA,EAAE+rB,GAGX,SAASC,GAAgBhsB,GACvB,OAAOA,EAAEisB,SCVX,SAAS1G,GAAkB3Q,EAAGjV,GAC5B,OAAOiV,EAAE4Q,SAAW7lB,EAAE6lB,OAAS,EAAI,EAWrC,SAAS0G,GAASC,GAChB,IAAIrG,EAAWqG,EAAErG,SACjB,OAAOA,EAAWA,EAAS,GAAKqG,EAAE5rB,EAIpC,SAAS6rB,GAAUD,GACjB,IAAIrG,EAAWqG,EAAErG,SACjB,OAAOA,EAAWA,EAASA,EAASnsB,OAAS,GAAKwyB,EAAE5rB,EAKtD,SAAS8rB,GAAYC,EAAIC,EAAIC,GAC3B,IAAIC,EAASD,GAASD,EAAG5P,EAAI2P,EAAG3P,GAChC4P,EAAG7G,GAAK+G,EACRF,EAAG1sB,GAAK2sB,EACRF,EAAG5G,GAAK+G,EACRF,EAAGhK,GAAKiK,EACRD,EAAG3E,GAAK4E,EAsBV,SAASE,GAAaC,EAAKR,EAAGjF,GAC5B,OAAOyF,EAAI/X,EAAE4Q,SAAW2G,EAAE3G,OAASmH,EAAI/X,EAAIsS,EAG7C,SAAS0F,GAAS/G,EAAMlJ,GACtBzmB,KAAKw0B,EAAI7E,EACT3vB,KAAKsvB,OAAS,KACdtvB,KAAK4vB,SAAW,KAChB5vB,KAAKm0B,EAAI,KACTn0B,KAAK0e,EAAI1e,KACTA,KAAKqsB,EAAI,EACTrsB,KAAK0xB,EAAI,EACT1xB,KAAKwvB,EAAI,EACTxvB,KAAK2J,EAAI,EACT3J,KAAKqK,EAAI,KACTrK,KAAKymB,EAAIA,ECtEI,YAAS6I,EAAQ/mB,EAAIC,EAAIC,EAAIC,GAO1C,IANA,IACIinB,EADAO,EAAQZ,EAAOM,SAEfnJ,GAAK,EACLzU,EAAIke,EAAMzsB,OACV4tB,EAAI/B,EAAOliB,QAAU1E,EAAKF,GAAM8mB,EAAOliB,QAElCqZ,EAAIzU,IACX2d,EAAOO,EAAMzJ,IAASle,GAAKA,EAAIonB,EAAKlnB,GAAKA,EACzCknB,EAAKnnB,GAAKA,EAAImnB,EAAKjnB,GAAKF,GAAMmnB,EAAKviB,MAAQikB,EDgE/CqF,GAASlrB,UAAYD,OAAOkE,OAAOugB,EAAKxkB,WEtEjC,IAAImrB,IAAO,EAAI/sB,KAAKqpB,KAAK,IAAM,EAE/B,SAAS2D,GAAcC,EAAOvH,EAAQ/mB,EAAIC,EAAIC,EAAIC,GAkBvD,IAjBA,IAEIouB,EACAC,EAIAvE,EAAIC,EAEJuE,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAfAC,EAAO,GACPrH,EAAQZ,EAAOM,SAGf4H,EAAK,EACLC,EAAK,EACLzlB,EAAIke,EAAMzsB,OAEV2J,EAAQkiB,EAAOliB,MASZoqB,EAAKxlB,GAAG,CACbwgB,EAAK/pB,EAAKF,EAAIkqB,EAAK/pB,EAAKF,EAGxB,GAAGwuB,EAAW9G,EAAMuH,KAAMrqB,aAAe4pB,GAAYS,EAAKzlB,GAO1D,IANAilB,EAAWC,EAAWF,EAEtBM,EAAON,EAAWA,GADlBK,EAAQztB,KAAK/F,IAAI4uB,EAAKD,EAAIA,EAAKC,IAAOrlB,EAAQypB,IAE9CO,EAAWxtB,KAAK/F,IAAIqzB,EAAWI,EAAMA,EAAOL,GAGrCQ,EAAKzlB,IAAKylB,EAAI,CAMnB,GALAT,GAAYD,EAAY7G,EAAMuH,GAAIrqB,MAC9B2pB,EAAYE,IAAUA,EAAWF,GACjCA,EAAYG,IAAUA,EAAWH,GACrCO,EAAON,EAAWA,EAAWK,GAC7BF,EAAWvtB,KAAK/F,IAAIqzB,EAAWI,EAAMA,EAAOL,IAC7BG,EAAU,CAAEJ,GAAYD,EAAW,MAClDK,EAAWD,EAIbI,EAAKn3B,KAAK02B,EAAM,CAAC1pB,MAAO4pB,EAAUU,KAAMlF,EAAKC,EAAI7C,SAAUM,EAAM1c,MAAMgkB,EAAIC,KACvEX,EAAIY,KAAMC,GAAYb,EAAKvuB,EAAIC,EAAIC,EAAI2E,EAAQ5E,GAAMiqB,EAAKuE,EAAW5pB,EAAQ1E,GAC5EkvB,GAAad,EAAKvuB,EAAIC,EAAI4E,EAAQ7E,GAAMiqB,EAAKwE,EAAW5pB,EAAQ3E,EAAIC,GACzE0E,GAAS4pB,EAAUQ,EAAKC,EAG1B,OAAOF,EAGT,OAAe,SAAUM,EAAOhB,GAE9B,SAASiB,EAASxI,EAAQ/mB,EAAIC,EAAIC,EAAIC,GACpCkuB,GAAcC,EAAOvH,EAAQ/mB,EAAIC,EAAIC,EAAIC,GAO3C,OAJAovB,EAASjB,MAAQ,SAAStsB,GACxB,OAAOstB,GAAQttB,GAAKA,GAAK,EAAIA,EAAI,IAG5ButB,EAVM,CAWZnB,IC5DY,cACb,IAAIoB,EAAOD,GACP/tB,GAAQ,EACRyoB,EAAK,EACLC,EAAK,EACLuF,EAAe,CAAC,GAChBC,EAAe5C,GACf6C,EAAa7C,GACb8C,EAAe9C,GACf+C,EAAgB/C,GAChBgD,EAAchD,GAElB,SAAS3tB,EAAQ6E,GAQf,OAPAA,EAAKhE,GACLgE,EAAK/D,GAAK,EACV+D,EAAK9D,GAAK+pB,EACVjmB,EAAK7D,GAAK+pB,EACVlmB,EAAK8jB,WAAWiI,GAChBN,EAAe,CAAC,GACZjuB,GAAOwC,EAAK8jB,WAAWkI,IACpBhsB,EAGT,SAAS+rB,EAAa3I,GACpB,IAAIxlB,EAAI6tB,EAAarI,EAAKS,OACtB7nB,EAAKonB,EAAKpnB,GAAK4B,EACf3B,EAAKmnB,EAAKnnB,GAAK2B,EACf1B,EAAKknB,EAAKlnB,GAAK0B,EACfzB,EAAKinB,EAAKjnB,GAAKyB,EACf1B,EAAKF,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GAC/BC,EAAKF,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GACnCinB,EAAKpnB,GAAKA,EACVonB,EAAKnnB,GAAKA,EACVmnB,EAAKlnB,GAAKA,EACVknB,EAAKjnB,GAAKA,EACNinB,EAAKC,WACPzlB,EAAI6tB,EAAarI,EAAKS,MAAQ,GAAK6H,EAAatI,GAAQ,EACxDpnB,GAAM8vB,EAAY1I,GAAQxlB,EAC1B3B,GAAM0vB,EAAWvI,GAAQxlB,GACzB1B,GAAM0vB,EAAaxI,GAAQxlB,GAElB5B,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,IADnCC,GAAM0vB,EAAczI,GAAQxlB,GAEnB3B,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GACnCqvB,EAAKpI,EAAMpnB,EAAIC,EAAIC,EAAIC,IA4C3B,OAxCAhB,EAAQqC,MAAQ,SAASQ,GACvB,OAAOiuB,UAAU/0B,QAAUsG,IAAUQ,EAAG7C,GAAWqC,GAGrDrC,EAAQtC,KAAO,SAASmF,GACtB,OAAOiuB,UAAU/0B,QAAU+uB,GAAMjoB,EAAE,GAAIkoB,GAAMloB,EAAE,GAAI7C,GAAW,CAAC8qB,EAAIC,IAGrE/qB,EAAQqwB,KAAO,SAASxtB,GACtB,OAAOiuB,UAAU/0B,QAAUs0B,EAAO3C,GAAS7qB,GAAI7C,GAAWqwB,GAG5DrwB,EAAQpG,QAAU,SAASiJ,GACzB,OAAOiuB,UAAU/0B,OAASiE,EAAQuwB,aAAa1tB,GAAGkuB,aAAaluB,GAAK7C,EAAQuwB,gBAG9EvwB,EAAQuwB,aAAe,SAAS1tB,GAC9B,OAAOiuB,UAAU/0B,QAAUw0B,EAA4B,mBAAN1tB,EAAmBA,EAAImuB,IAAUnuB,GAAI7C,GAAWuwB,GAGnGvwB,EAAQ+wB,aAAe,SAASluB,GAC9B,OAAOiuB,UAAU/0B,OAASiE,EAAQwwB,WAAW3tB,GAAG4tB,aAAa5tB,GAAG6tB,cAAc7tB,GAAG8tB,YAAY9tB,GAAK7C,EAAQwwB,cAG5GxwB,EAAQwwB,WAAa,SAAS3tB,GAC5B,OAAOiuB,UAAU/0B,QAAUy0B,EAA0B,mBAAN3tB,EAAmBA,EAAImuB,IAAUnuB,GAAI7C,GAAWwwB,GAGjGxwB,EAAQywB,aAAe,SAAS5tB,GAC9B,OAAOiuB,UAAU/0B,QAAU00B,EAA4B,mBAAN5tB,EAAmBA,EAAImuB,IAAUnuB,GAAI7C,GAAWywB,GAGnGzwB,EAAQ0wB,cAAgB,SAAS7tB,GAC/B,OAAOiuB,UAAU/0B,QAAU20B,EAA6B,mBAAN7tB,EAAmBA,EAAImuB,IAAUnuB,GAAI7C,GAAW0wB,GAGpG1wB,EAAQ2wB,YAAc,SAAS9tB,GAC7B,OAAOiuB,UAAU/0B,QAAU40B,EAA2B,mBAAN9tB,EAAmBA,EAAImuB,IAAUnuB,GAAI7C,GAAW2wB,GAG3F3wB,ECxFT,OAAe,SAAUmwB,EAAOhB,GAE9B,SAAS8B,EAAWrJ,EAAQ/mB,EAAIC,EAAIC,EAAIC,GACtC,IAAK6uB,EAAOjI,EAAOsJ,YAAerB,EAAKV,QAAUA,EAU/C,IATA,IAAIU,EACAT,EACA5G,EACAzJ,EAEAzU,EADAigB,GAAK,EAELP,EAAI6F,EAAK9zB,OACT2J,EAAQkiB,EAAOliB,QAEV6kB,EAAIP,GAAG,CAEd,IADexB,GAAf4G,EAAMS,EAAKtF,IAAgBrC,SACtBnJ,EAAIqQ,EAAI1pB,MAAQ,EAAG4E,EAAIke,EAAMzsB,OAAQgjB,EAAIzU,IAAKyU,EAAGqQ,EAAI1pB,OAAS8iB,EAAMzJ,GAAGrZ,MACxE0pB,EAAIY,KAAMC,GAAYb,EAAKvuB,EAAIC,EAAIC,EAAID,IAAOE,EAAKF,GAAMsuB,EAAI1pB,MAAQA,GACpEwqB,GAAad,EAAKvuB,EAAIC,EAAID,IAAOE,EAAKF,GAAMuuB,EAAI1pB,MAAQA,EAAO1E,GACpE0E,GAAS0pB,EAAI1pB,WAGfkiB,EAAOsJ,UAAYrB,EAAOX,GAAcC,EAAOvH,EAAQ/mB,EAAIC,EAAIC,EAAIC,GACnE6uB,EAAKV,MAAQA,EAQjB,OAJA8B,EAAW9B,MAAQ,SAAStsB,GAC1B,OAAOstB,GAAQttB,GAAKA,GAAK,EAAIA,EAAI,IAG5BouB,EA9BM,CA+BZhC,6C1BHY,WACb,IAAIkC,EAAaxJ,EACbmD,EAAK,EACLC,EAAK,EACLqG,GAAW,EAEf,SAASC,EAAQxsB,GACf,IAAIysB,EACAzuB,EAAI,EAGRgC,EAAKikB,WAAU,SAASb,GACtB,IAAIC,EAAWD,EAAKC,SAChBA,GACFD,EAAKplB,EA1Cb,SAAeqlB,GACb,OAAOA,EAASlK,OAAO6J,EAAa,GAAKK,EAASnsB,OAyCnCw1B,CAAMrJ,GACfD,EAAK9kB,EAnCb,SAAc+kB,GACZ,OAAO,EAAIA,EAASlK,OAAO+J,EAAY,GAkCxByJ,CAAKtJ,KAEdD,EAAKplB,EAAIyuB,EAAezuB,GAAKsuB,EAAWlJ,EAAMqJ,GAAgB,EAC9DrJ,EAAK9kB,EAAI,EACTmuB,EAAerJ,MAInB,IAAInpB,EAnCR,SAAkBmpB,GAEhB,IADA,IAAIC,EACGA,EAAWD,EAAKC,UAAUD,EAAOC,EAAS,GACjD,OAAOD,EAgCMwJ,CAAS5sB,GAChB9F,EA9BR,SAAmBkpB,GAEjB,IADA,IAAIC,EACGA,EAAWD,EAAKC,UAAUD,EAAOC,EAASA,EAASnsB,OAAS,GACnE,OAAOksB,EA2BOyJ,CAAU7sB,GAClBhE,EAAK/B,EAAK+D,EAAIsuB,EAAWryB,EAAMC,GAAS,EACxCgC,EAAKhC,EAAM8D,EAAIsuB,EAAWpyB,EAAOD,GAAQ,EAG7C,OAAO+F,EAAKikB,UAAUsI,EAAW,SAASnJ,GACxCA,EAAKplB,GAAKolB,EAAKplB,EAAIgC,EAAKhC,GAAKioB,EAC7B7C,EAAK9kB,GAAK0B,EAAK1B,EAAI8kB,EAAK9kB,GAAK4nB,GAC3B,SAAS9C,GACXA,EAAKplB,GAAKolB,EAAKplB,EAAIhC,IAAOE,EAAKF,GAAMiqB,EACrC7C,EAAK9kB,GAAK,GAAK0B,EAAK1B,EAAI8kB,EAAK9kB,EAAI0B,EAAK1B,EAAI,IAAM4nB,IAgBpD,OAZAsG,EAAQF,WAAa,SAAStuB,GAC5B,OAAOiuB,UAAU/0B,QAAUo1B,EAAatuB,EAAGwuB,GAAWF,GAGxDE,EAAQ3zB,KAAO,SAASmF,GACtB,OAAOiuB,UAAU/0B,QAAUq1B,GAAW,EAAOtG,GAAMjoB,EAAE,GAAIkoB,GAAMloB,EAAE,GAAIwuB,GAAYD,EAAW,KAAO,CAACtG,EAAIC,IAG1GsG,EAAQD,SAAW,SAASvuB,GAC1B,OAAOiuB,UAAU/0B,QAAUq1B,GAAW,EAAMtG,GAAMjoB,EAAE,GAAIkoB,GAAMloB,EAAE,GAAIwuB,GAAYD,EAAW,CAACtG,EAAIC,GAAM,MAGjGsG,oBkB1EM,WACb,IAAIz0B,EAAS,KACTkuB,EAAK,EACLC,EAAK,EACLnxB,EAAU+zB,GAEd,SAASL,EAAKzoB,GAYZ,OAXAA,EAAKhC,EAAIioB,EAAK,EAAGjmB,EAAK1B,EAAI4nB,EAAK,EAC3BnuB,EACFiI,EAAK8jB,WAAWkF,GAAWjxB,IACtBksB,UAAUgF,GAAal0B,EAAS,KAChC+uB,WAAWoF,GAAe,IAE/BlpB,EAAK8jB,WAAWkF,GAAWD,KACtB9E,UAAUgF,GAAaH,GAAc,IACrC7E,UAAUgF,GAAal0B,EAASiL,EAAKjD,EAAIM,KAAK9F,IAAI0uB,EAAIC,KACtDpC,WAAWoF,GAAe7rB,KAAK9F,IAAI0uB,EAAIC,IAAO,EAAIlmB,EAAKjD,KAEvDiD,EAeT,OAZAyoB,EAAK1wB,OAAS,SAASiG,GACrB,OAAOiuB,UAAU/0B,QAAUa,EAAS4wB,GAAS3qB,GAAIyqB,GAAQ1wB,GAG3D0wB,EAAK5vB,KAAO,SAASmF,GACnB,OAAOiuB,UAAU/0B,QAAU+uB,GAAMjoB,EAAE,GAAIkoB,GAAMloB,EAAE,GAAIyqB,GAAQ,CAACxC,EAAIC,IAGlEuC,EAAK1zB,QAAU,SAASiJ,GACtB,OAAOiuB,UAAU/0B,QAAUnC,EAAuB,mBAANiJ,EAAmBA,EAAImuB,IAAUnuB,GAAIyqB,GAAQ1zB,GAGpF0zB,gBHyET,SAAwBvD,GAEtB,OADAkD,GAAYlD,GACLA,2BYjHM,WACb,IAAIe,EAAK,EACLC,EAAK,EACLnxB,EAAU,EACVyI,GAAQ,EAEZ,SAASsvB,EAAU9sB,GACjB,IAAIyF,EAAIzF,EAAKjG,OAAS,EAOtB,OANAiG,EAAKhE,GACLgE,EAAK/D,GAAKlH,EACViL,EAAK9D,GAAK+pB,EACVjmB,EAAK7D,GAAK+pB,EAAKzgB,EACfzF,EAAK8jB,WAKP,SAAsBoC,EAAIzgB,GACxB,OAAO,SAAS2d,GACVA,EAAKC,UACP+H,GAAYhI,EAAMA,EAAKpnB,GAAIkqB,GAAM9C,EAAKS,MAAQ,GAAKpe,EAAG2d,EAAKlnB,GAAIgqB,GAAM9C,EAAKS,MAAQ,GAAKpe,GAEzF,IAAIzJ,EAAKonB,EAAKpnB,GACVC,EAAKmnB,EAAKnnB,GACVC,EAAKknB,EAAKlnB,GAAKnH,EACfoH,EAAKinB,EAAKjnB,GAAKpH,EACfmH,EAAKF,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GAC/BC,EAAKF,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GACnCinB,EAAKpnB,GAAKA,EACVonB,EAAKnnB,GAAKA,EACVmnB,EAAKlnB,GAAKA,EACVknB,EAAKjnB,GAAKA,GAnBI4vB,CAAa7F,EAAIzgB,IAC7BjI,GAAOwC,EAAK8jB,WAAWkI,IACpBhsB,EAiCT,OAZA8sB,EAAUtvB,MAAQ,SAASQ,GACzB,OAAOiuB,UAAU/0B,QAAUsG,IAAUQ,EAAG8uB,GAAatvB,GAGvDsvB,EAAUj0B,KAAO,SAASmF,GACxB,OAAOiuB,UAAU/0B,QAAU+uB,GAAMjoB,EAAE,GAAIkoB,GAAMloB,EAAE,GAAI8uB,GAAa,CAAC7G,EAAIC,IAGvE4G,EAAU/3B,QAAU,SAASiJ,GAC3B,OAAOiuB,UAAU/0B,QAAUnC,GAAWiJ,EAAG8uB,GAAa/3B,GAGjD+3B,YNnCM,WACb,IAAIxD,EAAKD,GACLG,EAAWD,GAEf,SAASwD,EAASp4B,GAChB,IAAI4I,EACA2c,EAEAla,EACA+iB,EACAK,EAEA4J,EACAC,EANAxnB,EAAI9Q,EAAKuC,OAITysB,EAAQ,IAAIpiB,MAAMkE,GAGlBynB,EAAY,GAEhB,IAAKhT,EAAI,EAAGA,EAAIzU,IAAKyU,EACnB3c,EAAI5I,EAAKulB,GAAIkJ,EAAOO,EAAMzJ,GAAK,IAAIuJ,EAAKlmB,GACP,OAA5ByvB,EAAS1D,EAAG/rB,EAAG2c,EAAGvlB,MAAmBq4B,GAAU,MAElDE,EADAD,EA/BQ,KA+Be7J,EAAKkG,GAAK0D,IACZC,KAAWC,EAAY9D,GAAYhG,GAI5D,IAAKlJ,EAAI,EAAGA,EAAIzU,IAAKyU,EAEnB,GADAkJ,EAAOO,EAAMzJ,GACC,OADG8S,EAASxD,EAAS70B,EAAKulB,GAAIA,EAAGvlB,MACvBq4B,GAAU,IAG3B,CAEL,KADAjK,EAASmK,EA1CD,IA0CuBF,IAClB,MAAM,IAAIjH,MAAM,YAAciH,GAC3C,GAAIjK,IAAWqG,GAAW,MAAM,IAAIrD,MAAM,cAAgBiH,GACtDjK,EAAOM,SAAUN,EAAOM,SAASxvB,KAAKuvB,GACrCL,EAAOM,SAAW,CAACD,GACxBA,EAAKL,OAASA,MATuB,CACrC,GAAI/iB,EAAM,MAAM,IAAI+lB,MAAM,kBAC1B/lB,EAAOojB,EAWX,IAAKpjB,EAAM,MAAM,IAAI+lB,MAAM,WAI3B,GAHA/lB,EAAK+iB,OAASoG,GACdnpB,EAAK8jB,YAAW,SAASV,GAAQA,EAAKS,MAAQT,EAAKL,OAAOc,MAAQ,IAAKpe,KAAMqe,WAAWC,GACxF/jB,EAAK+iB,OAAS,KACVtd,EAAI,EAAG,MAAM,IAAIsgB,MAAM,SAE3B,OAAO/lB,EAWT,OARA+sB,EAASzD,GAAK,SAAStrB,GACrB,OAAOiuB,UAAU/0B,QAAUoyB,EAAKT,GAAS7qB,GAAI+uB,GAAYzD,GAG3DyD,EAASvD,SAAW,SAASxrB,GAC3B,OAAOiuB,UAAU/0B,QAAUsyB,EAAWX,GAAS7qB,GAAI+uB,GAAYvD,GAG1DuD,QC4BM,WACb,IAAIT,EAAaxJ,GACbmD,EAAK,EACLC,EAAK,EACLqG,EAAW,KAEf,SAASY,EAAKntB,GACZ,IAAIlC,EA/BR,SAAkBkC,GAShB,IARA,IACIojB,EAEAG,EACAF,EACAnJ,EACAzU,EANA0nB,EAAO,IAAIhD,GAASnqB,EAAM,GAE1B2jB,EAAQ,CAACwJ,GAMN/J,EAAOO,EAAM9Z,OAClB,GAAIwZ,EAAWD,EAAK6E,EAAE5E,SAEpB,IADAD,EAAKC,SAAW,IAAI9hB,MAAMkE,EAAI4d,EAASnsB,QAClCgjB,EAAIzU,EAAI,EAAGyU,GAAK,IAAKA,EACxByJ,EAAM9vB,KAAK0vB,EAAQH,EAAKC,SAASnJ,GAAK,IAAIiQ,GAAS9G,EAASnJ,GAAIA,IAChEqJ,EAAMR,OAASK,EAMrB,OADC+J,EAAKpK,OAAS,IAAIoH,GAAS,KAAM,IAAI9G,SAAW,CAAC8J,GAC3CA,EAWGC,CAASptB,GAOjB,GAJAlC,EAAEmmB,UAAUoJ,GAAYvvB,EAAEilB,OAAOoC,GAAKrnB,EAAEgiB,EACxChiB,EAAEgmB,WAAWwJ,GAGTf,EAAUvsB,EAAK8jB,WAAWyJ,OAIzB,CACH,IAAItzB,EAAO+F,EACP9F,EAAQ8F,EACR7F,EAAS6F,EACbA,EAAK8jB,YAAW,SAASV,GACnBA,EAAKplB,EAAI/D,EAAK+D,IAAG/D,EAAOmpB,GACxBA,EAAKplB,EAAI9D,EAAM8D,IAAG9D,EAAQkpB,GAC1BA,EAAKS,MAAQ1pB,EAAO0pB,QAAO1pB,EAASipB,MAE1C,IAAIhmB,EAAInD,IAASC,EAAQ,EAAIoyB,EAAWryB,EAAMC,GAAS,EACnDszB,EAAKpwB,EAAInD,EAAK+D,EACdyvB,EAAKxH,GAAM/rB,EAAM8D,EAAIZ,EAAIowB,GACzBE,EAAKxH,GAAM/rB,EAAO0pB,OAAS,GAC/B7jB,EAAK8jB,YAAW,SAASV,GACvBA,EAAKplB,GAAKolB,EAAKplB,EAAIwvB,GAAMC,EACzBrK,EAAK9kB,EAAI8kB,EAAKS,MAAQ6J,KAI1B,OAAO1tB,EAOT,SAASqtB,EAAU3D,GACjB,IAAIrG,EAAWqG,EAAErG,SACbsK,EAAWjE,EAAE3G,OAAOM,SACpBuK,EAAIlE,EAAExP,EAAIyT,EAASjE,EAAExP,EAAI,GAAK,KAClC,GAAImJ,EAAU,EA5GlB,SAAuBqG,GAMrB,IALA,IAIIkE,EAJA7D,EAAQ,EACRC,EAAS,EACT3G,EAAWqG,EAAErG,SACbnJ,EAAImJ,EAASnsB,SAERgjB,GAAK,IACZ0T,EAAIvK,EAASnJ,IACX4F,GAAKiK,EACP6D,EAAEzI,GAAK4E,EACPA,GAAS6D,EAAExwB,GAAK4sB,GAAU4D,EAAE3K,GAmG1B4K,CAAcnE,GACd,IAAIoE,GAAYzK,EAAS,GAAGvD,EAAIuD,EAASA,EAASnsB,OAAS,GAAG4oB,GAAK,EAC/D8N,GACFlE,EAAE5J,EAAI8N,EAAE9N,EAAIwM,EAAW5C,EAAEzB,EAAG2F,EAAE3F,GAC9ByB,EAAEvE,EAAIuE,EAAE5J,EAAIgO,GAEZpE,EAAE5J,EAAIgO,OAECF,IACTlE,EAAE5J,EAAI8N,EAAE9N,EAAIwM,EAAW5C,EAAEzB,EAAG2F,EAAE3F,IAEhCyB,EAAE3G,OAAO6E,EAoBX,SAAmB8B,EAAGkE,EAAGnJ,GACvB,GAAImJ,EAAG,CAUL,IATA,IAQI7D,EARAgE,EAAMrE,EACNsE,EAAMtE,EACNQ,EAAM0D,EACNK,EAAMF,EAAIhL,OAAOM,SAAS,GAC1B6K,EAAMH,EAAI5I,EACVgJ,EAAMH,EAAI7I,EACViJ,EAAMlE,EAAI/E,EACVkJ,EAAMJ,EAAI9I,EAEP+E,EAAMP,GAAUO,GAAM6D,EAAMtE,GAASsE,GAAM7D,GAAO6D,GACvDE,EAAMxE,GAASwE,IACfD,EAAMrE,GAAUqE,IACZ7b,EAAIuX,GACRK,EAAQG,EAAIpK,EAAIsO,EAAML,EAAIjO,EAAIoO,EAAM5B,EAAWpC,EAAIjC,EAAG8F,EAAI9F,IAC9C,IACV2B,GAAYK,GAAaC,EAAKR,EAAGjF,GAAWiF,EAAGK,GAC/CmE,GAAOnE,EACPoE,GAAOpE,GAETqE,GAAOlE,EAAI/E,EACX+I,GAAOH,EAAI5I,EACXkJ,GAAOJ,EAAI9I,EACXgJ,GAAOH,EAAI7I,EAET+E,IAAQP,GAAUqE,KACpBA,EAAIlwB,EAAIosB,EACR8D,EAAI7I,GAAKiJ,EAAMD,GAEbJ,IAAQtE,GAASwE,KACnBA,EAAInwB,EAAIiwB,EACRE,EAAI9I,GAAK+I,EAAMG,EACf5J,EAAWiF,GAGf,OAAOjF,EAxDM6J,CAAU5E,EAAGkE,EAAGlE,EAAE3G,OAAO6E,GAAK+F,EAAS,IAItD,SAASL,EAAW5D,GAClBA,EAAEzB,EAAEjqB,EAAI0rB,EAAE5J,EAAI4J,EAAE3G,OAAOoC,EACvBuE,EAAEvE,GAAKuE,EAAE3G,OAAOoC,EAqDlB,SAASoI,EAASnK,GAChBA,EAAKplB,GAAKioB,EACV7C,EAAK9kB,EAAI8kB,EAAKS,MAAQqC,EAexB,OAZAiH,EAAKb,WAAa,SAAStuB,GACzB,OAAOiuB,UAAU/0B,QAAUo1B,EAAatuB,EAAGmvB,GAAQb,GAGrDa,EAAKt0B,KAAO,SAASmF,GACnB,OAAOiuB,UAAU/0B,QAAUq1B,GAAW,EAAOtG,GAAMjoB,EAAE,GAAIkoB,GAAMloB,EAAE,GAAImvB,GAASZ,EAAW,KAAO,CAACtG,EAAIC,IAGvGiH,EAAKZ,SAAW,SAASvuB,GACvB,OAAOiuB,UAAU/0B,QAAUq1B,GAAW,EAAMtG,GAAMjoB,EAAE,GAAIkoB,GAAMloB,EAAE,GAAImvB,GAASZ,EAAW,CAACtG,EAAIC,GAAM,MAG9FiH,4BM3OM,SAASpK,EAAQ/mB,EAAIC,EAAIC,EAAIC,GAC1C,IACI+d,EACA0E,EAFA+E,EAAQZ,EAAOM,SACZ5d,EAAIke,EAAMzsB,OACRq3B,EAAO,IAAIhtB,MAAMkE,EAAI,GAE9B,IAAK8oB,EAAK,GAAK3P,EAAM1E,EAAI,EAAGA,EAAIzU,IAAKyU,EACnCqU,EAAKrU,EAAI,GAAK0E,GAAO+E,EAAMzJ,GAAGrZ,OAKhC,SAASisB,EAAU5S,EAAGwL,EAAG7kB,EAAO7E,EAAIC,EAAIC,EAAIC,GAC1C,GAAI+d,GAAKwL,EAAI,EAAG,CACd,IAAItC,EAAOO,EAAMzJ,GAGjB,OAFAkJ,EAAKpnB,GAAKA,EAAIonB,EAAKnnB,GAAKA,EACxBmnB,EAAKlnB,GAAKA,OAAIknB,EAAKjnB,GAAKA,GAI1B,IAAIqyB,EAAcD,EAAKrU,GACnBuU,EAAe5tB,EAAQ,EAAK2tB,EAC5B1J,EAAI5K,EAAI,EACRwU,EAAKhJ,EAAI,EAEb,KAAOZ,EAAI4J,GAAI,CACb,IAAIC,EAAM7J,EAAI4J,IAAO,EACjBH,EAAKI,GAAOF,EAAa3J,EAAI6J,EAAM,EAClCD,EAAKC,EAGPF,EAAcF,EAAKzJ,EAAI,GAAOyJ,EAAKzJ,GAAK2J,GAAgBvU,EAAI,EAAI4K,KAAKA,EAE1E,IAAI8J,EAAYL,EAAKzJ,GAAK0J,EACtBK,EAAahuB,EAAQ+tB,EAEzB,GAAK1yB,EAAKF,EAAOG,EAAKF,EAAK,CACzB,IAAI6yB,GAAM9yB,EAAK6yB,EAAa3yB,EAAK0yB,GAAa/tB,EAC9CisB,EAAU5S,EAAG4K,EAAG8J,EAAW5yB,EAAIC,EAAI6yB,EAAI3yB,GACvC2wB,EAAUhI,EAAGY,EAAGmJ,EAAYC,EAAI7yB,EAAIC,EAAIC,OACnC,CACL,IAAI4yB,GAAM9yB,EAAK4yB,EAAa1yB,EAAKyyB,GAAa/tB,EAC9CisB,EAAU5S,EAAG4K,EAAG8J,EAAW5yB,EAAIC,EAAIC,EAAI6yB,GACvCjC,EAAUhI,EAAGY,EAAGmJ,EAAY7yB,EAAI+yB,EAAI7yB,EAAIC,IAjC5C2wB,CAAU,EAAGrnB,EAAGsd,EAAOliB,MAAO7E,EAAIC,EAAIC,EAAIC,oDCN7B,SAAS4mB,EAAQ/mB,EAAIC,EAAIC,EAAIC,IAC1B,EAAf4mB,EAAOc,MAAY5c,GAAQkkB,IAAMpI,EAAQ/mB,EAAIC,EAAIC,EAAIC,8CCFzC,SAAS6yB,KACtB,IAAI3jB,KAAEA,EAAIoH,WAAEA,EAAU5G,UAAEA,EAASqG,UAAEA,GAAcze,MAC7CqG,MAAEA,EAAKC,OAAEA,EAAMhF,QAAEA,GAAYsW,EAC7B4jB,EAAe/c,EAAmB,QAAE,GAExC+c,EAAazD,MACXyD,EAAazD,KACTyD,EAAazD,MAGnB,IAAI0D,EAAgBC,EACPF,GAAc,SAAU1xB,GACjC,OAAOA,EAAE5I,QAEViqB,KAAI,SAAUrhB,GACb,OAAOA,EAAEsD,SAUTuuB,EARaC,KAEd7D,KAAK8D,GAAYL,EAAazD,OAC9B3yB,KAAK,CAACiB,EAAOC,IACb4xB,WAAW52B,EAAQ,IACnB62B,aAAa72B,EAAQ,IACrB82B,cAAc92B,EAAQ,GAAK0d,EAAWc,cACtCuY,YAAY/2B,EAAQ,GACLw6B,CAAWL,GAE7BrjB,EAAUojB,aAAeG,EAEmBvjB,EAAUojB,aC5BxD,MAAMO,GAAenyB,KAAKqP,GAAK,IAG/B,IAAI+iB,GAAIC,GAER,SAASC,GAAUpyB,GACjB,OAAOA,EAAE3G,KAGX,SAASg5B,KACP,MAAO,QAGT,SAASC,KACP,MAAO,SAGT,SAASC,GAAcvyB,GACrB,OAAOA,EAAEsD,MAGX,SAASkvB,KACP,OAA+B,MAAL,EAAhB1yB,KAAK+nB,UAGjB,SAAS4K,KACP,OAAO,EAKT,SAASC,GAAYC,EAAiB3yB,EAAG5I,EAAMw7B,GAC7C,GAAI5yB,EAAE6yB,OAAQ,OACd,MAAMnN,EAAIiN,EAAgBz0B,QACxB6uB,EAAQ4F,EAAgB5F,MAC1BrH,EAAEoN,UAAU,EAAG,GAAIZ,IAAM,GAAKnF,EAAOoF,GAAKpF,GAC1C,IAAItsB,EAAI,EACNM,EAAI,EACJgyB,EAAO,EACT,MAAM7qB,EAAI9Q,EAAKuC,OAEf,MADEi5B,IACOA,EAAK1qB,GAAG,CACflI,EAAI5I,EAAKw7B,GACTlN,EAAEsN,OACFtN,EAAEuN,UAAYvN,EAAEwN,YAAc,UAC9BxN,EAAEyN,UAAY,SACdzN,EAAE3nB,KAAO,GAAGiC,EAAEozB,SAASpzB,EAAEqzB,cAAcrzB,EAAE1E,KAAO,GAAKyxB,QAAY/sB,EAAEjC,OACnE,IAAIsyB,EAAI3K,EAAE/T,YAAY3R,EAAE3G,KAAO,KAAKkD,MAAQwwB,EAC1CntB,EAAII,EAAE1E,MAAQ,EAChB,GAAI0E,EAAE7F,OAAQ,CACZ,MAAMm5B,EAAKxzB,KAAK2d,IAAIzd,EAAE7F,OAAS83B,IAC7BsB,EAAKzzB,KAAK4d,IAAI1d,EAAE7F,OAAS83B,IACzBuB,EAAMnD,EAAIkD,EACVE,EAAMpD,EAAIiD,EACVI,EAAM9zB,EAAI2zB,EACVI,EAAM/zB,EAAI0zB,EACZjD,EAAMvwB,KAAK/F,IAAI+F,KAAKkP,IAAIwkB,EAAMG,GAAM7zB,KAAKkP,IAAIwkB,EAAMG,IAAQ,IAAS,GAAM,EAC1E/zB,IAAME,KAAK/F,IAAI+F,KAAKkP,IAAIykB,EAAMC,GAAM5zB,KAAKkP,IAAIykB,EAAMC,SAEnDrD,EAAMA,EAAI,IAAS,GAAM,EAQ3B,GANIzwB,EAAImzB,IAAMA,EAAOnzB,GACjBa,EAAI4vB,GAAK6B,IAAM,IACjBzxB,EAAI,EACJM,GAAKgyB,EACLA,EAAO,GAELhyB,EAAInB,GAAKuyB,GAAI,MACjBzM,EAAEkO,WAAWnzB,GAAK4vB,GAAK,IAAMtD,GAAQhsB,GAAKnB,GAAK,IAAMmtB,GACjD/sB,EAAE7F,QAAQurB,EAAEvrB,OAAO6F,EAAE7F,OAAS83B,IAClCvM,EAAEmO,SAAS7zB,EAAE3G,KAAM,EAAG,GAClB2G,EAAExI,UACJkuB,EAAExtB,UAAY,EAAI8H,EAAExI,QACpBkuB,EAAEoO,WAAW9zB,EAAE3G,KAAM,EAAG,IAG1BqsB,EAAEqO,UACF/zB,EAAEzD,MAAQ8zB,EACVrwB,EAAExD,OAASoD,EACXI,EAAEg0B,KAAOvzB,EACTT,EAAEi0B,KAAOlzB,EACTf,EAAErB,GAAK0xB,GAAK,EACZrwB,EAAEpB,GAAKgB,GAAK,EACZI,EAAEvB,IAAMuB,EAAErB,GACVqB,EAAEtB,IAAMsB,EAAEpB,GACVoB,EAAEk0B,SAAU,EACZzzB,GAAK4vB,EAEP,MAAM8D,EAASzO,EAAE0O,aAAa,EAAG,GAAIlC,IAAM,GAAKnF,EAAOoF,GAAKpF,GAAO31B,KACjEy7B,EAAS,GACX,OAASD,GAAM,GAAG,CAEhB,KADA5yB,EAAI5I,EAAKw7B,IACFsB,QAAS,SAChB,MAAM7D,EAAIrwB,EAAEzD,MACV83B,EAAMhE,GAAK,EACb,IAAIzwB,EAAII,EAAEpB,GAAKoB,EAAEtB,GAEjB,IAAK,IAAIie,EAAI,EAAGA,EAAI/c,EAAIy0B,EAAK1X,IAAKkW,EAAOlW,GAAK,EAE9C,GADAlc,EAAIT,EAAEg0B,KACG,MAALvzB,EAAW,OACfM,EAAIf,EAAEi0B,KACN,IAAIK,EAAO,EACTC,GAAW,EACb,IAAK,IAAIpM,EAAI,EAAGA,EAAIvoB,EAAGuoB,IAAK,CAC1B,IAAK,IAAIxL,EAAI,EAAGA,EAAI0T,EAAG1T,IAAK,CAC1B,IAAI4K,EAAI8M,EAAMlM,GAAKxL,GAAK,GACtBiL,EAAIuM,GAASpzB,EAAIonB,IAAM+J,IAAM,IAAMzxB,EAAIkc,IAAO,GAAK,GAAM,GAAMA,EAAI,GAAO,EAC5EkW,EAAOtL,IAAMK,EACb0M,GAAQ1M,EAEN0M,EAAMC,EAAUpM,GAElBnoB,EAAEtB,KACFkB,IACAuoB,IACApnB,KAGJf,EAAEpB,GAAKoB,EAAEtB,GAAK61B,EACdv0B,EAAE6yB,OAASA,EAAOnpB,MAAM,GAAI1J,EAAEpB,GAAKoB,EAAEtB,IAAM21B,IAK/C,SAASG,GAAalrB,EAAKmrB,EAAOC,GAChCA,IAAO,EACP,MAAM7B,EAASvpB,EAAIupB,OACjBxC,EAAI/mB,EAAI/M,OAAS,EACjBo4B,EAAKrrB,EAAI7I,GAAK4vB,GAAK,GACnBuE,EAAU,IAALD,EACLE,EAAM,GAAKD,EACXh1B,EAAI0J,EAAI1K,GAAK0K,EAAI5K,GACnB,IACEo2B,EADEr0B,GAAK6I,EAAIvI,EAAIuI,EAAI5K,IAAMg2B,GAAMC,GAAM,GAEvC,IAAK,IAAIxM,EAAI,EAAGA,EAAIvoB,EAAGuoB,IAAK,CAC1B2M,EAAO,EACP,IAAK,IAAInY,EAAI,EAAGA,GAAK0T,EAAG1T,IACtB,IAAMmY,GAAQD,GAAQlY,EAAI0T,GAAKyE,EAAOjC,EAAO1K,EAAIkI,EAAI1T,MAAQiY,EAAK,IAAMH,EAAMh0B,EAAIkc,GAAI,OAAO,EAE/Flc,GAAKi0B,EAEP,OAAO,EAGT,SAASK,GAAYC,EAAQh1B,GAC3B,MAAMi1B,EAAKD,EAAO,GAChBE,EAAKF,EAAO,GACVh1B,EAAES,EAAIT,EAAEvB,GAAKw2B,EAAGx0B,IAAGw0B,EAAGx0B,EAAIT,EAAES,EAAIT,EAAEvB,IAClCuB,EAAEe,EAAIf,EAAEtB,GAAKu2B,EAAGl0B,IAAGk0B,EAAGl0B,EAAIf,EAAEe,EAAIf,EAAEtB,IAClCsB,EAAES,EAAIT,EAAErB,GAAKu2B,EAAGz0B,IAAGy0B,EAAGz0B,EAAIT,EAAES,EAAIT,EAAErB,IAClCqB,EAAEe,EAAIf,EAAEpB,GAAKs2B,EAAGn0B,IAAGm0B,EAAGn0B,EAAIf,EAAEe,EAAIf,EAAEpB,IAOxC,SAASu2B,GAAkB75B,GACzB,IAAI5E,EAAI4E,EAAK,GAAKA,EAAK,GACvB,OAAO,SAAUiF,GACf,MAAO,CAAC7J,GAAK6J,GAAK,IAAOT,KAAK4d,IAAInd,GAAIA,EAAIT,KAAK2d,IAAIld,KA+BvD,SAAS60B,GAAUltB,GACjB,MAAM0M,EAAI,GACV,IAAI+H,GAAK,EACT,OAASA,EAAIzU,GAAG0M,EAAE+H,GAAK,EACvB,OAAO/H,EAGT,SAASygB,GAAQr1B,GACf,MAAoB,mBAANA,EACVA,EACA,WACE,OAAOA,GAIf,MAAMs1B,GAAU,CACdC,YAAaJ,GACbK,YA5CF,SAA2Bl6B,GACzB,MACEotB,EADS,EACEptB,EAAK,GAAMA,EAAK,GAC7B,IAAImF,EAAI,EACNM,EAAI,EACN,OAAO,SAAUR,GACf,MAAMk1B,EAAOl1B,EAAI,GAAK,EAAI,EAE1B,OAAST,KAAKqpB,KAAK,EAAI,EAAIsM,EAAOl1B,GAAKk1B,EAAQ,GAC7C,KAAK,EACHh1B,GAAKioB,EACL,MACF,KAAK,EACH3nB,GAZK,EAaL,MACF,KAAK,EACHN,GAAKioB,EACL,MACF,QACE3nB,GAlBK,EAqBT,MAAO,CAACN,EAAGM,MAyBA,cACb,IAAIzF,EAAO,CAAC,IAAK,KACfjC,EAAO+4B,GACPr0B,EAAOs0B,GACPx6B,EAAW06B,GACXmD,EAAYpD,GACZqD,EAAarD,GACbn4B,EAASq4B,GACTh7B,EAAUi7B,GACVz0B,EAASm3B,GACTS,EAAQ,GACR93B,EAAe4mB,EAAAA,EACfmD,EAAS/nB,KAAK+nB,OACdgO,EAAS,KACT3hB,EAAM,aACR,MAAM4hB,EAAQ,GAkGd,SAASvL,EAAMkK,EAAOnrB,EAAK0rB,GAEzB,MAAM9W,EAAS5U,EAAI7I,EACjB0d,EAAS7U,EAAIvI,EACbg1B,EAAWj2B,KAAKqpB,KAAK7tB,EAAK,GAAKA,EAAK,GAAKA,EAAK,GAAKA,EAAK,IACxDuE,EAAI7B,EAAO1C,GACX06B,EAAKnO,IAAW,GAAM,GAAK,EAC7B,IAAIoO,EAEFvN,EACAC,EAFApoB,GAAKy1B,EAIP,MAAQC,EAAOp2B,EAAGU,GAAKy1B,MACrBtN,IAAOuN,EAAK,GACZtN,IAAOsN,EAAK,KAERn2B,KAAK9F,IAAI8F,KAAKkP,IAAI0Z,GAAK5oB,KAAKkP,IAAI2Z,KAAQoN,KAK5C,GAHAzsB,EAAI7I,EAAIyd,EAASwK,EACjBpf,EAAIvI,EAAIod,EAASwK,IAEbrf,EAAI7I,EAAI6I,EAAI7K,GAAK,GAAK6K,EAAIvI,EAAIuI,EAAI5K,GAAK,GAAK4K,EAAI7I,EAAI6I,EAAI3K,GAAKrD,EAAK,IAAMgO,EAAIvI,EAAIuI,EAAI1K,GAAKtD,EAAK,IAE7F05B,GAAWR,GAAalrB,EAAKmrB,EAAOn5B,EAAK,KACvC05B,IAnMYr1B,EAmMgBq1B,KAnMnBpgB,EAmMctL,GAlMzB7I,EAAImU,EAAEjW,GAAKgB,EAAE,GAAGc,GAAKmU,EAAEnU,EAAImU,EAAEnW,GAAKkB,EAAE,GAAGc,GAAKmU,EAAE7T,EAAI6T,EAAEhW,GAAKe,EAAE,GAAGoB,GAAK6T,EAAE7T,EAAI6T,EAAElW,GAAKiB,EAAE,GAAGoB,KAkM9C,CACxC,MAAM8xB,EAASvpB,EAAIupB,OACjBxC,EAAI/mB,EAAI/M,OAAS,EACjBm4B,EAAKp5B,EAAK,IAAM,EAChBq5B,EAAKrrB,EAAI7I,GAAK4vB,GAAK,GACnBuE,EAAU,IAALD,EACLE,EAAM,GAAKD,EACXh1B,EAAI0J,EAAI1K,GAAK0K,EAAI5K,GACnB,IAAIo2B,EACFr0B,GAAK6I,EAAIvI,EAAIuI,EAAI5K,IAAMg2B,GAAMC,GAAM,GACrC,IAAK,IAAIxM,EAAI,EAAGA,EAAIvoB,EAAGuoB,IAAK,CAC1B2M,EAAO,EACP,IAAK,IAAInY,EAAI,EAAGA,GAAK0T,EAAG1T,IACtB8X,EAAMh0B,EAAIkc,IAAOmY,GAAQD,GAAQlY,EAAI0T,GAAKyE,EAAOjC,EAAO1K,EAAIkI,EAAI1T,MAAQiY,EAAK,GAE/En0B,GAAKi0B,EAGP,cADOprB,EAAIupB,QACJ,EArNjB,IAAsBje,EAAGjV,EAyNrB,OAAO,EA4ET,OA1NAm2B,EAAM5hB,IAAM,SAAUwW,GACpB,OAAOgE,UAAU/0B,QAAWua,EAAMmhB,GAAQ3K,GAAKoL,GAAS5hB,GAG1D4hB,EAAMD,OAAS,SAAUnL,GACvB,OAAOgE,UAAU/0B,QAAWk8B,EAASR,GAAQ3K,GAAKoL,GAASD,GAG7DC,EAAM7hB,MAAQ,WACZ,MAAMiiB,EAAUp2B,KAAK9F,OAAOsB,GAC5B42B,GAAKgE,GAAW,EAChB/D,GAAK+D,EAEL,MAAMvD,EAwER,SAAoBkD,GAClBA,EAAOt5B,MAAQs5B,EAAOr5B,OAAS,EAC/B,MAAMuwB,EAAQjtB,KAAKqpB,KAAK0M,EAAOM,WAAW,MAAM/B,aAAa,EAAG,EAAG,EAAG,GAAGh9B,KAAKuC,QAAU,GACxFk8B,EAAOt5B,OAAS21B,IAAM,GAAKnF,EAC3B8I,EAAOr5B,OAAS21B,GAAKpF,EACrB,MAAM7uB,EAAU23B,EAAOM,WAAW,MAGlC,OAFAj4B,EAAQ+0B,UAAY/0B,EAAQg1B,YAAc,UAC1Ch1B,EAAQi1B,UAAY,SACb,CAAEj1B,QAAAA,EAAS6uB,MAAAA,GAhFMoJ,CAAWN,KACjCpB,EAAQqB,EAAMrB,MAAQqB,EAAMrB,MAAQW,IAAW95B,EAAK,IAAM,GAAKA,EAAK,IACpE4M,EAAI0tB,EAAMj8B,OACVy8B,EAAO,GACPh/B,EAAOw+B,EACJ1yB,KAAI,SAAUlD,EAAG2c,GAQhB,OAPA3c,EAAE3G,KAAOA,EAAKsI,KAAKzL,KAAM8J,EAAG2c,GAC5B3c,EAAEjC,KAAOA,EAAK4D,KAAKzL,KAAM8J,EAAG2c,GAC5B3c,EAAEozB,MAAQsC,EAAU/zB,KAAKzL,KAAM8J,EAAG2c,GAClC3c,EAAEqzB,OAASsC,EAAWh0B,KAAKzL,KAAM8J,EAAG2c,GACpC3c,EAAE7F,OAASA,EAAOwH,KAAKzL,KAAM8J,EAAG2c,GAChC3c,EAAE1E,OAASzD,EAAS8J,KAAKzL,KAAM8J,EAAG2c,GAClC3c,EAAExI,QAAUA,EAAQmK,KAAKzL,KAAM8J,EAAG2c,GAC3B3c,KAERnD,MAAK,SAAU+X,EAAGjV,GACjB,OAAOA,EAAErE,KAAOsZ,EAAEtZ,QAExB,IAAIqhB,GAAK,EACPqY,EAAUc,EAAMrB,MAEZ,CACE,CACEh0B,EAAG,EACHM,EAAG,GAEL,CACEN,EAAGnF,EAAK,GACRyF,EAAGzF,EAAK,KARZ,KAiDN,OAnCA,WACE,MAAM2Y,EAAQoiB,KAAKC,MACnB,KAAOD,KAAKC,MAAQriB,EAAQnW,KAAkB6e,EAAIzU,GAAG,CACnD,MAAMlI,EAAI5I,EAAKulB,GACf3c,EAAES,EAAKnF,EAAK,IAAMusB,IAAW,KAAS,EACtC7nB,EAAEe,EAAKzF,EAAK,IAAMusB,IAAW,KAAS,EACtC6K,GAAYC,EAAiB3yB,EAAG5I,EAAMulB,GAClC3c,EAAEk0B,SAAW3J,EAAMkK,EAAOz0B,EAAGg1B,KAC/BoB,EAAK9/B,KAAK0J,GACNg1B,EACGc,EAAMS,UAETxB,GAAYC,EAAQh1B,GAGtBg1B,EAAS,CACP,CAAEv0B,EAAGT,EAAES,EAAIT,EAAEvB,GAAIsC,EAAGf,EAAEe,EAAIf,EAAEtB,IAC5B,CAAE+B,EAAGT,EAAES,EAAIT,EAAErB,GAAIoC,EAAGf,EAAEe,EAAIf,EAAEpB,KAIhCoB,EAAES,GAAKnF,EAAK,IAAM,EAClB0E,EAAEe,GAAKzF,EAAK,IAAM,GAIlBqhB,GAAKzU,GAEPgM,EAAIkiB,GAGNN,EAAMU,MAAQJ,EACdN,EAAMW,QAAUzB,EAlClB0B,GAqCOZ,GA+DTA,EAAMa,WAAaC,IACjB,MAAMC,EAAMhB,KACLt5B,EAAOC,GAAUlB,EAClB+4B,EAAM93B,GAAS,EACfk4B,EAAQW,IAAW74B,GAAS,GAAKC,GACvCq6B,EAAIt6B,MAAQA,EACZs6B,EAAIr6B,OAASA,EACb,MAAMs6B,EAAMD,EAAIV,WAAW,MAC3BW,EAAIC,UAAUH,EAAK,EAAG,EAAGA,EAAIr6B,MAAOq6B,EAAIp6B,OAAQ,EAAG,EAAGD,EAAOC,GAC7D,MAAMw6B,EAAYF,EAAI1C,aAAa,EAAG,EAAG73B,EAAOC,GAAQpF,KACxD,IAAK,IAAI+wB,EAAI,EAAGA,EAAI3rB,EAAQ2rB,IAC1B,IAAK,IAAIxL,EAAI,EAAGA,EAAIpgB,EAAOogB,IAAK,CAC9B,MAAM4K,EAAI8M,EAAMlM,GAAKxL,GAAK,GACpBsa,EAAO9O,EAAI5rB,EAAQogB,GAAM,EAEzBiL,EADOoP,EAAUC,IAAQ,KAAOD,EAAUC,EAAM,IAAM,KAAOD,EAAUC,EAAM,IAAM,IACxE,GAAM,GAAMta,EAAI,GAAO,EACxC8X,EAAMlN,IAAMK,EAMhB,OAHAkO,EAAMrB,MAAQA,EACdqB,EAAMS,UAAW,EAEVT,GAGTA,EAAMh4B,aAAe,SAAU4sB,GAC7B,OAAOgE,UAAU/0B,QAAWmE,EAAoB,MAAL4sB,EAAYhG,EAAAA,EAAWgG,EAAIoL,GAASh4B,GAGjFg4B,EAAMF,MAAQ,SAAUlL,GACtB,OAAOgE,UAAU/0B,QAAWi8B,EAAQlL,EAAIoL,GAASF,GAGnDE,EAAMx6B,KAAO,SAAUovB,GACrB,OAAOgE,UAAU/0B,QAAW2B,EAAO,EAAEovB,EAAE,IAAKA,EAAE,IAAMoL,GAASx6B,GAG/Dw6B,EAAM/3B,KAAO,SAAU2sB,GACrB,OAAOgE,UAAU/0B,QAAWoE,EAAOs3B,GAAQ3K,GAAKoL,GAAS/3B,GAG3D+3B,EAAMJ,UAAY,SAAUhL,GAC1B,OAAOgE,UAAU/0B,QAAW+7B,EAAYL,GAAQ3K,GAAKoL,GAASJ,GAGhEI,EAAMH,WAAa,SAAUjL,GAC3B,OAAOgE,UAAU/0B,QAAWg8B,EAAaN,GAAQ3K,GAAKoL,GAASH,GAGjEG,EAAM37B,OAAS,SAAUuwB,GACvB,OAAOgE,UAAU/0B,QAAWQ,EAASk7B,GAAQ3K,GAAKoL,GAAS37B,GAG7D27B,EAAMz8B,KAAO,SAAUqxB,GACrB,OAAOgE,UAAU/0B,QAAWN,EAAOg8B,GAAQ3K,GAAKoL,GAASz8B,GAG3Dy8B,EAAM93B,OAAS,SAAU0sB,GACvB,OAAOgE,UAAU/0B,QAAWqE,EAASs3B,GAAQ5K,IAAMA,EAAIoL,GAAS93B,GAGlE83B,EAAMj+B,SAAW,SAAU6yB,GACzB,OAAOgE,UAAU/0B,QAAW9B,EAAWw9B,GAAQ3K,GAAKoL,GAASj+B,GAG/Di+B,EAAMt+B,QAAU,SAAUkzB,GACxB,OAAOgE,UAAU/0B,QAAWnC,EAAU69B,GAAQ3K,GAAKoL,GAASt+B,GAG9Ds+B,EAAMjO,OAAS,SAAU6C,GACvB,OAAOgE,UAAU/0B,QAAWkuB,EAAS6C,EAAIoL,GAASjO,GAG7CiO,EC9bM,SAASoB,KACtB,IAAIppB,KAAEA,EAAIQ,UAAEA,EAASqG,UAAEA,GAAcze,MACjCihC,QAAEA,EAAO56B,MAAEA,EAAKC,OAAEA,GAAWsR,EAC7BspB,EAAgBzpB,EAAUgH,EAAoB,SAAE,IAEpD,MAAM0iB,EAASx5B,KACfw5B,EAAOxB,OAAOuB,EAAcvB,QAAUsB,GACtCE,EAAO/7B,KAAK87B,EAAc97B,MAAQ,CAACiB,EAAOC,IAC1C66B,EAAOzB,MAAMwB,EAAchgC,MAC3BigC,EAAOnjB,KAWP,SAAa9c,GACXggC,EAAchgC,KAAOA,EACrBkX,EAAU8oB,cAAgBA,KAZxBA,EAAcr5B,MAAMs5B,EAAOt5B,KAAKq5B,EAAcr5B,MAC9Cq5B,EAAcv/B,UAAUw/B,EAAOx/B,SAASu/B,EAAcv/B,UACtDu/B,EAAc5/B,SAAS6/B,EAAO7/B,QAAQ4/B,EAAc5/B,SACpD4/B,EAAcj9B,QAAQk9B,EAAOl9B,OAAOi9B,EAAcj9B,QAClDi9B,EAAcp5B,QAAQq5B,EAAOr5B,OAAOo5B,EAAcp5B,QAClDo5B,EAAct5B,cAAcu5B,EAAOv5B,aAAas5B,EAAct5B,cAC9Ds5B,EAAcE,WAAWD,EAAOV,WAAWS,EAAcE,WAE7DD,EAAOpjB,QAOsC3F,EAAU8oB,cCV1C,SAASG,KAEtB3pB,EAAWjM,KAAKzL,MAGhBwe,EAAa/S,KAAKzL,MAClB2e,EAAelT,KAAKzL,MACpB8e,EAAcrT,KAAKzL,OAGfA,KAAKye,UAAUzZ,MAAQhF,KAAKye,UAAU9Z,KAAO3E,KAAKye,UAAUvY,SAAWlG,KAAKye,UAAUxX,aAAejH,KAAKye,UAAUjX,UACtHuY,EAAYtU,KAAKzL,MAEnBuL,OAAOoD,KAAK3O,KAAKye,WAAWne,QAAQJ,IAClC,GAAIF,KAAKye,UAAUve,GACjB,OAAQA,GACN,IAAK,MACHqpB,EAAgB9d,KAAKzL,MACrB,MACF,IAAK,OACH4qB,EAAiBnf,KAAKzL,MACtB,MACF,IAAK,MACH+qB,EAAgBtf,KAAKzL,MACrB,MACF,IAAK,QACH0oB,EAAiBjd,KAAKzL,MACtBqrB,EAAkB5f,KAAKzL,MACvB,MACF,IAAK,UACHwrB,EAAoB/f,KAAKzL,MACzB,MACF,IAAK,SACHshC,EAAmB71B,KAAKzL,MACxB,MACF,IAAK,cACHuhC,EAAwB91B,KAAKzL,MAC7B,MACF,IAAK,UACHkvB,EAAoBzjB,KAAKzL,MACzB,MACF,IAAK,UACHu7B,GAAoB9vB,KAAKzL,MACzB,MACF,IAAK,WACHghC,GAAqBv1B,KAAKzL,SC/DpC,OAAe,CACbwhC,OAAQ,SAAUC,GAChB,OAAO73B,KAAK83B,IAAID,EAAK,IAEvBE,QAAS,SAAUF,GACjB,OAAO73B,KAAK83B,IAAID,EAAM,EAAG,GAAK,GAEhCG,UAAW,SAAUH,GACnB,OAAKA,GAAO,IAAO,EACV,GAAM73B,KAAK83B,IAAID,EAAK,GAEpB,IAAO73B,KAAK83B,IAAID,EAAM,EAAG,GAAK,IAGzCI,OAAQ,SAAUJ,GAChB,OAAOA,ICbI,MAAMK,GACnB/hC,YAAY6X,GACV5X,KAAK+hC,QAAS,EAEd,IAAIphC,UAAEA,EAASC,kBAAEA,EAAiBC,gBAAEA,EAAemhC,UAAEA,EAASC,kBAAEA,GAAsBrqB,EAclFsqB,EAXmC,oBAA1BC,sBACFA,sBACwB,oBAAfC,WACT,SAAU5B,GACf4B,YAAW,WACT,IAAIC,GAAa,IAAIlC,KACrBK,EAAK6B,KACJ,UALA,EAWT,GAAI1hC,EAAW,CACb,IAAI2hC,EAAiBC,GAAO1hC,GACxB2hC,EAAiB,KAEjBhC,EAAO,WACT,IAAoB,IAAhBxgC,KAAK+hC,OAGP,OAFAC,EAAU,QACVC,IAIF,IAAII,GAAa,IAAIlC,KAGrB,GAFKqC,IAAgBA,EAAiBH,GAElCA,EAAYG,EAAiB5hC,EAAmB,CAClD,IAAI6hC,GAAWJ,EAAYG,GAAkB5hC,EAC7C6hC,EAAUH,EAAeG,GACzB7qB,EAAKoqB,UAAUS,GACfP,EAAe1B,QAEfwB,EAAU,GACVC,KAGJzB,EAAOA,EAAKkC,KAAK1iC,MAEjBkiC,EAAe1B,QAEfwB,EAAU,GACVC,IAOJliC,OACEC,KAAK+hC,QAAS,GC3DH,SAASY,KACtB,MAAM36B,QAAEA,EAAO4P,KAAEA,EAAI6B,YAAEA,GAAgBzZ,MACjC+X,MAAEA,EAAKC,MAAEA,EAAKhX,QAAEA,GAAY4W,GAC5B1W,KAAEA,EAAIyb,gBAAEA,EAAe9C,SAAEA,EAAQC,SAAEA,EAAQC,aAAEA,EAAYC,cAAEA,GAAkBP,GAC7ExY,KAAEA,EAAIa,YAAEA,EAAWhB,gBAAEA,EAAeM,iBAAEA,EAAgBC,kBAAEA,EAAiBC,QAAEA,EAAOC,QAAEA,EAAOC,WAAEA,EAAUC,QAAEA,EAAOC,UAAEA,GAAcV,GAC9HW,SAAUihC,EAAchhC,MAAOihC,EAAWhhC,WAAYihC,GAAmBphC,GACzExB,KAAM6iC,EAAiBhhC,UAAWihC,EAAsB9gC,YAAa+gC,EAAwB9gC,MAAO+gC,GAAqBphC,GACzHqhC,UAAWC,EAAsB/gC,SAAUghC,EAAqBzhC,MAAO0hC,EAAsBrhC,QAASshC,GAA2BP,GACjIphC,MAAO4hC,EAAwBvhC,QAASwhC,GAA6BR,GAE3EhiC,KAAMyiC,EACN1hC,UAAW2hC,EACXthC,SAAUuhC,EACVxhC,UAAWyhC,EACXvhC,YAAawhC,EACbhjC,gBAAiBijC,EACjB1iC,kBAAmB2iC,EACnBzhC,UAAW0hC,EACXzhC,YAAa4X,GACX8oB,EAEJ,IAAKjiC,GAAuB,GAAfC,EAAKuC,OAAa,OAG/B,GAAIkZ,EAAiB,CACnB,MAAM1B,aAAEA,EAAYC,aAAEA,EAAYF,aAAEA,GAAiB2B,GAC/CxB,WACJA,EAAUK,gBACVA,EAAeE,iBACfA,EAAgBL,YAChBA,EAAWQ,YACXA,EAAWC,YACXA,EAAWC,YACXA,EAAWC,YACXA,EAAWC,YACXA,EAAWC,WACXA,EAAUP,gBACVA,EAAeC,iBACfA,EAAgBO,YAChBA,EAAWC,YACXA,EAAWC,YACXA,EAAWC,YACXA,EAAWC,YACXA,EAAWC,YACXA,GACExB,EAEJ,GAAI0oB,EAAW,CACb,MAAMpoB,EAAqBvD,EAAMzU,UAAU5B,UAAUC,SAC/C4Z,EAAqBvD,EAAM1U,UAAU5B,UAAUC,SAGrDqG,EAAQ80B,OACR90B,EAAQhG,UAAY2hC,EACpB37B,EAAQk8B,YAAYN,GACpB57B,EAAQg1B,YAAc6G,EACtB77B,EAAQm8B,YAAcL,EACtB97B,EAAQo8B,YACRp8B,EAAQq8B,OAAOvoB,EAAaC,GAC5B/T,EAAQs8B,OAAOtoB,EAAaC,GAC5BjU,EAAQu8B,SACRv8B,EAAQ61B,UAGR71B,EAAQ80B,OACR90B,EAAQhG,UAAY2hC,EACpB37B,EAAQk8B,YAAYN,GACpB57B,EAAQg1B,YAAc6G,EACtB77B,EAAQm8B,YAAcL,EACtB97B,EAAQo8B,YACRp8B,EAAQq8B,OAAOhoB,EAAaC,GAC5BtU,EAAQs8B,OAAO/nB,EAAaC,GAC5BxU,EAAQu8B,SACRv8B,EAAQ61B,UAGR71B,EAAQ80B,OACR90B,EAAQ+0B,UAAYgH,EACpB/7B,EAAQm8B,YAAcH,EACtBh8B,EAAQw8B,SAASnpB,EAAcjB,EAAkByB,EAAcP,EAAqB,EAAIlB,GAAmBoB,EAAiBE,GAC5H1T,EAAQ61B,UAGR71B,EAAQ80B,OACR90B,EAAQ+0B,UAAYgH,EACpB/7B,EAAQm8B,YAAcH,EACtBh8B,EAAQw8B,SAASroB,EAAcR,EAAkB,EAAGS,EAAchC,EAAkBuB,EAAiBC,GACrG5T,EAAQ61B,UAGR71B,EAAQ80B,OACR90B,EAAQ+0B,UAAYkH,EACpBj8B,EAAQH,KAAUyT,EAAH,KACftT,EAAQy8B,aAAe,SACvBz8B,EAAQi1B,UAAY,QACpBj1B,EAAQ21B,SAASxiB,EAAYE,EAAaQ,GAC1C7T,EAAQ61B,UAGR71B,EAAQ80B,OACR90B,EAAQ+0B,UAAYkH,EACpBj8B,EAAQH,KAAU0T,EAAH,KACfvT,EAAQy8B,aAAe,MACvBz8B,EAAQi1B,UAAY,SACpBj1B,EAAQ21B,SAASzhB,EAAYC,EAAaC,GAC1CpU,EAAQ61B,UAGV,GAAuB,QAAnBkF,GACF,GAAI9nB,EAAc,CAChB,MAAM1S,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,GAAOuS,EAE3BjT,EAAQ80B,OACR90B,EAAQm8B,YAAcZ,EAClBF,GACFr7B,EAAQk8B,YAAYb,GAEtBr7B,EAAQhG,UAAYohC,EACpBp7B,EAAQg1B,YAAcsG,EACtBt7B,EAAQo8B,YACRp8B,EAAQq8B,OAAO97B,EAAIC,GACnBR,EAAQs8B,OAAO77B,EAAIC,GACnBV,EAAQu8B,SACRv8B,EAAQ61B,gBAKL,GAAuB,UAAnBkF,GACL9nB,EAAc,CAChB,MAAM1Q,EAAEA,EAACM,EAAEA,EAACxE,MAAEA,EAAKC,OAAEA,GAAW2U,EAEhCjT,EAAQ80B,OACR90B,EAAQm8B,YAAcV,EACtBz7B,EAAQ+0B,UAAYyG,EACpBx7B,EAAQw8B,SAASj6B,EAAGM,EAAGxE,EAAOC,GAC9B0B,EAAQ61B,WAQd38B,EAAKZ,QAAQyI,IACX,GAAiB,QAAbA,EAAK7I,MAA+B,SAAb6I,EAAK7I,KAAiB,CAE/C,MAAMqK,EAAEA,EAACM,EAAEA,EAACjJ,MAAEA,EAAKmb,WAAEA,EAAUC,WAAEA,EAAUC,YAAEA,GAAgBlU,EAE7D,OAAQgU,GACN,IAAK,SACH/U,EAAQo8B,YACRp8B,EAAQ+0B,UAA2B,QAAf9f,EAAwBrb,EAAQqb,EACpDjV,EAAQ08B,IAAIn6B,EAAGM,GAAImS,EAAa,GAAK,EAAG,EAAG,EAAIpT,KAAKqP,IACpDjR,EAAQ28B,OACR38B,EAAQo8B,YACRp8B,EAAQ+0B,UAAY,UACpB/0B,EAAQ08B,IAAIn6B,EAAGM,GAAImS,EAAa,GAAK,EAAG,EAAG,EAAIpT,KAAKqP,IACpDjR,EAAQ28B,OACR38B,EAAQ61B,WAKd,GAAiB,OAAb90B,EAAK7I,KAAe,CAEtB,MAAM0B,MAAEA,EAAKyC,OAAEA,EAAMC,OAAEA,EAAM4U,QAAEA,EAAOwE,MAAEA,GAAU3U,GAC3C4B,EAASC,GAAWvG,GACpBunB,EAAWD,GAAarnB,EAE/B0D,EAAQ80B,OACR90B,EAAQo8B,YACRp8B,EAAQq8B,OAAO15B,EAASC,GACxB5C,EAAQ+0B,UAAYn7B,EACpBoG,EAAQ08B,IAAI/5B,EAASC,EAAS+gB,EAAY,EAAGzS,EAASwE,GACtD1V,EAAQ28B,OACJ/Y,EAAY,IACd5jB,EAAQo8B,YACRp8B,EAAQq8B,OAAO15B,EAASC,GACxB5C,EAAQ+0B,UAAY/8B,KAAK4X,KAAK9W,gBAC9BkH,EAAQ08B,IAAI/5B,EAASC,EAASghB,EAAW1S,EAASwE,GAClD1V,EAAQ28B,QAEV38B,EAAQ61B,aAiFd,SAAwB71B,EAASuC,EAAGM,EAAGxE,EAAOC,EAAQhC,EAAQxD,EAAkB,UAAWmB,EAAU,IAiBnG+F,EAAQ80B,OACR90B,EAAQ+0B,UAAYj8B,EACpBkH,EAAQm8B,YAAcliC,EACtB+F,EAAQo8B,YACRp8B,EAAQq8B,OAAO95B,EAAIjG,EAAQuG,GAC3B7C,EAAQs8B,OAAO/5B,EAAIlE,EAAQ/B,EAAQuG,GACnC7C,EAAQ08B,IAAIn6B,EAAIlE,EAAQ/B,EAAS,GAAKuG,EAAIvG,EAAS,GAAKA,GAASsF,KAAKqP,GAAK,EAAG,GAC9EjR,EAAQs8B,OAAO/5B,EAAIlE,EAAOwE,EAAIvE,EAAShC,GACvC0D,EAAQ08B,IAAIn6B,EAAIlE,EAAQ/B,EAAS,GAAKuG,EAAIvE,EAAShC,EAAS,GAAKA,EAAQ,EAAGsF,KAAKqP,GAAK,GACtFjR,EAAQs8B,OAAO/5B,EAAIjG,EAAQuG,EAAIvE,GAC/B0B,EAAQ08B,IAAIn6B,EAAIjG,EAAS,GAAKuG,EAAIvE,EAAShC,EAAS,GAAKA,EAAQsF,KAAKqP,GAAK,EAAGrP,KAAKqP,IACnFjR,EAAQs8B,OAAO/5B,EAAGM,EAAIvG,GACtB0D,EAAQ08B,IAAIn6B,EAAIjG,EAAS,GAAKuG,EAAIvG,EAAS,GAAKA,EAAQsF,KAAKqP,GAAe,EAAVrP,KAAKqP,GAAU,GACjFjR,EAAQ28B,OACR38B,EAAQ61B,UA3GR+G,CAAe58B,EAAS6R,EAAUC,EAAUC,EAAcC,EAAe5Y,EAAkBN,EAAiBO,GAG5G,IAAIwjC,EAAYhrB,EAAWvY,EAAUE,EACjCsjC,EAAYhrB,EAAWxY,EAAUwhC,EAAiB,EAElDrpB,EAAYC,eAEd1R,EAAQ80B,OACR90B,EAAQ+0B,UAAY8F,EACpB76B,EAAQH,KAAU+6B,EAAH,KACf56B,EAAQy8B,aAAe,SACvBz8B,EAAQi1B,UAAY,OACpBj1B,EAAQ21B,SAASlkB,EAAYC,aAAcmrB,EAAYrjC,EAAYsjC,GACnE98B,EAAQ80B,OAERgI,GAAahC,EAAiBvhC,GAGhCL,EAAKZ,QAAQyI,IACX,GAAiB,eAAbA,EAAK7I,MAAsC,KAAb6I,EAAK7I,KAAa,CAClD,MAAM4c,KAAEA,EAAIiB,MAAEA,EAAKC,IAAEA,EAAGE,KAAEA,EAAID,IAAEA,EAAG/D,OAAEA,EAAMtY,MAAEA,GAAUmH,EAEvDf,EAAQ80B,OACR90B,EAAQo8B,YACRp8B,EAAQ+0B,UAAYn7B,EACpBoG,EAAQ08B,IAAIG,EAAWC,EAAWtjC,EAAY,EAAG,EAAIoI,KAAKqP,IAC1DjR,EAAQ28B,OACRE,GAAarjC,EAAaC,EAE1BuG,EAAQo8B,YACRp8B,EAAQ+0B,UAAY8F,EACpB76B,EAAQH,KAAU+6B,EAAH,KACf56B,EAAQy8B,aAAe,SACvBz8B,EAAQi1B,UAAY,OACpBj1B,EAAQ21B,SAAS7gB,EAAM+nB,EAAWC,GAClCA,GAAahC,EAAiBvhC,EAC9ByG,EAAQ21B,SAAS5f,EAAO8mB,EAAWC,GACnCA,GAAahC,EAAiBvhC,EAC9ByG,EAAQ21B,SAAS3f,EAAK6mB,EAAWC,GACjCA,GAAahC,EAAiBvhC,EAC9ByG,EAAQ21B,SAAS1f,EAAK4mB,EAAWC,GACjCA,GAAahC,EAAiBvhC,EAC9ByG,EAAQ21B,SAASzf,EAAM2mB,EAAWC,GAClCA,GAAahC,EAAiBvhC,EAC1B2Y,IACFlS,EAAQ21B,SAASzjB,EAAQ2qB,EAAWC,GACpCA,GAAahC,EAAiBvhC,GAEhCyG,EAAQ61B,UACRgH,EAAYhrB,EAAWvY,EAAUE,MAC5B,CACL,MAAM2B,KAAEA,EAAIvB,MAAEA,GAAUmH,EAExBf,EAAQ80B,OACR90B,EAAQo8B,YACRp8B,EAAQ+0B,UAAYn7B,EACpBoG,EAAQ08B,IAAIG,EAAWC,EAAWtjC,EAAY,EAAG,EAAIoI,KAAKqP,IAC1DjR,EAAQ28B,OACRE,GAAarjC,EAAaC,EAC1BuG,EAAQo8B,YACRp8B,EAAQ+0B,UAAY8F,EACpB76B,EAAQH,KAAU+6B,EAAH,KACf56B,EAAQy8B,aAAe,SACvBz8B,EAAQi1B,UAAY,OACpBj1B,EAAQ21B,SAASx6B,EAAM0hC,EAAWC,GAClC98B,EAAQ61B,UAERgH,EAAYhrB,EAAWvY,EAAUE,EACjCsjC,GAAahC,EAAiBvhC,KC7PpC,SAAwBwjC,GAAe/c,EAAS,EAAGC,EAAS,EAAGC,EAAOloB,KAAK4X,KAAKvR,MAAO8hB,EAAOnoB,KAAK4X,KAAKtR,QACtGtG,KAAKgI,QAAQ40B,UAAU5U,EAAQC,EAAQC,EAAMC,GAC7CnoB,KAAKgI,QAAQ+0B,UAAY/8B,KAAK4X,KAAK9W,gBACnCd,KAAKgI,QAAQw8B,SAASxc,EAAQC,EAAQC,EAAMC,GCD9C,SAAwB6c,KACtB,IAAKhlC,KAAK4X,KAAKjV,OAAO1B,KAAM,OAE5B,IAOIgkC,GAPAj9B,QAAEA,EAAO4P,KAAEA,EAAIoH,WAAEA,GAAehf,MAChCqG,MAAEA,EAAKC,OAAEA,EAAM3D,OAAEA,EAAMrB,QAAEA,GAAYsW,GACrC9U,WAAEA,EAAUC,YAAEA,EAAWF,YAAEA,EAAWtB,QAAEA,EAAOqB,UAAEA,EAASlB,UAAEA,GAAciB,GAC1EhB,SAAEA,EAAQC,MAAEA,EAAON,QAAS2d,GAAgBvd,GAC5C0d,WAAEA,EAAUF,YAAEA,EAAWY,aAAEA,GAAiBd,EAC5CiJ,EAAS3hB,EAAShF,EAAQ,GAAKwe,EAAeld,EAC9ColB,EAAS1mB,EAAQ,IAAM+E,EAAQ/E,EAAQ,GAAKA,EAAQ,GAAK4d,GAAe,EAG5EE,EAAW9e,QAAQ,CAAC4kC,EAAUC,KAC5Bnd,EAAS1mB,EAAQ,IAAM+E,EAAQ/E,EAAQ,GAAKA,EAAQ,GAAK4d,GAAe,EAExEgmB,EAAS5kC,QAAQ8kC,IACf,IAAIrmB,WAAEA,EAAUnd,MAAEA,EAAKkb,KAAEA,EAAIrB,YAAEA,GAAgB2pB,EAC/C,OAAQrmB,GACN,IAAK,SACHkmB,EAAkBr7B,KAAK/F,IAAkB,EAAdhB,EAAiBlB,GAC5CqG,EAAQo8B,YACRp8B,EAAQq8B,OAAOrc,EAASnlB,EAAaolB,EAASgd,EAAkB,GAChEj9B,EAAQ08B,IAAI1c,EAASnlB,EAAaolB,EAASgd,EAAkB,EAAGpiC,EAAa,EAAG,EAAI+G,KAAKqP,IACzFjR,EAAQq9B,YAERr9B,EAAQ+0B,UAAYn7B,EACpBoG,EAAQ28B,OAER3c,GAAwB,EAAdnlB,EAAkBoc,EAC5B,MACF,IAAK,OACHgmB,EAAkBr7B,KAAK/F,IAAId,EAAapB,GACxC,IAAI2jC,GAAcxiC,EAAaC,GAAe,EAG9CiF,EAAQo8B,YACRp8B,EAAQq8B,OAAOrc,EAAQC,EAASgd,EAAkB,GAClDj9B,EAAQs8B,OAAOtc,EAASsd,EAAa,EAAGrd,EAASgd,EAAkB,GACnEj9B,EAAQq9B,YACRr9B,EAAQhG,UAAY,EACpBgG,EAAQg1B,YAAcp7B,EACtBoG,EAAQu8B,SAERv8B,EAAQo8B,YACRp8B,EAAQq8B,OAAOrc,EAASllB,EAAa,EAAGmlB,EAASgd,EAAkB,GACnEj9B,EAAQ08B,IAAI1c,EAASllB,EAAa,EAAGmlB,EAASgd,EAAkB,EAAGliC,EAAc,EAAG,EAAG,EAAI6G,KAAKqP,IAChGjR,EAAQq9B,YACRr9B,EAAQ+0B,UAAYn7B,EACpBoG,EAAQ28B,OAER38B,EAAQo8B,YACRp8B,EAAQq8B,OAAOrc,EAASsd,EAAaviC,EAAc,EAAGklB,EAASgd,EAAkB,GACjFj9B,EAAQs8B,OAAOtc,EAASllB,EAAYmlB,EAASgd,EAAkB,GAC/Dj9B,EAAQq9B,YACRr9B,EAAQhG,UAAY,EACpBgG,EAAQg1B,YAAcp7B,EACtBoG,EAAQu8B,SAERvc,GAAUllB,EAAamc,EACvB,MACF,IAAK,OACHgmB,EAAkBr7B,KAAK/F,IAAId,EAAapB,GACxCqG,EAAQ+0B,UAAYh1B,EAClBnG,EACAoG,EACAggB,EACAC,EAASgd,EAAkB,EAAIliC,EAAc,EAC7CilB,EAASllB,EACTmlB,EAASgd,EAAkB,EAAIliC,EAAc,GAE/CiF,EAAQw8B,SAASxc,EAAQC,EAASgd,EAAkB,EAAIliC,EAAc,EAAGD,EAAYC,GAErFilB,GAAUllB,EAAamc,EAI3BjX,EAAQ80B,OACR90B,EAAQi1B,UAAY,OACpBj1B,EAAQy8B,aAAe,SACvBz8B,EAAQH,KAAUlG,EAAH,KACfqG,EAAQ+0B,UAAYn7B,EACpBoG,EAAQ21B,SAAS7gB,EAAMkL,EAAQC,EAASgd,EAAkB,GAC1Dj9B,EAAQ61B,UAER7V,GAAUvM,EAAcla,IAG1B0mB,GAAUgd,EAAkB1jC,IC1FhC,SAAwBgkC,KACtB,IAAIv9B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC/BgY,MAAEA,EAAKD,MAAEA,GAAUH,GAGrB3W,KAAM+e,EACN9f,KAAM+f,EACN/c,SAAUqd,EACVjd,UAAW4Y,EACX3Y,SAAUid,EACV9c,SAAU+c,EACV9c,cAAe+c,GACb1I,GAEF/W,KAAM0f,EACNzgB,KAAM0gB,EACN1d,SAAUge,EACV5d,UAAW6X,EACX5X,SAAU4d,EACVzd,SAAU0d,EACVzd,cAAe0d,GACbtJ,GAEE9W,KAAMqgB,EAAe5f,UAAW6f,GAAuBhB,GACvDtf,KAAMygB,EAAgBhgB,UAAWigB,EAAqB1d,OAAQ4d,GAAqB3F,GACnFjb,KAAMghB,EAAelgB,UAAWmgB,GAAmB1B,GACnDvf,KAAMqhB,EAAevgB,UAAWwgB,GAAmB9B,GACnDxf,KAAMuhB,EAAoBzgB,UAAW0gB,GAAwB/B,GAE7Dzf,KAAM2hB,EAAelhB,UAAWmhB,GAAuB3B,GACvDjgB,KAAM+hB,EAAgBthB,UAAWuhB,GAAwB9H,GACzDla,KAAMoiB,EAAethB,UAAWuhB,GAAmBnC,GACnDlgB,KAAMyiB,EAAe3hB,UAAW4hB,GAAmBvC,GACnDngB,KAAM2iB,EAAoB7hB,UAAW8hB,GAAwBxC,GAE7Dzf,MAAO4jC,EAAgB7jC,SAAUqiB,GAAsBzC,GACvD3f,MAAO6jC,EAAiB9jC,SAAU4Z,GAAuBoG,GACzD/f,MAAO8jC,EAAoB1jC,UAAWiiB,GAAuB/B,GAC7DtgB,MAAO+jC,EAAgB3jC,UAAWkiB,GAAmB3B,GACrD3gB,MAAOgkC,EAAqB5jC,UAAWmiB,GAAwB1B,GAE/D7gB,MAAOikC,EAAgBlkC,SAAUyiB,GAAsBvB,GACvDjhB,MAAOkkC,EAAiBnkC,SAAU2Z,GAAuB2H,GACzDrhB,MAAOmkC,GAAoB/jC,UAAWqiB,IAAuBf,GAC7D1hB,MAAOokC,GAAgBhkC,UAAWsiB,IAAmBX,GACrD/hB,MAAOqkC,GAAqBjkC,UAAWuiB,IAAwBV,GAEjExL,gBACFA,GAAeqO,eACfA,GAAcC,eACdA,GAAcC,oBACdA,GAAmBC,eACnBA,GAAcvO,gBACdA,GAAewO,eACfA,GAAcC,eACdA,GAAcC,oBACdA,GAAmBC,eACnBA,IACE7O,EAAUG,SAEVoI,IACEqC,IACFhb,EAAQ80B,OACR90B,EAAQH,KAAUyT,EAAH,KACftT,EAAQ+0B,UAAY+I,EACpB99B,EAAQi1B,UAAY,QACpBj1B,EAAQy8B,aAAe,SACvBnsB,GAAgBhY,QAAQyI,KACL,SAAb6X,GAAwB7X,EAAK9H,OAC/B+G,EAAQ21B,SAAS50B,EAAK5F,KAAM4F,EAAKwB,EAAGxB,EAAK8B,KAG7C7C,EAAQ61B,WAGNja,IACF5b,EAAQhG,UAAYuiB,GACpBvc,EAAQg1B,YAAciJ,GAEtBjf,GAAoB1mB,QAAQ,CAACyI,EAAM4E,MAChB,SAAbiT,GAAwB7X,EAAK9H,QAC/B+G,EAAQo8B,YACRp8B,EAAQq8B,OAAOt7B,EAAKif,OAAQjf,EAAKkf,QACjCjgB,EAAQs8B,OAAOv7B,EAAKmf,KAAMnf,EAAKof,MAC/BngB,EAAQq9B,YACRr9B,EAAQu8B,aAKV3hB,IACF5a,EAAQ80B,OACR90B,EAAQH,KAAUuc,EAAH,KACfpc,EAAQ+0B,UAAY8I,EACpB79B,EAAQi1B,UAAY,SACpBj1B,EAAQy8B,aAAe,SACvBz8B,EAAQ21B,SAAS1W,GAAe9jB,KAAM8jB,GAAe1c,EAAG0c,GAAepc,GACvE7C,EAAQ61B,YAIR7d,IACE0B,IACF1Z,EAAQ80B,OACR90B,EAAQH,KAAU0T,EAAH,KACfvT,EAAQ+0B,UAAY0I,EACpBz9B,EAAQy8B,aAAe,MAEC,GAApB5iB,EACF7Z,EAAQi1B,UAAY,SACXpb,EAAmB,EAC5B7Z,EAAQi1B,UAAY,QACXpb,EAAmB,IAC5B7Z,EAAQi1B,UAAY,QAGtB5kB,GAAgB/X,QAAQyI,KACL,SAAbkX,GAAwBlX,EAAK9H,QACP,GAApB4gB,EACF7Z,EAAQ21B,SAAS50B,EAAK5F,KAAM4F,EAAKwB,EAAGxB,EAAK8B,IAEzC7C,EAAQ80B,OACR90B,EAAQ01B,UAAU30B,EAAKwB,EAAGxB,EAAK8B,GAC/B7C,EAAQ/D,QAAS4d,EAAmBjY,KAAKqP,GAAM,KAC/CjR,EAAQ21B,SAAS50B,EAAK5F,KAAM,EAAG,GAC/B6E,EAAQ61B,cAKd71B,EAAQ61B,WAGNrb,IACFxa,EAAQhG,UAAYmiB,EACpBnc,EAAQg1B,YAAc4I,EAEtBhf,GAAoBtmB,QAAQ,CAACyI,EAAM4E,MAChB,SAAbsS,GAAwBlX,EAAK9H,QAC/B+G,EAAQo8B,YACRp8B,EAAQq8B,OAAOt7B,EAAKif,OAAQjf,EAAKkf,QACjCjgB,EAAQs8B,OAAOv7B,EAAKmf,KAAMnf,EAAKof,MAC/BngB,EAAQq9B,YACRr9B,EAAQu8B,aAKVjjB,IACFtZ,EAAQ80B,OACR90B,EAAQH,KAAUmc,EAAH,KACfhc,EAAQ+0B,UAAYyI,EACpBx9B,EAAQi1B,UAAY,OACpBj1B,EAAQy8B,aAAe,SACvBz8B,EAAQ21B,SAAS9W,GAAe1jB,KAAM0jB,GAAetc,EAAGsc,GAAehc,GACvE7C,EAAQ61B,YAKRld,IACE0C,IACFrb,EAAQhG,UAAYqiB,GACpBrc,EAAQg1B,YAAc+I,GAEtBjf,GAAexmB,QAAQyI,KACJ,SAAb6X,GAAwB7X,EAAK9H,QAC/B+G,EAAQo8B,YACRp8B,EAAQq8B,OAAOt7B,EAAKif,OAAQjf,EAAKkf,QACjCjgB,EAAQs8B,OAAOv7B,EAAKmf,KAAMnf,EAAKof,MAC/BngB,EAAQq9B,YACRr9B,EAAQu8B,aAKV7gB,IACF1b,EAAQo8B,YACRp8B,EAAQq8B,OAAOtd,GAAeiB,OAAQjB,GAAekB,QACrDjgB,EAAQs8B,OAAOvd,GAAemB,KAAMnB,GAAeoB,MACnDngB,EAAQq9B,YAERr9B,EAAQhG,UAAYsiB,GACpBtc,EAAQg1B,YAAcgJ,GACtBh+B,EAAQu8B,WAIRvkB,IACEiC,IACFja,EAAQhG,UAAYiiB,EACpBjc,EAAQg1B,YAAc0I,EAEtBhf,GAAepmB,QAAQyI,KACJ,SAAbkX,GAAwBlX,EAAK9H,QAC/B+G,EAAQo8B,YACRp8B,EAAQq8B,OAAOt7B,EAAKif,OAAQjf,EAAKkf,QACjCjgB,EAAQs8B,OAAOv7B,EAAKmf,KAAMnf,EAAKof,MAC/BngB,EAAQq9B,YACRr9B,EAAQu8B,aAKVjiB,IACFta,EAAQo8B,YACRp8B,EAAQq8B,OAAO1d,GAAeqB,OAAQrB,GAAesB,QACrDjgB,EAAQs8B,OAAO3d,GAAeuB,KAAMvB,GAAewB,MACnDngB,EAAQq9B,YAERr9B,EAAQhG,UAAYkiB,EACpBlc,EAAQg1B,YAAc2I,EACtB39B,EAAQu8B,WCvNC,SAAS2B,KACtB,IAAIl+B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC/Bc,gBAAEA,EAAeqD,UAAEA,EAASwkB,WAAEA,GAAe/Q,GAE/CxT,MAAO+hC,EAAcpiC,YACrBA,EACAb,SAAU0lB,EACVllB,SAAU0iC,EACV7hC,UAAW8hC,EACX7hC,UAAW8hC,GACTniC,GACElD,KAAM4nB,EAAmBnnB,UAAWonB,GAA2BF,GAC/D3nB,KAAMslC,EAAmBxkC,UAAWykC,GAAuBJ,GAC3DnlC,KAAMwlC,EAAwB1kC,UAAW2kC,GAA4BL,GAErEzkC,MAAO+kC,EAAoBhlC,SAAUonB,GAA0BD,GAC/DlnB,MAAOglC,EAAoB5kC,UAAW6kC,GAAuBL,GAC7D5kC,MAAOklC,EAAyB9kC,UAAW+kC,GAA4BL,GAEzEjiC,IAAEA,EAAGC,KAAEA,GAAS4hC,GACdrlC,KAAM+lC,EAAkBplC,MAAOqlC,EAAmBhlC,QAASilC,GAAwBziC,GACnFxD,KAAMkmC,EAAmBvlC,MAAOwlC,EAAoBnlC,QAASolC,GAAyB3iC,GAExFL,OAAEA,EAAMC,OAAEA,EAAM2kB,gBAAEA,EAAerL,aAAEA,GAAiBxF,EAAUjU,WAC7DwG,EAASC,GAAWvG,EAEzB,GAAsB,WAAlB8hC,EACFld,EAAgB3oB,QAAQ,CAACgnC,EAAkBC,KACzC,IAAIC,GAAazjC,EAAcwjC,GAAc,EAE7Cv/B,EAAQo8B,YACRkD,EAAiBhnC,QAAQ,CAACmnC,EAAmBC,KACjB,GAAtBA,EACF1/B,EAAQq8B,OAAOoD,EAAkBl9B,EAAGk9B,EAAkB58B,GAEtD7C,EAAQs8B,OAAOmD,EAAkBl9B,EAAGk9B,EAAkB58B,KAG1D7C,EAAQq9B,YAGU,IAAdmC,GAAmBR,IACrBh/B,EAAQ+0B,UAAYj8B,EACpBkH,EAAQ28B,OACR38B,EAAQ80B,OACR90B,EAAQm8B,YAAckD,EACtBr/B,EAAQ+0B,UAAYqK,EACpBp/B,EAAQ28B,OACR38B,EAAQ61B,WAIQ,IAAd2J,GAAmBL,IACrBn/B,EAAQ+0B,UAAYj8B,EACpBkH,EAAQ28B,OACR38B,EAAQ80B,OACR90B,EAAQm8B,YAAc+C,EACtBl/B,EAAQ+0B,UAAiC,QAArBkK,EAA8BnmC,EAAkBmmC,EACpEj/B,EAAQ28B,OACR38B,EAAQ61B,WAIN4I,IACFz+B,EAAQhG,UAAY+kC,EACpB/+B,EAAQg1B,YAAc8J,EACtB9+B,EAAQu8B,iBAIZ,IAAK,IAAI52B,EAAQ,EAAGA,EAAQ5J,EAAa4J,IAAS,CAChD,IAAIyb,GAASrlB,EAAc4J,GAAS5J,EAChCyjC,GAAazjC,EAAc4J,GAAS,EAExC3F,EAAQo8B,YACRp8B,EAAQ08B,IAAI/5B,EAASC,EAAStG,EAAS8kB,EAAO,EAAa,EAAVxf,KAAKqP,IAGpC,IAAduuB,GAAmBR,IACrBh/B,EAAQ+0B,UAAYj8B,EACpBkH,EAAQ28B,OACR38B,EAAQ80B,OACR90B,EAAQm8B,YAAckD,EACtBr/B,EAAQ+0B,UAAYqK,EACpBp/B,EAAQ28B,OACR38B,EAAQ61B,WAIQ,IAAd2J,GAAmBL,IACrBn/B,EAAQ+0B,UAAYj8B,EACpBkH,EAAQ28B,OACR38B,EAAQ80B,OACR90B,EAAQm8B,YAAc+C,EACtBl/B,EAAQ+0B,UAAiC,QAArBkK,EAA8BnmC,EAAkBmmC,EACpEj/B,EAAQ28B,OACR38B,EAAQ61B,WAIN4I,IACFz+B,EAAQhG,UAAY+kC,EACpB/+B,EAAQg1B,YAAc8J,EACtB9+B,EAAQu8B,UAMVgC,GACFtd,EAAgB,GAAG3oB,QAAQqnC,IACzB3/B,EAAQo8B,YACRp8B,EAAQq8B,OAAO15B,EAASC,GACxB5C,EAAQs8B,OAAOqD,EAAoBp9B,EAAGo9B,EAAoB98B,GAC1D7C,EAAQhG,UAAY6kC,EACpB7+B,EAAQg1B,YAAc4J,EACtB5+B,EAAQu8B,WAKR1b,GACFjL,EAAatd,QAAQsnC,IACnB,IAAIzkC,KAAEA,EAAImmB,MAAEA,EAAKziB,SAAEA,GAAa+gC,GAC1Br9B,EAAGwiB,GAAWzD,GACd/e,EAAGiiB,EAAW3hB,EAAG4hB,GAAc5lB,EAErCmB,EAAQ80B,OACJtQ,GAAa7hB,EACf3C,EAAQi1B,UAAY,SACXlQ,EAAS,EAClB/kB,EAAQi1B,UAAY,OACXlQ,EAAS,IAClB/kB,EAAQi1B,UAAY,SAGtBj1B,EAAQy8B,aAAe,SAEvBz8B,EAAQH,KAAUkhB,EAAH,KACf/gB,EAAQ+0B,UAAY4J,EACpB3+B,EAAQ21B,SAASx6B,EAAMqpB,EAAWC,GAClCzkB,EAAQ61B,YC3IC,SAASgK,GAAapF,GACnC,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC7ByC,MAAOqlC,EAAW9vB,MAAEA,GAAUJ,GAEhCkN,SAAEA,EAAQpK,SAAEA,EAAQqK,SAAEA,EAAQxK,SAAEA,GAAanC,EAAUG,SAE7CP,EAAM9X,KACN8X,EAAM9X,KAEF,YAAd8X,EAAM9X,MACRkY,EAAUiF,SAAS/c,QAAQ,CAAC6pB,EAAYC,KACtCD,EAAW7pB,QAAQ,CAACgd,EAAS+M,KAC3B/M,EAAQhd,QAAQ,CAACwX,EAAYI,KAC3B,IAAI3N,EAAEA,EAACM,EAAEA,EAAC3J,KAAEA,EAAI4D,SAAEA,EAAQ2lB,UAAEA,EAAS3jB,UAAEA,GAAcgR,GAC/ClW,MAAOmmC,GAAiBjhC,EAE9BkB,EAAQ80B,OACR90B,EAAQ+0B,UAAYh1B,EAASggC,EAAc//B,EAASuC,EAAIzF,EAAW,EAAG+F,EAAI4f,EAAWlgB,EAAIzF,EAAU+F,GAC/F3J,GAAQ,EACV8G,EAAQw8B,SAASj6B,EAAIzF,EAAW,EAAG+F,EAAG/F,GAAW2lB,EAAYgY,GAE7Dz6B,EAAQw8B,SAASj6B,EAAIzF,EAAW,EAAG+F,EAAG/F,EAAU2lB,EAAYgY,GAG9Dz6B,EAAQ61B,gBAKC,GAAX4E,GACFrqB,EAAUiF,SAAS/c,QAAQ,CAAC6pB,EAAYC,KACtCD,EAAW7pB,QAAQ,CAACgd,EAAS+M,KAC3B/M,EAAQhd,QAAQ,CAACwX,EAAYI,KAC3B,IAAMjX,KAAM+mC,EAAWz9B,EAAEA,EAACM,EAAEA,EAAC/F,SAAEA,EAAQ2lB,UAAEA,EAASvpB,KAAEA,EAAIuB,MAAEA,EAAKqE,UAAEA,GAAcgR,GACzE7W,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,EAAalrB,OAAQmrB,GAAgB5lC,GAC1Gb,MAAOmmC,GAAiBjhC,EAC9B,MAAM3D,EAAOklC,EAAcA,EAAYnnC,GAAQA,EAG/C+mC,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EAEnEH,GAAaD,IACfhgC,EAAQ80B,OACR90B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQg1B,YAA4B,QAAdmL,EAAuBJ,EAAeI,EAC5DngC,EAAQ+0B,UAAY,UACpB/0B,EAAQy8B,aAAe,SACvBz8B,EAAQi1B,UAAY,SAEhB/7B,GAAQ,GACV8G,EAAQ41B,WAAWz6B,EAAMoH,EAAGM,EAAI4f,EAAY,GAC5CziB,EAAQ21B,SAASx6B,EAAMoH,EAAGM,EAAI4f,EAAY,KAE1CziB,EAAQ41B,WAAWz6B,EAAMoH,EAAGM,EAAI4f,EAAY,GAC5CziB,EAAQ21B,SAASx6B,EAAMoH,EAAGM,EAAI4f,EAAY,IAE5CziB,EAAQ61B,mBAOlBzlB,EAAUiF,SAAS/c,QAAQ,CAAC6pB,EAAYC,KACtCD,EAAW7pB,QAAQ,CAACgd,EAAS+M,KAC3B/M,EAAQhd,QAAQ,CAACwX,EAAYI,KAC3B,IAAI3N,EAAEA,EAACM,EAAEA,EAAC3J,KAAEA,EAAI4D,SAAEA,EAAQ2lB,UAAEA,EAAS3jB,UAAEA,GAAcgR,GAC/ClW,MAAOmmC,GAAiBjhC,EAE9BkB,EAAQ80B,OACR90B,EAAQ+0B,UAAYgL,EAEhB7mC,EAAO,EACT8G,EAAQw8B,SAASj6B,EAAGM,EAAK/F,EAAW29B,EAAW,EAAGhY,EAAW3lB,EAAW29B,GAExEz6B,EAAQw8B,SAASj6B,EAAGM,EAAK/F,EAAW29B,EAAW,GAAIhY,EAAW3lB,EAAW29B,GAE3Ez6B,EAAQ61B,gBAKC,GAAX4E,GACFrqB,EAAUiF,SAAS/c,QAAQ,CAAC6pB,EAAYC,KACtCD,EAAW7pB,QAAQ,CAACgd,EAAS+M,KAC3B/M,EAAQhd,QAAQ,CAACwX,EAAYI,KAC3B,IAAMjX,KAAM+mC,EAAWz9B,EAAEA,EAACM,EAAEA,EAAC/F,SAAEA,EAAQ2lB,UAAEA,EAASvpB,KAAEA,EAAIuB,MAAEA,EAAKqE,UAAEA,GAAcgR,GACzE7W,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,EAAalrB,OAAQmrB,GAAgB5lC,GAC1Gb,MAAOmmC,GAAiBjhC,EAC9B,MAAM3D,EAAOklC,EAAcA,EAAYnnC,GAAQA,EAG/C+mC,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EAEnEH,GAAaD,IACfhgC,EAAQ80B,OACR90B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQg1B,YAA4B,QAAdmL,EAAuBJ,EAAeI,EAC5DngC,EAAQ+0B,UAAY,UACpB/0B,EAAQy8B,aAAe,SACvBz8B,EAAQi1B,UAAY,SAEhB/7B,GAAQ,GACV8G,EAAQ41B,WAAWz6B,EAAMoH,EAAIkgB,EAAY,EAAG5f,GAC5C7C,EAAQ21B,SAASx6B,EAAMoH,EAAIkgB,EAAY,EAAG5f,KAE1C7C,EAAQ41B,WAAWz6B,EAAMoH,EAAIkgB,EAAY,EAAG5f,GAC5C7C,EAAQ21B,SAASx6B,EAAMoH,EAAIkgB,EAAY,EAAG5f,IAE5C7C,EAAQ61B,kBClHP,SAASyK,GAAc7F,GACpC,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC7ByC,MAAOqlC,EAAW9vB,MAAEA,GAAUJ,GAEhC3P,OAAEA,EAAME,KAAEA,EAAID,OAAEA,EAAME,KAAEA,EAAIoc,MAAEA,EAAKG,MAAEA,EAAKG,SAAEA,EAAQpK,SAAEA,EAAQqK,SAAEA,EAAQxK,SAAEA,GAAanC,EAAUG,SAEjGwN,EAAwB,SAAd/N,EAAM9X,KAAkB6kB,EAAWD,EAC7CkB,EAAwB,SAAdhO,EAAM9X,KAAkBqa,EAAWG,EAEjD,SAAS6tB,EAAiB5iB,EAASc,GACjC,SAAId,EAAQc,EAAI,KAAMd,EAAQc,EAAI,MACzBd,EAAQc,GAAG5b,GAAKjB,KAAK/F,IAAI8hB,EAAQc,EAAI,GAAG5b,EAAG8a,EAAQc,EAAI,GAAG5b,IAAM8a,EAAQc,GAAG5b,GAAKjB,KAAK9F,IAAI6hB,EAAQc,EAAI,GAAG5b,EAAG8a,EAAQc,EAAI,GAAG5b,IAMrI,SAAS29B,EAASxjC,EAAM8B,GACtB,IAAM7F,KAAMwnC,EAAQzmC,UAAEA,EAAWJ,MAAOQ,EAAWH,QAASK,GAAgB0C,GACtEpD,MAAO8mC,GAAkB5hC,EAE3B2hC,IACFzgC,EAAQ80B,OACR90B,EAAQ2gC,SAAW,QACnB3gC,EAAQm8B,YAAc7hC,EACtB0F,EAAQhG,UAAYA,EACpBgG,EAAQg1B,YAA2B,QAAb56B,EAAsBsmC,EAAgBtmC,EAC5D4F,EAAQu8B,SACRv8B,EAAQ61B,WAIZ,SAAS+K,EAASvjC,EAAMyB,EAAWgoB,EAAYpS,EAAYqS,EAAUC,GACnE,IAAM/tB,KAAM4nC,EAAUjnC,MAAOknC,EAAW7mC,QAAS8mC,GAAgB1jC,GAC3DzD,MAAO8mC,GAAkB5hC,EAE3B+hC,IACgB,YAAd7wB,EAAM9X,KACJ6lB,GAAW,GAAKC,GAAW,GAC7Bhe,EAAQs8B,OAAOvV,EAAU7mB,GACzBF,EAAQs8B,OAAOxV,EAAY5mB,IAClB6d,GAAW,GAAKC,GAAW,GACpChe,EAAQs8B,OAAOvV,EAAU3mB,GACzBJ,EAAQs8B,OAAOxV,EAAY1mB,KAE3BJ,EAAQs8B,OAAOvV,EAAUvK,GACzBxc,EAAQs8B,OAAOxV,EAAYtK,IAGzBuB,GAAW,GAAKC,GAAW,GAC7Bhe,EAAQs8B,OAAOr8B,EAAQ+mB,GACvBhnB,EAAQs8B,OAAOr8B,EAAQyU,IACdqJ,GAAW,GAAKC,GAAW,GACpChe,EAAQs8B,OAAOn8B,EAAM6mB,GACrBhnB,EAAQs8B,OAAOn8B,EAAMuU,KAErB1U,EAAQs8B,OAAO3f,EAAOqK,GACtBhnB,EAAQs8B,OAAO3f,EAAOjI,IAG1B1U,EAAQq9B,YACRr9B,EAAQ80B,OACR90B,EAAQm8B,YAAc4E,EACtB/gC,EAAQ+0B,UAAYh1B,EAAsB,QAAb+gC,EAAsBJ,EAAgBI,EAAW9gC,EAASC,EAAQG,EAAMD,EAAMD,GAC3GF,EAAQ28B,OACR38B,EAAQ61B,WAIZpmB,EAAUW,EAAUyE,WAAWvc,QAAQ0oC,IACrC,IAOIla,EAAYpS,EAAYqS,EAAUC,GAPlCloB,UAAEA,EAAS9B,KAAEA,EAAIG,OAAEA,EAAME,KAAEA,EAAI5C,MAAEA,EAAKwC,OAAEA,EAAMC,aAAEA,GAAiB8jC,GAC/DpnC,MAAO8mC,GAAkB5hC,GAEzB7F,KAAMgoC,EAAY/oC,KAAM6c,EAAY3X,KAAM4X,EAAYpb,MAAOqb,GAAgB9X,GAE7ElE,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,EAAalrB,OAAQmrB,GAAgB5lC,EAIhH,GAAIwC,EAAQ,CAEV+jC,EAAS9nC,KAAO8nC,EAAS9nC,KAAK8L,IAAI4R,IAChC,IAAIrU,EAAEA,EAACM,EAAEA,EAACvE,OAAEA,EAAMpF,KAAEA,GAAS0d,EA4B7B,MA1BkB,YAAd5G,EAAM9X,KAEN0e,EAAS/T,EADPkb,GAAW,GAAKC,GAAW,EAChBnb,EAAIvE,EAASA,EAASm8B,EAC1B1c,GAAW,GAAKC,GAAW,EACvBnb,EAAIvE,EAASA,EAASm8B,EAE/BvhC,EAAO,EACI2J,EAAIvE,EAASA,EAASm8B,EAEtB53B,EAAIvE,EAASA,EAASm8B,EAKrC7jB,EAASrU,EADPwb,GAAW,GAAKC,GAAW,EAChBzb,EAAIjE,EAASA,EAASm8B,EAC1B1c,GAAW,GAAKC,GAAW,EACvBzb,EAAIjE,EAASA,EAASm8B,EAE/BvhC,EAAO,EACIqJ,EAAIjE,EAASA,EAASm8B,EAEtBl4B,EAAIjE,EAASA,EAASm8B,EAKlC7jB,IAIToqB,EAASE,UAAYF,EAAS9nC,KAAKqsB,OAAO3O,GACT,iBAAjBA,EAAS1d,OAIHgE,EAAe8jC,EAASE,UAAYF,EAAS9nC,MAEnDZ,QAAQ,CAACse,EAAUC,EAAW8G,KAC5C,MAAMjH,EAAI,GACJjV,EAAI,GACV,IAAI0/B,EAAM,KACNC,EAAM,KACNC,EAAM,KACNC,EAAM,MACN/+B,EAAEA,EAACM,EAAEA,EAAC3J,KAAEA,GAAS0d,EAErB,GAAmB,iBAAR1d,EACT,GAAI4tB,GAAcpS,EAAY,CAC5B,IAAI+J,EAAI5H,EAAY,EASpB,GARI4H,EAAI,GACN0iB,EAAMxjB,EAAQ,GAAGpb,GAAKob,EAAQ,GAAGpb,EAAIob,EAAQ,GAAGpb,GAAKmU,EACrD0qB,EAAMzjB,EAAQ,GAAG9a,GAAK8a,EAAQ,GAAG9a,EAAI8a,EAAQ,GAAG9a,GAAK6T,IAErDyqB,EAAMxjB,EAAQc,GAAGlc,GAAKob,EAAQc,EAAI,GAAGlc,EAAIob,EAAQc,EAAI,GAAGlc,GAAKmU,EAC7D0qB,EAAMzjB,EAAQc,GAAG5b,GAAK8a,EAAQc,EAAI,GAAG5b,EAAI8a,EAAQc,EAAI,GAAG5b,GAAK6T,GAG3D+H,EAAId,EAAQliB,OAAS,EAAG,CAC1B,IAAIm7B,EAAOjZ,EAAQliB,OAAS,EAC5B4lC,EAAM1jB,EAAQiZ,GAAMr0B,GAAKob,EAAQiZ,GAAMr0B,EAAIob,EAAQiZ,EAAO,GAAGr0B,GAAKd,EAClE6/B,EAAM3jB,EAAQiZ,GAAM/zB,GAAK8a,EAAQiZ,GAAM/zB,EAAI8a,EAAQiZ,EAAO,GAAG/zB,GAAKpB,OAElE4/B,EAAM1jB,EAAQc,EAAI,GAAGlc,GAAKob,EAAQc,EAAI,GAAGlc,EAAIob,EAAQc,GAAGlc,GAAKd,EAC7D6/B,EAAM3jB,EAAQc,EAAI,GAAG5b,GAAK8a,EAAQc,EAAI,GAAG5b,EAAI8a,EAAQc,GAAG5b,GAAKpB,EAG3D8+B,EAAiB5iB,EAASc,EAAI,KAChC6iB,EAAM3jB,EAAQc,EAAI,GAAG5b,GAEnB09B,EAAiB5iB,EAASc,KAC5B2iB,EAAMzjB,EAAQc,GAAG5b,GAGnB7C,EAAQuhC,cAAcJ,EAAKC,EAAKC,EAAKC,EAAK/+B,EAAGM,GAE7CkkB,EAAWxkB,EACXykB,EAAWnkB,OAEX7C,EAAQo8B,YACRp8B,EAAQq8B,OAAO95B,EAAGM,GAClBikB,EAAavkB,EACbmS,EAAa7R,IAIX3F,GAAgC,iBAAThE,GAAsB2d,EAAY,GAAK8G,EAAQliB,UACtEsrB,GAAYC,IACdwZ,EAASxjC,EAAM8B,GACf8hC,EAASvjC,EAAMyB,EAAWgoB,EAAYpS,EAAYqS,EAAUC,GAC5DD,EAAW,KACXC,EAAW,MAEbF,EAAa,KACbpS,EAAa,aAIjBssB,EAAS9nC,KAAKZ,QAAQ,CAACse,EAAUC,EAAW8G,KAC1C,IAAIpb,EAAEA,EAACM,EAAEA,EAACvE,OAAEA,EAAMpF,KAAEA,GAAS0d,EAEX,YAAd5G,EAAM9X,KAEN2K,EADEkb,GAAW,GAAKC,GAAW,EACzBnb,EAAIvE,EAASA,EAASm8B,EACjB1c,GAAW,GAAKC,GAAW,EAChCnb,EAAIvE,EAASA,EAASm8B,EAEtBvhC,EAAO,EACL2J,EAAIvE,EAASA,EAASm8B,EAEtB53B,EAAIvE,EAASA,EAASm8B,EAK5Bl4B,EADEwb,GAAW,GAAKC,GAAW,EACzBzb,EAAIjE,EAASA,EAASm8B,EACjB1c,GAAW,GAAKC,GAAW,EAChCzb,EAAIjE,EAASA,EAASm8B,EAEtBvhC,EAAO,EACLqJ,EAAIjE,EAASA,EAASm8B,EAEtBl4B,EAAIjE,EAASA,EAASm8B,EAKb,iBAARvhC,IACL4tB,GAAcpS,GAChB1U,EAAQs8B,OAAO/5B,EAAGM,GAClBkkB,EAAWxkB,EACXykB,EAAWnkB,IAEX7C,EAAQo8B,YACRp8B,EAAQq8B,OAAO95B,EAAGM,GAClBikB,EAAavkB,EACbmS,EAAa7R,MAIX3F,GAAgC,iBAAThE,GAAsB2d,EAAY,GAAK8G,EAAQliB,SACtEsrB,GAAYC,IACdwZ,EAASxjC,EAAM8B,GACf8hC,EAASvjC,EAAMyB,EAAWgoB,EAAYpS,EAAYqS,EAAUC,GAC5DF,EAAa,KACbpS,EAAa,KACbqS,EAAW,KACXC,EAAW,QAMJ,GAAXyT,IACEwG,IACFjhC,EAAQ80B,OACRkM,EAAS9nC,KAAKZ,QAAQse,IACpB,IAAIrU,EAAEA,EAACM,EAAEA,EAAC3J,KAAEA,GAAS0d,EAErB,GAAoB,iBAAT1d,EAEX,OAAQ6b,GACN,IAAK,SACH/U,EAAQo8B,YACRp8B,EAAQ08B,IAAIn6B,EAAGM,EAAGmS,EAAa,EAAG,EAAG,EAAIpT,KAAKqP,IAC9CjR,EAAQ+0B,UAA2B,QAAf9f,EAAwByrB,EAAgBzrB,EAC5DjV,EAAQ28B,OAER38B,EAAQo8B,YACRp8B,EAAQ08B,IAAIn6B,EAAGM,EAAGmS,EAAa,EAAG,EAAG,EAAIpT,KAAKqP,IAC9CjR,EAAQ+0B,UAAY,UACpB/0B,EAAQ28B,UAId38B,EAAQ61B,WAIVoK,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EAEnEH,IACFjgC,EAAQ80B,OACR90B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQ+0B,UAA0B,QAAdoL,EAAuBO,EAAgBP,EAC3DngC,EAAQi1B,UAAY,SAEpB+L,EAAS9nC,KAAKZ,QAAQse,IACpB,IAAIrU,EAAEA,EAACM,EAAEA,EAAC3J,KAAEA,GAAS0d,EAErB,GAAoB,iBAAT1d,EAAmB,OAE9B,MAAMiC,EAAOklC,EAAcA,EAAYnnC,GAAQA,EAE7B,YAAd8W,EAAM9X,KACJ6lB,GAAW,GAAKC,GAAW,GAC7Bhe,EAAQy8B,aAAe,SACvBz8B,EAAQ21B,SAASx6B,EAAMoH,EAAGM,EAAIu9B,IACrBriB,GAAW,GAAKC,GAAW,GACpChe,EAAQy8B,aAAe,MACvBz8B,EAAQ21B,SAASx6B,EAAMoH,EAAGM,EAAIu9B,IAE1BlnC,GACF8G,EAAQy8B,aAAe,SACvBz8B,EAAQ21B,SAASx6B,EAAMoH,EAAGM,EAAIu9B,KAE9BpgC,EAAQy8B,aAAe,MACvBz8B,EAAQ21B,SAASx6B,EAAMoH,EAAGM,EAAIu9B,KAIlCpgC,EAAQy8B,aAAe,SACvBz8B,EAAQ21B,SAASz8B,EAAMqJ,EAAGM,EAAIu9B,MAIlCpgC,EAAQ61B,cC/SD,SAASgK,GAAapF,GACnC,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC/Bc,gBAAEA,EAAiB2B,MAAOqlC,GAAgBlwB,GAC1C1W,KAAEA,EAAImD,OAAEA,EAAMC,OAAEA,EAAMkB,YAAEA,EAAWC,iBAAEA,EAAgBylB,SAAEA,EAAQnF,QAAEA,EAAOxgB,SAAEA,GAAa6S,EAAUM,UAChG/N,EAASC,GAAWvG,GACpBunB,EAAWD,GAAarnB,EACzB4U,EAA0B,IAAhB1T,EAAqBA,EAAcoE,KAAKqP,GAAM,IAAM,EA2ClE,GAzCA/X,EAAKZ,QAAQ,CAACse,EAAUC,KACtBD,EAAS1F,QAAUA,EAGjB0F,EAASnB,aADK,QAAZlY,EACuB,EAAIrE,EAAKuC,OAAUg/B,EAEnB7jB,EAASxR,MAAQ8d,EAAYuX,EAExD7jB,EAASlB,MAAQxE,EAAU,EAAI0F,EAASnB,aAAe7T,KAAKqP,GAE5D,IAAI3U,EAASqnB,EACG,UAAZpmB,GAAoC,QAAZA,IAC1BjB,EAASsnB,GAAcD,EAAYC,GAAahN,EAASxR,MAAS2Y,GAEpEnH,EAASta,OAASA,EAElB0D,EAAQo8B,YACRp8B,EAAQq8B,OAAO15B,EAASC,GACxB5C,EAAQ08B,IAAI/5B,EAASC,EAAStG,EAAQsa,EAAS1F,QAAS0F,EAASlB,OACjE1V,EAAQhG,UAAY,EACpBgG,EAAQg1B,YAAcl8B,EACtBkH,EAAQ+0B,UAAYne,EAAS9X,UAAUlF,MACvCoG,EAAQ28B,OACHl/B,GACHuC,EAAQu8B,SAGN3Y,EAAY,IACd5jB,EAAQo8B,YACRp8B,EAAQq8B,OAAO15B,EAASC,GACxB5C,EAAQ08B,IAAI/5B,EAASC,EAASghB,EAAWhN,EAAS1F,QAAS0F,EAASlB,OACpE1V,EAAQ+0B,UAAYj8B,EACpBkH,EAAQg1B,YAAcl8B,EACtBkH,EAAQu8B,SACRv8B,EAAQ28B,QAGVzrB,EAAU0F,EAASlB,QAIN,GAAX+kB,EAAc,CAChB,IAAMhgC,MAAO+mC,EAAW9jC,UAAEA,EAASI,MAAEA,GAAUsS,EAAUM,UACnDzX,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,EAAalrB,OAAQmrB,GAAgBmB,GAC5G5jC,QAAEA,EAAOC,QAAEA,EAAO7D,UAAEA,EAAS2D,cAAEA,GAAkBD,EACjD+jC,EAAa9d,EAAY/lB,EACzB8jC,EAAa,MACXzoC,KAAM0oC,EAASxmC,KAAEA,EAAIzB,UAAEA,EAASqE,QAAEA,EAAOC,aAAEA,EAAYzE,QAAEA,EAAOT,gBAAEA,EAAeiG,YAAEA,EAAWC,YAAEA,GAAgBlB,GAChHnE,SAAUihC,EAAchhC,MAAOihC,EAAWhhC,WAAYihC,GAAmBphC,GACzEC,SAAUioC,EAAiBhoC,MAAOioC,EAAchoC,WAAYioC,GAAsB9jC,EAuExF,GApEAiiC,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EACvEC,EAAcP,GAAeA,EAAY5qB,OAAS4qB,EAAY5qB,OAASmrB,EAEnEJ,GACF/mC,EAAKZ,QAAQ,CAACse,EAAUC,KACtB,IAAI6lB,EAAM,EAAI96B,KAAKqP,IAAM2F,EAAS1F,QAAW,EAAItP,KAAKqP,GAAK2F,EAASnB,aAAgB,GAChFta,EAAOklC,EACPA,EAAY,CAAEvrB,KAAM8B,EAAS9B,KAAM1P,MAAOwR,EAASxR,MAAOjC,SAAWyT,EAASxR,MAAQ8d,EAAY,KAAK9P,QAAQ,MAC1GwD,EAASxR,MAAQ8d,EAAY,KAAK9P,QAAQ,GAA/C,IAGA2uB,EAAqB,CACvBx/B,EAAGX,KAAK4d,IAAIkd,GAAO9lB,EAASta,OAC5BuG,EAAGjB,KAAK2d,IAAImd,GAAO9lB,EAASta,QAG1B0lC,EAAqB,CACvBz/B,EAAGX,KAAK4d,IAAIkd,GAAO+E,EACnB5+B,EAAGjB,KAAK2d,IAAImd,GAAO+E,GAGjBQ,EAAmB,CACrB1/B,EAAGy/B,EAAmBz/B,GAAK,EAAIy/B,EAAmBz/B,EAAI1E,EAAUmkC,EAAmBz/B,EAAI1E,EACvFgF,EAAGm/B,EAAmBn/B,GAGxBo/B,E7D4DR,SAA+Bl/B,EAAOC,EAAQC,GAC5C,GAAID,EACF,KAAOF,EAAYC,EAAOC,EAAQC,IAC5BF,EAAMR,EAAI,EACZQ,EAAMF,IACGE,EAAMR,EAAI,GACnBQ,EAAMF,IAIZ,OAAOE,E6DtEkBm/B,CAAeD,EAAkBP,EAAyD,EAA7C9/B,KAAK/F,IAAI8B,EAAeuiC,EAAgB,IACxGwB,EAAaO,EAEb,IAAIE,EAAuB1/B,EAAwBs/B,EAAoB1lC,GACnE+lC,EAAuB3/B,EAAwBu/B,EAAoB3lC,GACnEgmC,EAAqB5/B,EAAwBw/B,EAAkB5lC,GAGnE2D,EAAQH,KAAUqgC,EAAH,KACf,IAAI/qB,EAAYnV,EAAQyT,YAAYtY,GAAMkD,MACtCikC,EAAoB/+B,OAAOg/B,OAAO,GAAIF,GACtCJ,EAAiB1/B,EAAI,EACvB+/B,EAAkB//B,GAAK5E,EAAgByiC,EAEvCkC,EAAkB//B,GAAK4S,EAAYxX,EAAgByiC,EAGrDpgC,EAAQo8B,YACRp8B,EAAQq8B,OAAO8F,EAAqB5/B,EAAG4/B,EAAqBt/B,GAC5D7C,EAAQwiC,iBAAiBJ,EAAqB7/B,EAAG6/B,EAAqBv/B,EAAGw/B,EAAmB9/B,EAAG8/B,EAAmBx/B,GAClH7C,EAAQhG,UAAYA,EACpBgG,EAAQg1B,YAAcpe,EAAS9X,UAAUlF,MACzCoG,EAAQu8B,SACRv8B,EAAQq9B,YAERr9B,EAAQo8B,YACRp8B,EAAQq8B,OAAOgG,EAAmB9/B,EAAG8/B,EAAmBx/B,GACxD7C,EAAQ08B,IAAI2F,EAAmB9/B,EAAG8/B,EAAmBx/B,EAAGlF,EAAe,EAAG,EAAIiE,KAAKqP,IACnFjR,EAAQq9B,YACRr9B,EAAQ+0B,UAAYne,EAAS9X,UAAUlF,MACvCoG,EAAQ28B,OAER38B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQy8B,aAAe,SACvBz8B,EAAQ+0B,UAA0B,QAAdoL,EAAuBvpB,EAAS9X,UAAUlF,MAAQumC,EACtEngC,EAAQ21B,SAASx6B,EAAMmnC,EAAkB//B,EAAG+/B,EAAkBz/B,KAI9D8+B,EAAW,CACb,MAAMc,EAAatnC,EAAKunC,MAAM,MAAMnd,OAAOxkB,KAAUA,GAC/C4hC,EAAgB5kC,EAAQ2kC,MAAM,MAAMnd,OAAOxkB,KAAUA,GAE3D,IAAI6hC,EAAShgC,EACTigC,EAAc/H,EAAiB2H,EAAWhnC,OAASqmC,EAAoBa,EAAclnC,OAErFsC,IACF8kC,GAAetpC,GAEjBqpC,GAAUC,EAAc,EAExB7iC,EAAQ80B,OACR90B,EAAQi1B,UAAY,SACpBj1B,EAAQy8B,aAAe,SAEnBthC,IACFynC,GAAU9H,EAAiB,EAC3B96B,EAAQH,KAAU+6B,EAAH,KACf56B,EAAQ+0B,UAAY8F,EACpB4H,EAAWnqC,QAAQ,CAAC6C,EAAMwK,EAAO2L,KAC/BtR,EAAQ21B,SAASx6B,EAAMwH,EAASigC,GAC5Bj9B,EAAQ,GAAK2L,EAAI7V,OACnBmnC,GAAU9H,EAAiB,EAE3B8H,GAAU9H,IAGd8H,GAAUrpC,GAGRwE,IACF6kC,GAAUd,EAAoB,EAC9B9hC,EAAQH,KAAU+hC,EAAH,KACf5hC,EAAQ+0B,UAAY8M,EACpBc,EAAcrqC,QAAQ6C,IACpB6E,EAAQ21B,SAASx6B,EAAMwH,EAASigC,GAChCA,GAAUd,KAGd9hC,EAAQ61B,YCvKC,SAASiN,GAAerI,GACrC,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC7ByC,MAAOqlC,GAAgBlwB,GACzBvT,OAAEA,GAAW+T,EAAUjU,UAE3BiU,EAAUgB,WAAW9Y,QAAQirB,IAC3B,IAAIlS,aAAEA,EAAYvS,UAAEA,EAASzB,KAAEA,EAAIL,KAAEA,EAAIG,OAAEA,EAAM1C,MAAEA,GAAU8oB,GACvDtqB,KAAM4nC,EAAUjnC,MAAOknC,EAAW7mC,QAAS8oC,GAAgB1lC,GAC3DpE,KAAMwnC,EAAQuC,UAAEA,EAAWppC,MAAOQ,EAAWH,QAASK,GAAgB0C,GACtE/D,KAAMgoC,EAAY/oC,KAAM6c,EAAY3X,KAAM4X,EAAYpb,MAAOqb,GAAgB9X,GAC7ElE,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,GAAgB3lC,EAoC3F,GAlCAuF,EAAQo8B,YACR/qB,EAAa/Y,QAAQ,CAACse,EAAUC,KAC9B,IAAIyK,EAAQ1K,EAAS0K,MAEjBziB,EAAW4D,EADF,CAAEF,EAAG+e,EAAM/e,EAAIk4B,EAAS53B,EAAGye,EAAMze,EAAI43B,GACHp+B,IACzCkG,EAAGiiB,EAAW3hB,EAAG4hB,GAAc5lB,EACrC+X,EAAS/X,SAAWA,EAEH,GAAbgY,EACF7W,EAAQq8B,OAAO7X,EAAWC,GAE1BzkB,EAAQs8B,OAAO9X,EAAWC,KAG9BzkB,EAAQq9B,YAEJwD,IACF7gC,EAAQ80B,OACR90B,EAAQm8B,YAAc4G,EACtB/iC,EAAQ+0B,UAAyB,QAAb+L,EAAsBhiC,EAAUlF,MAAQknC,EAC5D9gC,EAAQ28B,OACR38B,EAAQu8B,SACRv8B,EAAQ61B,WAGN4K,IACFzgC,EAAQ80B,OACR90B,EAAQgjC,UAAYA,EACpBhjC,EAAQm8B,YAAc7hC,EACtB0F,EAAQg1B,YAA2B,QAAb56B,EAAsB0E,EAAUlF,MAAQQ,EAC9D4F,EAAQu8B,SACRv8B,EAAQ61B,WAGK,GAAX4E,EAAc,CAChB,GAAIwG,EACF,OAAQlsB,GACN,IAAK,SACH/U,EAAQ80B,OACRzjB,EAAa/Y,QAAQse,IACnB,IAAMrU,EAAGiiB,EAAW3hB,EAAG4hB,GAAc7N,EAAS/X,SAC9CmB,EAAQo8B,YACRp8B,EAAQ08B,IAAIlY,EAAWC,EAAWzP,EAAa,EAAG,EAAa,EAAVpT,KAAKqP,IAC1DjR,EAAQ+0B,UAA2B,QAAf9f,EAAwBnW,EAAUlF,MAAQqb,EAC9DjV,EAAQ28B,OAER38B,EAAQo8B,YACRp8B,EAAQ08B,IAAIlY,EAAWC,EAAWzP,EAAa,EAAG,EAAa,EAAVpT,KAAKqP,IAC1DjR,EAAQ+0B,UAAY,OACpB/0B,EAAQ28B,SAEV38B,EAAQ61B,UAMdoK,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EAEnEH,IACFjgC,EAAQ80B,OACR90B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQ+0B,UAA0B,QAAdoL,EAAuBrhC,EAAUlF,MAAQumC,EAC7DngC,EAAQi1B,UAAY,SACpBj1B,EAAQy8B,aAAe,SAEvBprB,EAAa/Y,QAAQse,IACnB,IAAIrU,EAAEA,EAACM,EAAEA,GAAM+T,EAAS/X,SACxBmB,EAAQ21B,SAAS/e,EAAS1d,KAAMqJ,EAAGM,EAAIu9B,SCtFlC,SAAS6C,GAAiBxI,GACvC,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC7ByC,MAAOqlC,GAAgBlwB,EAE7BQ,EAAUsT,aAAaprB,QAAQ4qC,IAC7B,IAAMpuB,KAAMquB,EAAejqC,KAAEA,EAAIuB,MAAEA,EAAKqE,UAAEA,EAAS7E,QAAEA,EAAOD,UAAEA,EAASmE,YAAEA,GAAgB+kC,GACnFjqC,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,GAAgB3lC,GACrFb,MAAOwqB,GAAqBtlB,EAElC5F,EAAKZ,QAAQse,IACX,IAAI4N,UAAEA,EAASC,UAAEA,EAASnoB,OAAEA,EAAQ1C,MAAOwpC,GAAkBxsB,EAC7D5W,EAAQ80B,OACR90B,EAAQo8B,YACRp8B,EAAQ08B,IAAIlY,EAAWC,EAAWnoB,EAASm+B,EAAS,EAAa,EAAV74B,KAAKqP,IACxDjX,EAAY,IACdgG,EAAQg1B,YAA6B,QAAf72B,EAAwBilC,EAAgBjlC,EAC9D6B,EAAQhG,UAAY,EACpBgG,EAAQu8B,UAEVv8B,EAAQ+0B,UAAYqO,EACpBpjC,EAAQm8B,YAAcliC,EACtB+F,EAAQ28B,OACR38B,EAAQ61B,YAGK,GAAX4E,IAEFwF,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EAEnEH,IACFjgC,EAAQ80B,OACR90B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQ+0B,UAA0B,QAAdoL,EAAuB/b,EAAmB+b,EAE9DjnC,EAAKZ,QAAQse,IACX,IAAI/T,EAAEA,EAACwhB,EAAEA,EAAC/nB,OAAEA,EAAMwY,KAAEA,EAAI0P,UAAEA,EAASC,UAAEA,GAAc7N,EAC/Czb,EAAO2Z,IAAcuP,GAAQ8e,GAED,iBAArB/e,GACTpkB,EAAQi1B,UAAY,SACpBj1B,EAAQy8B,aAAe,SACvBz8B,EAAQ21B,SAASx6B,EAAMqpB,EAAWC,EAAYnoB,EAAS8jC,KAEvDpgC,EAAQi1B,UAAY,SACpBj1B,EAAQy8B,aAAe,SACvBz8B,EAAQ21B,SAASx6B,EAAMqpB,EAAWC,MAGtCzkB,EAAQ61B,cCnDD,SAASgK,GAAapF,GACnC,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC/BkB,KAAEA,EAAI0F,YAAEA,EAAWE,UAAEA,EAAWrE,MAAO+mC,GAAgBpxB,EAAUuU,aACjE5lB,YAAEA,EAAWC,YAAEA,GAAgBF,EA2BnC,GAzBA5F,EAAKZ,QAAQse,IACX,IAAI0K,MAAEA,EAAOxiB,UAAWukC,GAAkBzsB,GACpChd,MAAOwpC,GAAkBC,EAE/BrjC,EAAQo8B,YACR9a,EAAMhpB,QAAQ,CAACgrC,EAAWC,KACxB,IAAIhhC,EAAEA,EAACM,EAAEA,GAAMygC,EACG,GAAdC,EACFvjC,EAAQq8B,OAAO95B,EAAGM,EAAI43B,GAEtBz6B,EAAQs8B,OAAO/5B,EAAGM,EAAI43B,KAG1Bz6B,EAAQq9B,YAEJr+B,EAAc,IAChBgB,EAAQg1B,YAAcj2B,EACtBiB,EAAQhG,UAAYgF,EACpBgB,EAAQu8B,UAEVv8B,EAAQ+0B,UAAYqO,EACpBpjC,EAAQ28B,SAIK,GAAXlC,EAAc,CAChB,IAAMhgC,MAAOqlC,GAAgBlwB,GACvB3W,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,EAAavhC,SAAU2kC,GAAkBhC,EAEpHvB,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EAEnEH,IACFjgC,EAAQ80B,OACR57B,EAAKZ,QAAQse,IACX,IAAI9B,KAAEA,EAAMhW,UAAWukC,EAAale,UAAEA,GAAcvO,GAChDrU,EAAEA,EAACM,EAAEA,GAAMsiB,EAEM,UAAjBqe,GACFxjC,EAAQi1B,UAAY,SACpBj1B,EAAQy8B,aAAe,SACvBz8B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQg1B,YAAcqO,EAAczpC,MACpCoG,EAAQ+0B,UAAY,UACpB/0B,EAAQ41B,WAAW9gB,EAAMvS,EAAGM,GAC5B7C,EAAQ21B,SAAS7gB,EAAMvS,EAAGM,KAIxB7C,EAAQi1B,UAFS,SAAfr2B,EAEkB,QAGA,OAEtBoB,EAAQy8B,aAAe,SACvBz8B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQ+0B,UAA0B,QAAdoL,EAAuBkD,EAAczpC,MAAQumC,EACjEngC,EAAQ21B,SAAS7gB,EAAMvS,EAAGM,MAG9B7C,EAAQ61B,YCjEC,SAAS4N,GAAqBhJ,GAC3C,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,EAASqG,UAAEA,GAAcze,KAC1C0rC,EAAoBj0B,EAAUgH,EAAuB,cACnDnX,SAAUsmB,EAAgBrmB,QAASsmB,EAAelpB,IAAK6kB,GAAckiB,EAAkB,IACzF5tB,KAAEA,EAAIxW,SAAEA,EAAQ2nB,SAAEA,EAAQtqB,IAAEA,GAAQyT,EAAUqE,iBA4BlD,GA1BAzU,EAAQ80B,OACRhf,EAAKxd,QAAQyI,IACX,IAAInH,MAAEA,EAAKsF,YAAEA,EAAWjF,QAAEA,EAAO+E,YAAEA,EAAW4nB,UAAEA,EAASF,YAAEA,EAAWC,cAAEA,GAAkB5lB,GACtFwB,EAAEA,EAACM,EAAEA,EAACxE,MAAEA,EAAKC,OAAEA,GAAWsoB,GACxB5G,OAAQ2jB,EAAc1jB,OAAQ2jB,EAAc1jB,KAAM2jB,EAAY1jB,KAAM2jB,GAAepd,GACnF1G,OAAQ+jB,EAAgB9jB,OAAQ+jB,EAAgB9jB,KAAM+jB,EAAc9jB,KAAM+jB,GAAiBvd,EAEjG3mB,EAAQg1B,YAAc91B,EACtBc,EAAQ+0B,UAAYn7B,EACpBoG,EAAQm8B,YAAcliC,EACtB+F,EAAQmkC,WAAW5hC,EAAIvD,EAAc,EAAG6D,EAAI7D,EAAc,EAAGX,EAAQW,EAAaV,EAASU,GAC3FgB,EAAQw8B,SAASj6B,EAAGM,EAAGxE,EAAOC,GAE9B0B,EAAQo8B,YACRp8B,EAAQhG,UAAY,EACpBgG,EAAQg1B,YAAc91B,EACtBc,EAAQq8B,OAAOsH,EAAcC,GAC7B5jC,EAAQs8B,OAAOuH,EAAYC,GAC3B9jC,EAAQu8B,SAERv8B,EAAQq8B,OAAO0H,EAAgBC,GAC/BhkC,EAAQs8B,OAAO2H,EAAcC,GAC7BlkC,EAAQu8B,WAEVv8B,EAAQ61B,UAEO,GAAX4E,EAAc,CAChB,IAAMxhC,KAAM6sB,EAAc/rB,UAAWgsB,GAAkBH,GACjD3sB,KAAM+sB,EAAajsB,UAAWksB,GAAiBJ,GAC/C5sB,KAAMitB,EAASpnB,UAAWslC,EAAcrqC,UAAWssB,GAAiB7E,GACpE5nB,MAAOyqC,EAAerqC,UAAWsqC,EAAejqC,SAAUkqC,EAActqC,QAASuqC,GAAoBze,GACrGnsB,MAAO6qC,EAAczqC,UAAW0qC,EAAcrqC,SAAUsqC,EAAa1qC,QAAS2qC,GAAmB3e,GACjGjG,OAAQ6kB,EAAgB5kB,OAAQ6kB,EAAgB5kB,KAAM6kB,EAAc5kB,KAAM6kB,GAAiB1lC,GAC3F0gB,OAAQilB,EAAehlB,OAAQilB,EAAehlB,KAAMilB,EAAahlB,KAAMilB,GAAgBne,GACvFrtB,MAAOyrC,EAAUprC,QAASqrC,GAAelB,GACzCpqC,UAAWurC,EAAcnrC,UAAWorC,GAAiBnf,EA4B3D,GA1BIP,IACF9lB,EAAQ80B,OACR90B,EAAQo8B,YACRp8B,EAAQq8B,OAAOwI,EAAgBC,GAC/B9kC,EAAQs8B,OAAOyI,EAAcC,GAC7BhlC,EAAQg1B,YAAcqP,EACtBrkC,EAAQhG,UAAYsqC,EACpBtkC,EAAQk8B,YAAYqI,GACpBvkC,EAAQm8B,YAAcqI,EACtBxkC,EAAQu8B,SACRv8B,EAAQ61B,WAGN7P,IACFhmB,EAAQ80B,OACR90B,EAAQo8B,YACRp8B,EAAQq8B,OAAO4I,EAAeC,GAC9BllC,EAAQs8B,OAAO6I,EAAaC,GAC5BplC,EAAQg1B,YAAcyP,EACtBzkC,EAAQhG,UAAY0qC,EACpB1kC,EAAQk8B,YAAYyI,GACpB3kC,EAAQm8B,YAAcyI,EACtB5kC,EAAQu8B,SACRv8B,EAAQ61B,WAGN3P,EAAS,CACX,MAAMY,WAAEA,EAAUpS,WAAEA,EAAUqS,SAAEA,EAAQC,SAAEA,EAAQ9tB,KAAEA,GAASyD,EAE7DqD,EAAQ80B,OACR90B,EAAQhG,UAAYurC,EACpBvlC,EAAQg1B,YAAcwQ,EACtBxlC,EAAQq8B,OAAOvV,EAAYpS,GAC3B1U,EAAQs8B,OAAOvV,EAAUC,GACzBhnB,EAAQu8B,SACRv8B,EAAQ61B,UAER38B,EAAKZ,QAAQgd,IACX,IAAI1b,MAAEA,EAAK2I,EAAEA,EAACM,EAAEA,EAACxE,MAAEA,EAAKC,OAAEA,GAAWgX,EAErCtV,EAAQ80B,OACR90B,EAAQo8B,YACRp8B,EAAQ+0B,UAAwB,QAAZsQ,EAAqBzrC,EAAQyrC,EACjDrlC,EAAQm8B,YAAcmJ,EACtBtlC,EAAQw8B,SAASj6B,EAAGM,EAAGxE,GAAQC,GAC/B0B,EAAQ61B,cC1FD,SAAS4P,GAAiBhL,GACvC,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC/Bqa,aAAEA,EAAYG,aAAEA,GAAiBpC,EAAUG,UACzC9V,MAAOqlC,GAAgBlwB,EAE7B,MAAM81B,EAAkB91B,EAAKI,MAAMrU,cAAc5B,UAAUC,UACrD2rC,EAAkB/1B,EAAKG,MAAMpU,cAAc5B,UAAUC,UAE3DoW,EAAUgX,aAAa9uB,QAAQstC,IAC7B,IAAI1sC,KAAEA,EAAIuB,MAAEA,GAAUmrC,GAChB3sC,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,GAAgB3lC,EAE3FvB,EAAKZ,QAAQse,IACX,IAAI4N,UAAEA,EAASC,UAAEA,EAAW7qB,MAAOwpC,EAAa3jC,SAAEA,GAAamX,EAC/D5W,EAAQ80B,OACR90B,EAAQo8B,YACJ38B,EACFO,EAAQ8V,KAAK0O,EAA8B,EAAlBmhB,EAAqBlhB,EAA8B,EAAlBihB,EAAqBrzB,EAAiC,EAAlBszB,EAAqBnzB,EAAiC,EAAlBkzB,GAElI1lC,EAAQ8V,KAAK0O,EAAWC,EAAWpS,EAAcG,GAEnDxS,EAAQ+0B,UAAYqO,EACpBpjC,EAAQm8B,YAAc1B,EACtBz6B,EAAQ28B,OACR38B,EAAQ61B,YAGK,GAAX4E,IAEFwF,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EAEnEH,IACFjgC,EAAQ80B,OACR90B,EAAQH,KAAUqgC,EAAH,KACflgC,EAAQ+0B,UAA0B,QAAdoL,EAAuB,UAAYA,EACvDngC,EAAQi1B,UAAY,SACpBj1B,EAAQy8B,aAAe,SAEvBvjC,EAAKZ,QAAQse,IACX,IAAI4N,UAAEA,EAASC,UAAEA,GAAc7N,EAC3Bzb,EAAOyb,EAAS,GACpB5W,EAAQ21B,SAASx6B,EAAMqpB,EAAYnS,EAAe,EAAGoS,EAAYjS,EAAe,KAElFxS,EAAQ61B,cC5CD,SAASgQ,GAAiBpL,GACvC,IAAIz6B,QAAEA,EAAO4P,KAAEA,EAAIQ,UAAEA,GAAcpY,MAC7ByC,MAAOqlC,GAAgBlwB,GACvBnV,MAAO+mC,EAAWjlC,UAAEA,GAAc6T,EAAUojB,aAAat6B,MACzDD,KAAMgnC,EAAWtmC,SAAUumC,EAAetmC,MAAOumC,EAAYzlC,OAAQ0lC,GAAgBoB,GACrFvoC,KAAM6sC,EAAe9rC,UAAW+rC,EAAgBnsC,MAAOosC,GAAmBzpC,EAEhFyD,EAAQ80B,OACR1kB,EAAUojB,aAAa5L,SAAStvB,QAAQ,CAACyI,EAAM4E,KAC7C,IAAIpF,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAExH,KAAEA,GAAS6H,EAC3B1C,EAAQoC,EAAKF,EACbjC,EAASoC,EAAKF,EAElBR,EAAQ+0B,UAAY77B,EAAK4F,UAAUlF,MACnCoG,EAAQm8B,YAAc1B,EAClBqL,IACF9lC,EAAQhG,UAAY+rC,EACpB/lC,EAAQg1B,YAAcgR,EACtBhmC,EAAQmkC,WAAW5jC,EAAIC,EAAInC,EAAOC,IAEpC0B,EAAQw8B,SAASj8B,EAAIC,EAAInC,EAAOC,KAElC0B,EAAQ61B,UAGO,GAAX4E,IAEFwF,EAAYH,GAA0C,kBAApBA,EAAY7mC,KAAoB6mC,EAAY7mC,KAAOgnC,EACrFC,EAAgBJ,GAAeA,EAAYnmC,SAAWmmC,EAAYnmC,SAAWumC,EAC7EC,EAAaL,GAAeA,EAAYlmC,MAAQkmC,EAAYlmC,MAAQumC,EACpEC,EAAcN,GAAeA,EAAYplC,OAASolC,EAAYplC,OAAS0lC,EAEnEH,IACFjgC,EAAQ80B,OACR1kB,EAAUojB,aAAa5L,SAAStvB,QAAQyI,IACtC,IAAIR,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAExH,KAAEA,GAAS6H,GAC3B+T,KAAEA,EAAIhW,UAAEA,GAAc5F,EACtBqJ,EAAIhC,GAAME,EAAKF,GAAM,EACrBsC,EAAIrC,GAAME,EAAKF,GAAM,EAErB1E,EAAM8F,KAAK9F,IAAI2E,EAAKF,EAAIG,EAAKF,GAEjCR,EAAQi1B,UAAY,SACpBj1B,EAAQy8B,aAAe,SACvBz8B,EAAQH,KAAgB,GAAN/D,EAAH,KACfkE,EAAQg1B,YAA4B,QAAdmL,EAAuBrhC,EAAUlF,MAAQumC,EAC/DngC,EAAQ+0B,UAAY,UACpB/0B,EAAQ41B,WAAW9gB,EAAMvS,EAAGM,GAC5B7C,EAAQ21B,SAAS7gB,EAAMvS,EAAGM,KAE5B7C,EAAQ61B,YClDC,SAASoQ,GAAkBxL,GACxC,IAAI7qB,KAAEA,EAAIQ,UAAEA,GAAcpY,MACtBihC,QAAEA,EAAO56B,MAAEA,EAAKC,OAAEA,GAAWsR,EAEjCqpB,EAAQ56B,MAAQA,EAChB46B,EAAQ36B,OAASA,EACjB,MAAM0B,EAAUi5B,EAAQhB,WAAW,MAEnC7nB,EAAU8oB,cAAchgC,KAAKZ,QAAQyI,IACnC,IAAI5F,KAAEA,EAAIoH,EAAEA,EAACM,EAAEA,EAAChD,KAAEA,EAAIzC,KAAEA,EAAInB,OAAEA,EAAM6C,UAAEA,GAAciC,EAEpDf,EAAQ80B,OACR90B,EAAQo8B,YACRp8B,EAAQH,KAAO,GAAGzC,OAAUyC,IAC5BG,EAAQ+0B,UAAYj2B,EAAUlF,MAC9BoG,EAAQi1B,UAAY,SACpBj1B,EAAQ01B,UAAUr3B,EAAQ,EAAIkE,EAAGjE,EAAS,EAAIuE,GAC9C7C,EAAQ/D,OAAQA,EAAS2F,KAAKqP,GAAM,KACpCjR,EAAQ21B,SAASx6B,EAAM,EAAG,GAC1B6E,EAAQ61B,YCFG,SAASqQ,KACtB,MAAMvtC,UAAEA,EAASC,kBAAEA,EAAiBC,gBAAEA,GAAoBb,KAAK4X,KAC/D5X,KAAKmuC,mBAAqBnuC,KAAKmuC,kBAAkBC,OAEjDpuC,KAAKmuC,kBAAoB,IAAIrM,GAAU,CACrCnhC,UAAAA,EACAC,kBAAAA,EACAC,gBAAAA,EACAmhC,UAAWS,IAGTsC,GAAet5B,KAAKzL,OAChBA,KAAKye,UAAUzZ,MAAQhF,KAAKye,UAAU9Z,KAAO3E,KAAKye,UAAUvY,SAAWlG,KAAKye,UAAUxX,aAAejH,KAAKye,UAAUjX,UACtH+9B,GAAS95B,KAAKzL,MAGhBuL,OAAOoD,KAAK3O,KAAKye,WAAWne,QAAQJ,IAClC,OAAQA,GACN,IAAK,MACHmuC,GAAa5iC,KAAKzL,KAAMyiC,GACxB,MACF,IAAK,OACH6F,GAAc78B,KAAKzL,KAAMyiC,GACzB,MACF,IAAK,MACHoF,GAAap8B,KAAKzL,KAAMyiC,GACxB,MACF,IAAK,QACHyD,GAAcz6B,KAAKzL,MACnB8qC,GAAer/B,KAAKzL,KAAMyiC,GAC1B,MACF,IAAK,UACHwI,GAAiBx/B,KAAKzL,KAAMyiC,GAC5B,MACF,IAAK,SACH6L,GAAgB7iC,KAAKzL,KAAMyiC,GAC3B,MACF,IAAK,cACHgJ,GAAqBhgC,KAAKzL,KAAMyiC,GAChC,MACF,IAAK,UACHgL,GAAiBhiC,KAAKzL,KAAMyiC,GAC5B,MACF,IAAK,UACHoL,GAAiBpiC,KAAKzL,KAAMyiC,GAC5B,MACF,IAAK,WACHwL,GAAkBxiC,KAAKzL,KAAMyiC,MAKpB,GAAXA,IACFuC,GAAWv5B,KAAKzL,MAChB2iC,GAAYl3B,KAAKzL,QAGrBiiC,kBAAmB,KACjBjiC,KAAKuuC,MAAMC,QAAQ,oCC5DzB,MACEzuC,YAAY6X,EAAO,IACjB5X,KAAK2X,OAASpM,OAAOg/B,OAAO,GAAIkE,GAChCzuC,KAAK4X,KAAOrM,OAAOg/B,OAAO,GAAI3yB,GAC9B5X,KAAKgI,QAAUhI,KAAK4X,KAAKqpB,QAAQhB,WAAW,MAC5CjgC,KAAKyZ,YAAc,CACjBC,aAAc,GACdxY,KAAM,GACNC,aAAc,EACd6H,OAAQ,IAEVhJ,KAAKgf,WAAa,GAClBhf,KAAKye,UAAY,GACjBze,KAAKoY,UAAY,GAGjBpY,KAAKuuC,MAAQ,IAAIzuC,EACjBE,KAAKuuC,MAAMG,iBAAiB,iBAAkB92B,EAAK+2B,kBAGnDtN,GAAc51B,KAAKzL,MAGnBkuC,GAAWziC,KAAKzL,MAGlBD,WAAW6uC,EAAa,IACtBrjC,OAAOoD,KAAKigC,GAAYtuC,QAAQyN,IACnB,UAAPA,GACF/N,KAAK4X,KAAKC,OAASJ,EAAUm3B,EAAW/2B,QACxCI,EAAUxM,KAAKzL,OAEfgX,EAAc43B,EAAY7gC,EAAK/N,KAAK4X,KAAM7J,GAAK,KAOnDszB,GAAc51B,KAAKzL,MAGnBkuC,GAAWziC,KAAKzL,MAGlBD,YAAYS,GACV,MAAMG,UAAEA,EAASK,QAAEA,GAAYhB,KAAK4X,KAC9Bi3B,EAAiBluC,EAEvB,IAAKK,EAAQC,KAAM,OAEnB,MAAM6tC,YAAEA,EAAW9lC,OAAEA,GAAWhJ,KAAK+uC,gBAAgBvuC,GAErDR,KAAKyZ,YAAc,CACjBC,aAAc,GACdxY,KAAM,GACNC,aAAc,EACd6H,OAAAA,GAGFuC,OAAOoD,KAAKmgC,GAAaxuC,QAAQJ,IAC/B,MAAMsY,EAAes2B,EAAY5uC,GACjC,OAAQA,GACN,IAAK,MACHkd,EAAuB3R,KAAKzL,KAAMwY,GAClC,MACF,IAAK,OACHoE,EAAwBnR,KAAKzL,KAAMwY,GACnC,MACF,IAAK,MACH+E,EAAuB9R,KAAKzL,KAAMwY,GAClC,MACF,IAAK,QACHmF,EAAyBlS,KAAKzL,KAAMwY,GACpC,MACF,IAAK,cACL,IAAK,IACHqF,EAA+BpS,KAAKzL,KAAMwY,MAKhDjN,OAAOoD,KAAKmgC,GAAaxuC,QAAQJ,IAC/B,OAAQA,GACN,IAAK,MACL,IAAK,OACL,IAAK,cACL,IAAK,IACEF,KAAKyZ,YAAYkD,iBACpBxC,EAAmB1O,KAAKzL,KAAM8uC,EAAY5uC,OAMlDsZ,EAAwB/N,KAAKzL,MAEOA,KAAKyZ,YAEzCzZ,KAAK4X,KAAKjX,WAAY,EACtButC,GAAWziC,KAAKzL,MAChBA,KAAK4X,KAAKjX,UAAYkuC,EAGxB9uC,cACE,MAAM6X,KAAEA,EAAI6B,YAAEA,GAAgBzZ,MACxBW,UAAEA,GAAciX,EAChBi3B,EAAiBluC,EAEvBX,KAAKyZ,YAAc,CACjBC,aAAc,GACdxY,KAAM,GACNC,aAAc,EACd6H,OAAQ,IAKVhJ,KAAK4X,KAAKjX,WAAY,EACtButC,GAAWziC,KAAKzL,MAChBA,KAAK4X,KAAKjX,UAAYkuC,EAGxB9uC,gBAAgBS,GACd,MAAMwuC,EAAUxuC,EAAEwuC,SAAWxuC,EAAEwuC,QAAQvrC,OAASjD,EAAEwuC,QAAUxuC,EAAEyuC,eACxDjmC,EAAS,CAAEuB,EAAGykC,EAAQ,GAAGr1B,SAAW,EAAG9O,EAAGmkC,EAAQ,GAAGp1B,SAAW,GACtE,IAAIk1B,EAAc,GAqBlB,OAnBAvjC,OAAOoD,KAAK3O,KAAKye,WAAWne,QAAQJ,IAClC,OAAQA,GACN,IAAK,MACL,IAAK,OACL,IAAK,cACL,IAAK,IACH4uC,EAAY5uC,GAAQiY,EAAyB1M,KAAKzL,KAAMgJ,GACxD,MACF,IAAK,MACH8lC,EAAY5uC,GAAQuY,EAAwBhN,KAAKzL,KAAMgJ,GACvD,MACF,IAAK,QACH8lC,EAAY5uC,GAAQiZ,EAA0B1N,KAAKzL,KAAMgJ,MAOxD,CACL8lC,YAAAA,EACA9lC,OAAAA"}