class t{constructor(){this.events={}}addEventListener(t,e=function(){}){this.events[t]=this.events[t]||[],this.events[t].push(e)}trigger(t,...e){this.events[t]&&this.events[t].forEach(t=>{try{t.apply(null,e)}catch(t){console.error(t)}})}}var e={animation:!0,animationDuration:1e3,animationTiming:"easeInOut",backgroundColor:"#ffffff",colors:["#7cb5ec","#f7a35c","#434348","#90ed7d","#f15c80","#8085e9"],tooltip:{show:!1,data:[],maxTextWidth:0,backgroundColor:"#000000",backgroundRadius:5,backgroundOpacity:.7,padding:10,itemGap:5,iconRadius:5,iconGap:5,textStyle:{fontSize:15,color:"#ffffff",lineHeight:15},axisPointer:{type:"line",lineStyle:{lineWidth:1,color:"#808080",opacity:1},shadowStyle:{color:"#969696",opacity:.3},cross:{show:!0,lineWidth:1,lineColor:"#808080",lineDash:[5,10],lineOpacity:1,backgroundColor:"#999999",backgroundOpacity:1,fontColor:"#ffffff",fontPadding:5}}},label:{show:!0,fontSize:10,color:"auto",margin:5},legend:{show:!0,type:"default",marginTop:15,itemGap:15,shapeRadius:7.5,shapeWidth:30,shapeHeight:15,textStyle:{fontSize:15,color:"#333333",padding:5}},padding:[20,20,20,20],yAxisCategory:{show:!0,type:"category",boundaryGap:!0,axisName:{show:!0,text:"轴线名称",gap:10,textStyle:{color:"#666666",fontSize:15,align:"center"}},axisLabel:{show:!0,gap:5,textStyle:{color:"#666666",fontSize:12}},axisTick:{show:!0,alignWithLabel:!1,length:5,lineStyle:{lineWidth:1,color:"#666666"}},axisLine:{show:!0,lineStyle:{lineWidth:1,color:"#666666"}},axisSplitLine:{show:!0,alignWithLabel:!1,lineStyle:{lineWidth:1,color:"#dddddd"}}},yAxisValue:{show:!0,type:"value",max:"auto",min:"auto",splitNumber:4,axisName:{show:!0,text:"轴线名称",gap:10,textStyle:{color:"#666666",fontSize:15,align:"center"}},axisLabel:{show:!0,gap:5,textStyle:{color:"#666666",fontSize:12}},axisTick:{show:!0,length:5,lineStyle:{lineWidth:1,color:"#666666"}},axisLine:{show:!0,lineStyle:{lineWidth:1,color:"#666666"}},axisSplitLine:{show:!0,lineStyle:{lineWidth:1,color:"#dddddd"}}},xAxisCategory:{show:!0,type:"category",boundaryGap:!0,axisName:{show:!0,text:"轴线名称",gap:10,textStyle:{color:"#666666",fontSize:15}},axisLabel:{show:!0,rotate:0,gap:5,textStyle:{color:"#666666",fontSize:12}},axisTick:{show:!0,alignWithLabel:!1,length:5,lineStyle:{lineWidth:1,color:"#666666"}},axisLine:{show:!0,lineStyle:{lineWidth:1,color:"#666666"}},axisSplitLine:{show:!0,alignWithLabel:!1,lineStyle:{lineWidth:1,color:"#dddddd"}}},xAxisValue:{show:!0,type:"value",max:"auto",min:"auto",splitNumber:4,axisName:{show:!0,text:"轴线名称",gap:10,textStyle:{color:"#666666",fontSize:15,align:"center"}},axisLabel:{show:!0,rotate:0,gap:5,textStyle:{color:"#666666",fontSize:12}},axisTick:{show:!0,length:5,lineStyle:{lineWidth:1,color:"#666666"}},axisLine:{show:!0,lineStyle:{lineWidth:1,color:"#666666"}},axisSplitLine:{show:!0,lineStyle:{lineWidth:1,color:"#dddddd"}}},radarAxis:{shape:"polygon",center:["50%","50%"],radius:"80%",max:"auto",splitNumber:4,axisName:{show:!0,textStyle:{fontSize:15,color:"#666666",margin:10}},axisLine:{show:!0,lineStyle:{lineWidth:1,color:"#cccccc",opacity:1}},splitLine:{show:!0,lineStyle:{lineWidth:1,color:"#cccccc",opacity:1}},splitArea:{odd:{show:!0,color:"#f5f5f5",opacity:1},even:{show:!0,color:"#e6e6e6",opacity:1}}},bar:{barMaxWidth:20,barMinWidth:1,barWidth:"auto",barGap:5},line:{smooth:!1,connectNulls:!1,line:{show:!0,lineWidth:2,color:"auto",opacity:1},symbol:{show:!0,type:"circle",size:7,color:"auto"},area:{show:!1,color:"auto",opacity:.5}},pie:{center:["50%","50%"],radius:[0,"80%"],roseType:!1,offsetAngle:0,disablePieStroke:!0,labelLine:{lineDotRadius:3,lineWidth:1,length1:25,length2:15},title:{show:!1,text:"主标题",textStyle:{fontSize:30,color:"#666666",lineHeight:30},subtext:"副标题",subtextStyle:{fontSize:20,color:"#999999",lineHeight:20},itemGap:5}},radar:{line:{show:!0,lineWidth:1,color:"auto",opacity:1},area:{show:!1,color:"auto",opacity:.5},symbol:{show:!0,type:"circle",size:7,color:"auto"}},scatter:{radius:10,opacity:1,lineWidth:0,strokeColor:"auto"},funnel:{width:"auto",height:"auto",top:"0%",left:"0%",right:"0%",bottom:"0%",max:100,min:0,gap:5,shape:"funnel",sort:"descending",funnelAlign:"center",label:{position:"inside"},itemStyle:{borderColor:"#ffffff",borderWidth:1}},candlestick:{barMaxWidth:20,barMinWidth:1,barWidth:"auto",itemStyle:{color:"#ec0000",bordercolor:"#ec0000",opacity:1,color0:"#00da3c",bordercolor0:"#00da3c",opacity0:1,borderWidth:1},highLine:{show:!1,lineStyle:{color:"#ec0000",lineWidth:1,lineDash:[10,15],opacity:1}},lowLine:{show:!1,lineStyle:{color:"#ec0000",lineWidth:1,lineDash:[10,15],opacity:1}},bar:{show:!1,height:50,margin:15,itemStyle:{color:"auto",opacity:1},lineStyle:{lineWidth:1,lineColor:"#666666"}}},heatmap:{itemStyle:{color:["#BAE7FF","#0050B3"],useSplit:!1}},treemap:{splitLine:{show:!0,lineWidth:5,color:"#ffffff"}},tagCloud:{padding:1,timeInterval:500,font:"serif",fontSize:15,rotate:0,spiral:"archimedean"}};function a(t,e,a,i,n,o){if(s(t)){const{linearGradient:r,colors:l}=t,[s,h,c,u]=r,f=n-a,x=o-i,p=e.createLinearGradient(a+f*s,i+x*h,a+f*c,i+x*u);return l.forEach(t=>{const{offset:e,color:a}=t;p.addColorStop(e,a)}),p}return t}function i(t){let e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t),a=parseInt(e[1],16),i=parseInt(e[2],16),n=parseInt(e[3],16);a/=255,i/=255,n/=255;let o,r,l=Math.max(a,i,n),s=Math.min(a,i,n),h=(l+s)/2;if(l==s)o=r=0;else{let t=l-s;switch(r=h>.5?t/(2-l-s):t/(l+s),l){case a:o=(i-n)/t+(i<n?6:0);break;case i:o=(n-a)/t+2;break;case n:o=(a-i)/t+4}o/=6}return r*=100,r=Math.round(r),h*=100,h=Math.round(h),o=Math.round(360*o),[o,r,h]}function n(t){let e,a,i,[n,o,r]=t;if(n/=360,o/=100,r/=100,0===o)e=a=i=r;else{const t=(t,e,a)=>(a<0&&(a+=1),a>1&&(a-=1),a<1/6?t+6*(e-t)*a:a<.5?e:a<2/3?t+(e-t)*(2/3-a)*6:t),l=r<.5?r*(1+o):r+o-r*o,s=2*r-l;e=t(s,l,n+1/3),a=t(s,l,n),i=t(s,l,n-1/3)}const l=t=>{const e=Math.round(255*t).toString(16);return 1===e.length?"0"+e:e};return`#${l(e)}${l(a)}${l(i)}`}function o(t,e){let[a,i]=e;return{x:a+t.x,y:i-t.y}}function r(t,e,a){let i=!1;return t.x>0&&e.x>0?i=t.y+a>e.y:t.x<0&&e.x<0&&(i=t.y-a<e.y),i}function l(t){return t?(t=String(t).replace("%",""),t=Number(t)/100):t=0,t}function s(t){return"[object Object]"===Object.prototype.toString.call(t)}var h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};var c=function(t,e){return t(e={exports:{}},e.exports),e.exports}((function(t,e){var a="[object Arguments]",i="[object Function]",n="[object GeneratorFunction]",o="[object Map]",r="[object Set]",l=/\w*$/,s=/^\[object .+?Constructor\]$/,c=/^(?:0|[1-9]\d*)$/,u={};u[a]=u["[object Array]"]=u["[object ArrayBuffer]"]=u["[object DataView]"]=u["[object Boolean]"]=u["[object Date]"]=u["[object Float32Array]"]=u["[object Float64Array]"]=u["[object Int8Array]"]=u["[object Int16Array]"]=u["[object Int32Array]"]=u[o]=u["[object Number]"]=u["[object Object]"]=u["[object RegExp]"]=u[r]=u["[object String]"]=u["[object Symbol]"]=u["[object Uint8Array]"]=u["[object Uint8ClampedArray]"]=u["[object Uint16Array]"]=u["[object Uint32Array]"]=!0,u["[object Error]"]=u[i]=u["[object WeakMap]"]=!1;var f="object"==typeof h&&h&&h.Object===Object&&h,x="object"==typeof self&&self&&self.Object===Object&&self,p=f||x||Function("return this")(),d=e&&!e.nodeType&&e,y=d&&t&&!t.nodeType&&t,g=y&&y.exports===d;function b(t,e){return t.set(e[0],e[1]),t}function m(t,e){return t.add(e),t}function S(t,e,a,i){var n=-1,o=t?t.length:0;for(i&&o&&(a=t[++n]);++n<o;)a=e(a,t[n],n,t);return a}function w(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function v(t){var e=-1,a=Array(t.size);return t.forEach((function(t,i){a[++e]=[i,t]})),a}function A(t,e){return function(a){return t(e(a))}}function D(t){var e=-1,a=Array(t.size);return t.forEach((function(t){a[++e]=t})),a}var M,_=Array.prototype,P=Function.prototype,T=Object.prototype,k=p["__core-js_shared__"],L=(M=/[^.]+$/.exec(k&&k.keys&&k.keys.IE_PROTO||""))?"Symbol(src)_1."+M:"",E=P.toString,W=T.hasOwnProperty,z=T.toString,j=RegExp("^"+E.call(W).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),X=g?p.Buffer:void 0,Y=p.Symbol,I=p.Uint8Array,O=A(Object.getPrototypeOf,Object),B=Object.create,C=T.propertyIsEnumerable,R=_.splice,N=Object.getOwnPropertySymbols,H=X?X.isBuffer:void 0,$=A(Object.keys,Object),F=yt(p,"DataView"),G=yt(p,"Map"),q=yt(p,"Promise"),Z=yt(p,"Set"),U=yt(p,"WeakMap"),V=yt(Object,"create"),J=wt(F),K=wt(G),Q=wt(q),tt=wt(Z),et=wt(U),at=Y?Y.prototype:void 0,it=at?at.valueOf:void 0;function nt(t){var e=-1,a=t?t.length:0;for(this.clear();++e<a;){var i=t[e];this.set(i[0],i[1])}}function ot(t){var e=-1,a=t?t.length:0;for(this.clear();++e<a;){var i=t[e];this.set(i[0],i[1])}}function rt(t){var e=-1,a=t?t.length:0;for(this.clear();++e<a;){var i=t[e];this.set(i[0],i[1])}}function lt(t){this.__data__=new ot(t)}function st(t,e){var i=At(t)||function(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&Dt(t)}(t)&&W.call(t,"callee")&&(!C.call(t,"callee")||z.call(t)==a)}(t)?function(t,e){for(var a=-1,i=Array(t);++a<t;)i[a]=e(a);return i}(t.length,String):[],n=i.length,o=!!n;for(var r in t)!e&&!W.call(t,r)||o&&("length"==r||mt(r,n))||i.push(r);return i}function ht(t,e,a){var i=t[e];W.call(t,e)&&vt(i,a)&&(void 0!==a||e in t)||(t[e]=a)}function ct(t,e){for(var a=t.length;a--;)if(vt(t[a][0],e))return a;return-1}function ut(t,e,s,h,c,f,x){var p;if(h&&(p=f?h(t,c,f,x):h(t)),void 0!==p)return p;if(!Pt(t))return t;var d=At(t);if(d){if(p=function(t){var e=t.length,a=t.constructor(e);e&&"string"==typeof t[0]&&W.call(t,"index")&&(a.index=t.index,a.input=t.input);return a}(t),!e)return function(t,e){var a=-1,i=t.length;e||(e=Array(i));for(;++a<i;)e[a]=t[a];return e}(t,p)}else{var y=bt(t),g=y==i||y==n;if(Mt(t))return function(t,e){if(e)return t.slice();var a=new t.constructor(t.length);return t.copy(a),a}(t,e);if("[object Object]"==y||y==a||g&&!f){if(w(t))return f?t:{};if(p=function(t){return"function"!=typeof t.constructor||St(t)?{}:(e=O(t),Pt(e)?B(e):{});var e}(g?{}:t),!e)return function(t,e){return pt(t,gt(t),e)}(t,function(t,e){return t&&pt(e,Tt(e),t)}(p,t))}else{if(!u[y])return f?t:{};p=function(t,e,a,i){var n=t.constructor;switch(e){case"[object ArrayBuffer]":return xt(t);case"[object Boolean]":case"[object Date]":return new n(+t);case"[object DataView]":return function(t,e){var a=e?xt(t.buffer):t.buffer;return new t.constructor(a,t.byteOffset,t.byteLength)}(t,i);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return function(t,e){var a=e?xt(t.buffer):t.buffer;return new t.constructor(a,t.byteOffset,t.length)}(t,i);case o:return function(t,e,a){return S(e?a(v(t),!0):v(t),b,new t.constructor)}(t,i,a);case"[object Number]":case"[object String]":return new n(t);case"[object RegExp]":return function(t){var e=new t.constructor(t.source,l.exec(t));return e.lastIndex=t.lastIndex,e}(t);case r:return function(t,e,a){return S(e?a(D(t),!0):D(t),m,new t.constructor)}(t,i,a);case"[object Symbol]":return s=t,it?Object(it.call(s)):{}}var s}(t,y,ut,e)}}x||(x=new lt);var A=x.get(t);if(A)return A;if(x.set(t,p),!d)var M=s?function(t){return function(t,e,a){var i=e(t);return At(t)?i:function(t,e){for(var a=-1,i=e.length,n=t.length;++a<i;)t[n+a]=e[a];return t}(i,a(t))}(t,Tt,gt)}(t):Tt(t);return function(t,e){for(var a=-1,i=t?t.length:0;++a<i&&!1!==e(t[a],a,t););}(M||t,(function(a,i){M&&(a=t[i=a]),ht(p,i,ut(a,e,s,h,i,t,x))})),p}function ft(t){return!(!Pt(t)||(e=t,L&&L in e))&&(_t(t)||w(t)?j:s).test(wt(t));var e}function xt(t){var e=new t.constructor(t.byteLength);return new I(e).set(new I(t)),e}function pt(t,e,a,i){a||(a={});for(var n=-1,o=e.length;++n<o;){var r=e[n],l=i?i(a[r],t[r],r,a,t):void 0;ht(a,r,void 0===l?t[r]:l)}return a}function dt(t,e){var a,i,n=t.__data__;return("string"==(i=typeof(a=e))||"number"==i||"symbol"==i||"boolean"==i?"__proto__"!==a:null===a)?n["string"==typeof e?"string":"hash"]:n.map}function yt(t,e){var a=function(t,e){return null==t?void 0:t[e]}(t,e);return ft(a)?a:void 0}nt.prototype.clear=function(){this.__data__=V?V(null):{}},nt.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},nt.prototype.get=function(t){var e=this.__data__;if(V){var a=e[t];return"__lodash_hash_undefined__"===a?void 0:a}return W.call(e,t)?e[t]:void 0},nt.prototype.has=function(t){var e=this.__data__;return V?void 0!==e[t]:W.call(e,t)},nt.prototype.set=function(t,e){return this.__data__[t]=V&&void 0===e?"__lodash_hash_undefined__":e,this},ot.prototype.clear=function(){this.__data__=[]},ot.prototype.delete=function(t){var e=this.__data__,a=ct(e,t);return!(a<0)&&(a==e.length-1?e.pop():R.call(e,a,1),!0)},ot.prototype.get=function(t){var e=this.__data__,a=ct(e,t);return a<0?void 0:e[a][1]},ot.prototype.has=function(t){return ct(this.__data__,t)>-1},ot.prototype.set=function(t,e){var a=this.__data__,i=ct(a,t);return i<0?a.push([t,e]):a[i][1]=e,this},rt.prototype.clear=function(){this.__data__={hash:new nt,map:new(G||ot),string:new nt}},rt.prototype.delete=function(t){return dt(this,t).delete(t)},rt.prototype.get=function(t){return dt(this,t).get(t)},rt.prototype.has=function(t){return dt(this,t).has(t)},rt.prototype.set=function(t,e){return dt(this,t).set(t,e),this},lt.prototype.clear=function(){this.__data__=new ot},lt.prototype.delete=function(t){return this.__data__.delete(t)},lt.prototype.get=function(t){return this.__data__.get(t)},lt.prototype.has=function(t){return this.__data__.has(t)},lt.prototype.set=function(t,e){var a=this.__data__;if(a instanceof ot){var i=a.__data__;if(!G||i.length<199)return i.push([t,e]),this;a=this.__data__=new rt(i)}return a.set(t,e),this};var gt=N?A(N,Object):function(){return[]},bt=function(t){return z.call(t)};function mt(t,e){return!!(e=null==e?9007199254740991:e)&&("number"==typeof t||c.test(t))&&t>-1&&t%1==0&&t<e}function St(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||T)}function wt(t){if(null!=t){try{return E.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function vt(t,e){return t===e||t!=t&&e!=e}(F&&"[object DataView]"!=bt(new F(new ArrayBuffer(1)))||G&&bt(new G)!=o||q&&"[object Promise]"!=bt(q.resolve())||Z&&bt(new Z)!=r||U&&"[object WeakMap]"!=bt(new U))&&(bt=function(t){var e=z.call(t),a="[object Object]"==e?t.constructor:void 0,i=a?wt(a):void 0;if(i)switch(i){case J:return"[object DataView]";case K:return o;case Q:return"[object Promise]";case tt:return r;case et:return"[object WeakMap]"}return e});var At=Array.isArray;function Dt(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}(t.length)&&!_t(t)}var Mt=H||function(){return!1};function _t(t){var e=Pt(t)?z.call(t):"";return e==i||e==n}function Pt(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function Tt(t){return Dt(t)?st(t):function(t){if(!St(t))return $(t);var e=[];for(var a in Object(t))W.call(t,a)&&"constructor"!=a&&e.push(a);return e}(t)}t.exports=function(t){return ut(t,!0,!0)}}));function u(t,e,a,i,n=!1){var o;a[i]||0===a[i]||""===a[i]||"boolean"==typeof a[i]?n?s(t[e])?Object.keys(t[e]).forEach(o=>{u(t[e],o,a[i],o,n)}):(o=t[e],"[object Array]"===Object.prototype.toString.call(o)?"series"==e?t[e].forEach((e,i)=>{Object.keys(e).forEach(e=>{u(t[e][i],e,a[e][i],e,!0)})}):a[i]=[].concat(t[e]):a[i]=t[e]):s(a[i])&&s(t[e])&&Object.keys(t[e]).forEach(o=>{u(t[e],o,a[i],o,n)}):a[i]=c(t[e])}function f(){let{config:t,opts:e}=this;u(t,"animation",e,"animation"),u(t,"animationDuration",e,"animationDuration"),u(t,"animationTiming",e,"animationTiming"),u(t,"backgroundColor",e,"backgroundColor"),u(t,"colors",e,"colors"),u(t,"padding",e,"padding"),u(t,"legend",e,"legend"),u(t,"tooltip",e,"tooltip"),e.series.forEach(a=>{switch(a.type){case"bar":case"line":case"scatter":case"candlestick":case"k":case"heatmap":e.yAxis&&"category"==e.yAxis.type?u(t,"yAxisCategory",e,"yAxis"):u(t,"yAxisValue",e,"yAxis"),e.xAxis&&"value"==e.xAxis.type?u(t,"xAxisValue",e,"xAxis"):u(t,"xAxisCategory",e,"xAxis");case"radar":u(t,"radarAxis",e,"radarAxis")}}),x.call(this)}function x(){let{config:t,opts:e}=this;e.series.forEach((a,i)=>{switch(u(t,"label",a,"label"),a.type){case"bar":case"line":case"pie":case"radar":case"scatter":case"funnel":case"heatmap":case"treemap":case"tagCloud":u(t,a.type,e.series,i);break;case"candlestick":case"k":u(t,"candlestick",e.series,i)}})}function p(t){const{x:e,y:a}=t,{opts:i,chartData:n}=this,{yAxis:o,xAxis:r}=i,{xStart:l,xEnd:s,yStart:h,yEnd:c,xAxisLabelPoint:u,yAxisLabelPoint:f}=n.axisData;let x=-1;return e<s&&e>l&&a>c&&a<h&&("category"==r.type&&"value"==o.type?(u.forEach((t,a)=>{e+(Math.abs(i._scrollDistance_)||0)>t.x&&(x=a)}),e<u[0].x&&(x=0)):"value"==r.type&&"category"==o.type?(f.forEach((t,e)=>{a<t.y&&(x=e)}),a>f[0].y&&(x=0)):"value"==r.type&&o.type),x}function d(t){const{x:e,y:a}=t,{data:i,center:n,radius:o,offsetAngle:r}=this.chartData.chartPie,[l,s]=n,[h,c]=o,u=Math.hypot(Math.abs(e-l),Math.abs(a-s));let f=-1;if(u>=h&&u<=c){let t;t=Math.atan2(e-l,s-a)>0?Math.atan2(e-l,s-a):Math.PI+(Math.PI-Math.abs(Math.atan2(e-l,s-a))),t>(90+r)*Math.PI/180?t-=(90+r)*Math.PI/180:t=2*Math.PI+(t-(90+r)*Math.PI/180),i.forEach((e,a)=>{t>e._start_&&(f=a)})}return f}function y(t){const{x:e,y:a}=t,{chartRadar:i,radarAxis:n}=this.chartData,{center:o,radius:r}=n,[l,s]=o,h=Math.hypot(Math.abs(e-l),Math.abs(a-s));let c=-1;if(h<=r){let t;t=Math.atan2(e-l,s-a)>0?Math.atan2(e-l,s-a):Math.PI+(Math.PI-Math.abs(Math.atan2(e-l,s-a))),i[0].dataPosition.forEach((e,a,i)=>{const{spacingRadian:n,_start_:o}=e;0==a?(t>=2*Math.PI-n/2||t<o+n/2)&&(c=0):t>o-n/2&&t<=o+n/2&&(c=i.length-a)})}return c}function g(){const{opts:t,tooltipData:e}=this,{width:a,height:i,tooltip:n}=t,{padding:o,itemGap:r,iconRadius:l,iconGap:s,textStyle:h}=n,{offset:c,data:u,maxTextWidth:f,tooltipTitle:x}=e,{x:p,y:d}=c;let y,g,b=2*o+2*l+s+f,m=2*o;x&&(m+=h.lineHeight+r),u.forEach((t,e,a)=>{if("candlestick"==t.type||"k"==t.type){const e=t.volumn?6:5;m+=h.lineHeight*e+r*e-1}else e+1==a.length?m+=h.lineHeight:m+=h.lineHeight+r}),y=p+10+b<=a?p+10:p-10-b>=0?p-10-b:p<=a/2?a-b:0,g=d+10+m<=i?d+10:d-10-m>=0?d-10-m:d<=i/2?i-m:0,this.tooltipData={...this.tooltipData,tooltipX:y,tooltipY:g,tooltipWidth:b,tooltipHeight:m,tooltipTitle:x},this.tooltipData}function b(t){if(-1==t)return;const{context:e,opts:a,chartData:i,tooltipData:n}=this,{xAxis:o,yAxis:r,tooltip:l}=a,{x:s,y:h}=n.offset,{type:c,cross:u}=l.axisPointer,{fontPadding:f}=u,{xStart:x,xEnd:p,xEachSpacing:d,xSpacing:y,xDataRange:g,xMinData:b,yStart:m,yEnd:S,yEachSpacing:w,ySpacing:v,yDataRange:A,yMinData:D,xAxisLabelPoint:M,yAxisLabelPoint:_}=i.axisData,P=n.data.some(t=>"candlestick"==t.type||"k"==t.type);let T,k,L,E,W,z;if("category"==o.type&&"value"==r.type?(T=M[t].text,k=M[t].x,L=M[t].y):"value"==o.type&&"category"==r.type?(T=_[t].text,k=_[t].x,L=_[t].y):"value"==o.type&&r.type,"category"==o.type&&"value"==r.type){const a=(D+(m-h)*A/v).toFixed(2),i=_[0].x,n=r.axisLabel.textStyle.fontSize,s=o.axisLabel.textStyle.fontSize;e.font=n+"px";const u=e.measureText(a).width+2*f,y=n+2*f;e.font=s+"px";const g=e.measureText(T).width+2*f,b=s+2*f;this.tooltipData.offset={x:k,y:h},e.font=l.textStyle.fontSize+"px",this.tooltipData.maxTextWidth=Math.max(this.tooltipData.maxTextWidth,e.measureText(T).width),this.tooltipData.tooltipTitle=T,E={yAxisLabel:a,yAxisLabelWidth:u,yAxisLabelHeight:y,yAxisLabelX:i,yAxisLabelY:h,yAxisLineX0:x,yAxisLineY0:h,yAxisLineX1:p,yAxisLineY1:h,xAxisLabel:T,xAxisLabelWidth:g,xAxisLabelHeight:b,xAxisLabelX:k,xAxisLabelY:L,xAxisLineX0:k,xAxisLineY0:m,xAxisLineX1:k,xAxisLineY1:S},"line"==c?W={x0:k,y0:m,x1:k,y1:S}:"shadow"==c&&(W={x:x+d*t,y:S,width:d,height:v})}else if("value"==o.type&&"category"==r.type){const a=(b+(s-x)*g/y).toFixed(2),i=M[0].y,n=r.axisLabel.textStyle.fontSize,h=o.axisLabel.textStyle.fontSize;e.font=n+"px";const u=e.measureText(T).width+2*f,d=n+2*f;e.font=h+"px";const v=e.measureText(a).width+2*f,A=h+2*f;this.tooltipData.offset={x:s,y:L},e.font=l.textStyle.fontSize+"px",this.tooltipData.maxTextWidth=Math.max(this.tooltipData.maxTextWidth,e.measureText(T).width),this.tooltipData.tooltipTitle=T,E={yAxisLabel:T,yAxisLabelWidth:u,yAxisLabelHeight:d,yAxisLabelX:k,yAxisLabelY:L,yAxisLineX0:x,yAxisLineY0:L,yAxisLineX1:p,yAxisLineY1:L,xAxisLabel:a,xAxisLabelWidth:v,xAxisLabelHeight:A,xAxisLabelX:s,xAxisLabelY:i,xAxisLineX0:s,xAxisLineY0:m,xAxisLineX1:s,xAxisLineY1:S},"line"==c?z={x0:x,y0:L,x1:p,y1:L}:"shadow"==c&&(z={x:x,y:S-w*t,width:y,height:w})}else"value"==o.type&&r.type;P&&i.chartCandlestick.bar&&(E.xAxisLineY0=i.chartCandlestick.bar.lineStartY),n.axisPointerData={crossPointer:E,xAxisPointer:W,yAxisPointer:z}}function m(t){if(-1==t)return;const{context:e,opts:a,chartData:i,tooltipData:n}=this;let o=n.maxTextWidth;e.font=a.tooltip.textStyle.fontSize+"px",i.chartLine.forEach(a=>{const{type:i,name:r,data:l,itemStyle:s,label:h,symbol:c}=a,{type:u,size:f,color:x}=c,{x:p,y:d,data:y}=l[t];if("number"!=typeof y)return;const g=`${r}: ${h.format?h.format(y):y}`,b=e.measureText(g).width;o=Math.max(o,b),n.data.push({type:i,name:r,text:g,color:s.color,x:p,y:d,symbolType:u,symbolSize:f,symbolColor:x})}),this.tooltipData.maxTextWidth=o}function S(t){if(-1==t)return;const{context:e,opts:a,chartData:i,tooltipData:n}=this;let o=n.maxTextWidth;e.font=a.tooltip.textStyle.fontSize+"px",i.chartBar[t].forEach(t=>{t.forEach(t=>{const{type:a,name:i,data:r,itemStyle:l,label:s}=t,h=`${i}: ${s.format?s.format(r):r}`,c=e.measureText(h).width;o=Math.max(o,c),n.data.push({type:a,name:i,text:h,color:l.color})})}),this.tooltipData.maxTextWidth=o}function w(t){if(-1==t)return;const{context:e,opts:a,chartData:i,tooltipData:n}=this,{name:o,type:r,data:l,center:s,radius:h,label:c}=i.chartPie,{name:u,value:f,itemStyle:x,_proportion_:p,_start_:d,_end_:y}=l[t],g=`${u}: ${c.format?c.format(f):f}`;e.font=a.tooltip.textStyle.fontSize+"px";const b=e.measureText(g).width,m=Math.max(n.maxTextWidth,b);this.tooltipData.maxTextWidth=m,this.tooltipData.tooltipTitle=o,n.data.push({type:r,name:u,text:g,color:x.color,center:s,radius:h,_proportion_:p,_start_:d,_end_:y})}function v(t){if(-1==t)return;const{context:e,opts:a,chartData:i,tooltipData:n}=this;let o=n.maxTextWidth;e.font=a.tooltip.textStyle.fontSize+"px",i.chartRadar.forEach(a=>{const{type:i,name:r,data:l,dataPosition:s,itemStyle:h,label:c,symbol:u}=a,{type:f,size:x,color:p}=u,d=l[t],{x:y,y:g}=s[t].position;if("number"!=typeof d)return;const b=`${r}: ${c.format?c.format(d):d}`,m=e.measureText(b).width;o=Math.max(o,m),n.data.push({type:i,name:r,text:b,color:h.color,x:y,y:g,symbolType:f,symbolSize:x,symbolColor:p})}),this.tooltipData.maxTextWidth=o,this.tooltipData.tooltipTitle=i.radarAxis.namePosition[t].text}function A(t){if(-1==t)return;const{context:e,opts:a,chartData:i,tooltipData:n}=this,{type:o,name:r,rect:l}=i.chartCandlestick,{start:s,end:h,low:c,high:u,volumn:f,color:x}=l[t],p="开盘价："+s,d="收盘价："+h,y="最低价："+c,g="最高价："+u,b="成交量："+f;let m,S=n.maxTextWidth;e.font=a.tooltip.textStyle.fontSize+"px",m=e.measureText(p).width,S=Math.max(S,m),m=e.measureText(d).width,S=Math.max(S,m),m=e.measureText(y).width,S=Math.max(S,m),m=e.measureText(g).width,S=Math.max(S,m),f&&(m=e.measureText(b).width,S=Math.max(S,m)),n.data.push({type:o,name:r,start:p,end:d,low:y,high:g,volumn:b,color:x}),this.tooltipData.maxTextWidth=S}function D(){let t={};this.opts.series.forEach(e=>{"candlestick"==e.type||"k"==e.type?(t.candlestick||(t.candlestick=[]),t.candlestick.push(e)):"funnel"==e.type?(t.funnel||(t.funnel=[]),e.data.sort((t,a)=>"ascending"==e.sort?t.value-a.value:a.value-t.value),t.funnel.push(e)):(t[e.type]||(t[e.type]=[]),t[e.type].push(e))}),this.seriesMap=t,this.seriesMap}function M(){let{colors:t,series:e}=this.opts;e.forEach((e,a)=>{"pie"==e.type||"funnel"==e.type||"treemap"==e.type||"tagCloud"==e.type?e.data.forEach((e,a)=>{e.itemStyle=e.itemStyle||{},e.itemStyle.color||(e.itemStyle.color=t[a%t.length])}):(e.itemStyle=e.itemStyle||{},e.itemStyle.color||(e.itemStyle.color=t[a%t.length]))})}function _(){if(this.opts.legend.show){let{context:t,opts:e}=this,{width:a,padding:i,legend:n,series:o}=e,{type:r,data:l,shapeWidth:s,shapeHeight:h,shapeRadius:c,itemGap:u,marginTop:f,textStyle:x}=n,{fontSize:p,padding:d}=x,y=0,g=0,b=[],m=[],S=s,w=[],v=a-i[1]-i[3],A=[],D=o.some(t=>(A=t,"pie"==t.type||"funnel"==t.type||"treemap"==t.type||"tagCloud"==t.type));w=l||(D?A.data.map(t=>t.name):o.map(t=>t.name)),t.font=p+"px",D?w.forEach(t=>{A.data.forEach(e=>{if(t==e.name){let t;if("default"==r)switch(A.type){case"pie":t="circle",S=2*c;break;case"funnel":case"treemap":case"tagCloud":t="rect"}let{name:a,itemStyle:i}=e,n=this.context.measureText(a||"").width,o=S+d+u+n,l={legendType:t,name:a,measureText:n,color:i.color};g+o>v?(b.push(m),g=o,m=[l]):(g+=o,y=Math.max(y,g),m.push(l))}})}):w.forEach(t=>{o.forEach(e=>{let a;if("default"==r)switch(e.type){case"bar":case"radar":case"candlestick":case"k":case"heatmap":a="rect";break;case"line":a="line";break;case"scatter":a="circle",S=2*c}if(t==e.name){let{name:t,itemStyle:i}=e,n=this.context.measureText(t||"").width,o=S+d+u+n,r={legendType:a,name:t,measureText:n,color:i.color};g+o>v?(b.push(m),g=o,m=[r]):(g+=o,y=Math.max(y,g),m.push(r))}})}),m.length&&b.push(m),this.legendData={legendList:b,legendWidth:y-u,legendHeight:b.length*Math.max(h,p)+(b.length-1)*u+f}}else this.legendData={legendList:[],legendWidth:0,legendHeight:0};this.legendData}function P(){let t=c(this.seriesMap),{context:e,opts:a,legendData:i,chartData:n}=this,{width:o,height:r,padding:l,xAxis:s,yAxis:h}=a,{show:u,type:f,data:x,boundaryGap:p,max:d,min:y,splitNumber:g,axisName:b,axisLabel:m,axisTick:S,axisLine:w,axisSplitLine:v}=s,{show:A,type:D,data:M,boundaryGap:_,max:P,min:T,splitNumber:k,axisName:L,axisLabel:E,axisTick:W,axisLine:z,axisSplitLine:j}=h,{show:X,textStyle:Y,gap:I,text:O}=b,{show:B,textStyle:C,gap:R,rotate:N,showIndex:H,format:$}=m,{show:F,lineStyle:G,length:q,alignWithLabel:Z,showIndex:U}=S,{show:V,lineStyle:J}=w,{show:K,lineStyle:Q,alignWithLabel:tt,showIndex:et}=v,{show:at,textStyle:it,gap:nt,text:ot}=L,{show:rt,textStyle:lt,gap:st,showIndex:ht,format:ct}=E,{show:ut,lineStyle:ft,length:xt,alignWithLabel:pt,showIndex:dt}=W,{show:yt,lineStyle:gt}=z,{show:bt,lineStyle:mt,alignWithLabel:St,showIndex:wt}=j,{fontSize:vt}=Y,{fontSize:At}=C,{lineWidth:Dt}=G,{lineWidth:Mt}=J,{lineWidth:_t}=Q,{fontSize:Pt}=it,{fontSize:Tt}=lt,{lineWidth:kt}=ft,{lineWidth:Lt}=gt,{lineWidth:Et}=mt,Wt=l[3],zt=o-l[1],jt=r-l[2]-i.legendHeight,Xt=l[0];if(t.candlestick&&t.candlestick.length){let{show:e,height:a,margin:i,lineStyle:n}=t.candlestick[0].bar,{lineWidth:o}=n;e&&(jt-=a+i+o)}let Yt,It,Ot,Bt,Ct,Rt,Nt,Ht,$t,Ft,Gt,qt,Zt,Ut,Vt,Jt,Kt=Wt,Qt=jt,te=!0,ee=!0;function ae(e="x"){let a={},i={},n=[];Object.keys(t).forEach(e=>{"bar"==e&&(t.bar.forEach(t=>{t.stack?(a[t.stack]||(a[t.stack]=[]),a[t.stack].push(t.data)):(a[t.name]||(a[t.name]=[]),a[t.name].push(t.data))}),i[e]=a),"line"!=e&&"scatter"!=e&&"candlestick"!=e||t[e].forEach(t=>{i[e]||(i[e]=[]),i[e].push(t.data)})}),Object.keys(i).forEach(t=>{"bar"==t&&Object.keys(i[t]).forEach(t=>{if(a[t].length>1){let e=a[t].reduce((t,e)=>(0==t.length?t=e:e.forEach((e,a)=>{t[a]+=e}),t),[]);n=n.concat(e)}else{let e=a[t][0];n=n.concat(e)}}),"line"==t&&i[t].forEach(t=>{let e=t.reduce((t,e)=>(e=Number(e),isNaN(e)||"number"!=typeof e||t.push(e),t),[]);n=n.concat(e)}),"scatter"==t&&i[t].forEach(t=>{let a=t.reduce((t,a)=>(a=Number(a[e]),isNaN(a)||"number"!=typeof a||t.push(a),t),[]);n=n.concat(a)}),"candlestick"==t&&i[t].forEach(t=>{let e=t.reduce((t,e)=>(e=Number(e[2]),isNaN(e)||"number"!=typeof e||t.push(e),t),[]);n=n.concat(e)})});let o=[],r="x"==e?g:k,l="x"==e?d:P,s="x"==e?y:T,h=Math.max(...n),c=Math.min(...n),u=0,f=0,x=1,p=1;l="auto"==l?l:Number(l),s="auto"==s?s:Number(s),"auto"==l||"auto"==s?(h="auto"==l?h<=0&&c<=0?0:h:l,c="auto"==s?h>=0&&c>=0?0:c:s,u=h-c):(h=l,c=s,u=h-c);let b=!(h>0&&c<0);for(x=u>=1e4?1e3:u>=1e3?100:u>=100?10:u>=10?5:u>=1?.1:u>=.1?.01:u>=.01?.001:u>=.001?1e-4:1e-5;x<1;)x*=10,p*=10;if("auto"==l&&"auto"==s)if(h>=0&&c>=0){for(u*=p,f=Math.ceil(u/r);f%x!=0;)f+=1;f/=p,u=f*r,h=c+u}else if(h<=0&&c<=0){for(u*=p,f=Math.floor(u/r);f%x!=0;)f+=1;f/=p,u=f*r,c=h-u}else{for(u*=p,f=Math.ceil(u/r);f%x!=0;)f+=1;f/=p,o.push(0);let t=0;for(;t<h;)t+=f,o.push(t);for(h=t,t=0;t>c;)t-=f,o.unshift(t);c=t,u=h-c}if("auto"==l&&"number"==typeof s)if(h>=0&&c>=0){for(u*=p,f=Math.ceil(u/r);f%x!=0;)f+=1;f/=p,u=f*r,h=c+u}else if(h<=0&&c<=0)u*=p,f=Number((u/r).toFixed(2)),f/=p,u=f*r;else{for(u*=p,f=Math.ceil(u/r);f%x!=0;)f+=1;f/=p,o.push(0);let t=0;for(;t<h;)t+=f,o.push(t);for(h=t,t=0;t-f>c;)t-=f,o.unshift(t);o.unshift(c),u=h-c}if("number"==typeof l&&"auto"==s)if(h>=0&&c>=0)u*=p,f=Number((u/r).toFixed(2)),f/=p,u=f*r;else if(h<=0&&c<=0){for(u*=p,f=Math.floor(u/r);f%x!=0;)f+=1;f/=p,u=f*r,c=h-u}else{for(u*=p,f=Math.ceil(u/r);f%x!=0;)f+=1;f/=p,o.push(0);let t=0;for(;t+f<h;)t+=f,o.push(t);for(o.push(h),t=0;t>c;)t-=f,o.unshift(t);c=t,u=h-c}if("number"==typeof l&&"number"==typeof s)if(h>=0&&c>=0)f=Number((u/r).toFixed(2));else if(h<=0&&c<=0)f=Number((u/r).toFixed(2));else{for(u*=p,f=Math.ceil(u/r);f%x!=0;)f+=1;f/=p,o.push(0);let t=0;for(;t+f<h;)t+=f,o.push(t);for(o.push(h),t=0;t-f>c;)t-=f,o.unshift(t);o.unshift(c),u=h-c}if(b)for(let t=0;t<=r;t++){let e=c+f*t;e=e.toFixed(p.toString().length-1),o.push(Number(e))}return"x"==e?(Jt=u,Ut=h,Vt=c,ee=b):(Zt=u,Gt=h,qt=c,te=b),o}n.axisData={xStart:null,xEnd:null,yStart:null,yEnd:null,yIsSamePart:null,xIsSamePart:null,yZero:null,yPlusSpacing:null,yMinusSpacing:null,ySpacing:null,yEachSpacing:null,xZero:null,xPlusSpacing:null,xMinusSpacing:null,xSpacing:null,xEachSpacing:null,yMaxData:null,yMinData:null,yDataRange:null,xMaxData:null,xMinData:null,xDataRange:null,xAxisLabelPoint:[],xAxisTickPoint:[],xAxisLinePoint:{},xAxisSplitLinePoint:[],xAxisNamePoint:{},yAxisLabelPoint:[],yAxisTickPoint:[],yAxisLinePoint:{},yAxisSplitLinePoint:[],yAxisNamePoint:{}};let ie="category"==f?x:ae("x"),ne="category"==D?M:ae("y");e.font=At+"px";let oe=0,re=0,le=ie.reduce((t,a,i)=>{let n=$?$(a):a;return oe=Math.max(e.measureText(n).width,oe),t.push(n),t},[]);N=Number(N),re=0==N?At:Math.abs(oe*Math.sin(N*Math.PI/180))+Math.abs(At*Math.cos(N*Math.PI/180)),e.font=Tt+"px";let se=0,he=ne.reduce((t,a,i)=>{let n=ct?ct(a):a;return se=Math.max(e.measureText(n).width,se),t.push(n),t},[]),ce=0;ce="category"==f&&p?ie.length:ie.length-1,ce=a.enableScroll?Math.min(5,ce):ce;let ue=0;if(ue="category"==D&&_?ne.length:ne.length-1,u&&B&&(jt-=re+R),u&&F&&("value"==D&&te||"category"==D)&&(jt-=q),te?u&&V&&(jt-=Mt/2):u&&K&&(jt-=_t/2),A&&at&&(Xt+=Pt+nt),Bt=jt-Xt,Ct=Math.floor(Bt/ue),Xt=jt-Ct*ue,Bt=jt-Xt,rt&&(Wt+=se+st),A&&ut&&("value"==f&&ee||"category"==f)&&(Wt+=xt),ee?A&&yt&&(Wt+=Lt/2):A&&bt&&(Wt+=Et/2),u&&X){e.font=vt+"px";let t=e.measureText(O).width;zt-=t+I}if($t=zt-Wt,Ft=Math.floor($t/ce),zt=Wt+Ft*ce,$t=zt-Wt,"value"!=D||te||ne.reduce((t,e,a)=>{if(0==a)t.push({y:jt});else{let e=Math.abs(ne[a-1]-ne[a])*Bt/Zt;t.push({y:t[a-1].y-e})}return 0==e&&(Yt=t[a].y),a+1==ne.length&&(Xt=t[a].y,Bt=jt-Xt,It=Yt-Xt,Ot=jt-Yt),t},[]),"value"!=f||ee||ie.reduce((t,e,a)=>{if(0==a)t.push({x:Wt});else{let e=Math.abs(ie[a]-ie[a-1])*$t/Jt;t.push({x:t[a-1].x+e})}return 0==e&&(Rt=t[a].x),a+1==ie.length&&(zt=t[a].x,$t=zt-Wt,Nt=zt-Rt,Ht=Rt-Wt),t},[]),"value"==D){let t=Kt;if(A&&rt&&(t+=se),n.axisData.yAxisLabelPoint=he.reduce((e,a,i)=>{if(0==i)e.push({text:a,x:t,y:jt});else{let n=Math.abs(ne[i-1]-ne[i])*Bt/Zt;e.push({text:a,x:t,y:e[i-1].y-n})}return e},[]),A&&bt){let t=Kt;A&&rt&&(t+=se+st),A&&ut&&ee&&(t+=xt),n.axisData.yAxisSplitLinePoint=n.axisData.yAxisLabelPoint.reduce((e,a,i)=>(e.push({startX:t,startY:a.y,endX:zt,endY:a.y}),e),[])}if(A&&ut){let t=Kt;ee?A&&rt&&(t+=se+st):t=Rt-Mt/2-xt,n.axisData.yAxisTickPoint=n.axisData.yAxisLabelPoint.reduce((e,a,i)=>(e.push({startX:t,startY:a.y,endX:t+xt,endY:a.y}),e),[])}if(A&&yt){let t=Kt;ee?(A&&rt&&(t+=se+st),A&&ut&&(t+=xt),t+=Lt/2):t=Rt,n.axisData.yAxisLinePoint={startX:t,startY:jt,endX:t,endY:Xt-kt/2}}if(A&&at){let t=Kt;ee?(A&&rt&&(t+=se+st),A&&ut&&(t+=xt),A&&yt&&(t+=Lt/2)):t=Rt,n.axisData.yAxisNamePoint={text:ot,x:t,y:Xt-nt}}}if("value"==f){let t=Qt;if(u&&B&&(t-=re),n.axisData.xAxisLabelPoint=le.reduce((e,a,i)=>{if(0==i)e.push({text:a,x:Wt,y:t});else{let n=Math.abs(ie[i]-ie[i-1])*$t/Jt;e.push({text:a,x:e[i-1].x+n,y:t})}return e},[]),u&&K){let t=Qt;u&&B&&(t-=re+R),u&&F&&te&&(t-=q),n.axisData.xAxisSplitLinePoint=n.axisData.xAxisLabelPoint.reduce((e,a,i)=>(e.push({startX:a.x,startY:t,endX:a.x,endY:Xt}),e),[])}if(u&&F){let t=Qt;te?A&&rt&&(t-=re+R):t=Yt+Mt/2+xt,n.axisData.xAxisTickPoint=n.axisData.xAxisLabelPoint.reduce((e,a,i)=>(e.push({startX:a.x,startY:t,endX:a.x,endY:t-q}),e),[])}if(u&&V){let t=Qt;te?(u&&B&&(t-=re+R),u&&F&&(t-=q),t-=Mt/2):t=Yt,n.axisData.xAxisLinePoint={startX:Wt,startY:t,endX:zt+Dt/2,endY:t}}if(u&&X){let t=Qt;ee?(u&&B&&(t-=re+R),u&&F&&(t-=q),u&&V&&(t-=Mt/2)):t=Yt,n.axisData.xAxisNamePoint={text:O,x:zt+I,y:t}}}if("category"==D){if(n.axisData.yAxisLabelPoint=he.reduce((t,e,a)=>{let i=Kt;return A&&rt&&(i+=se),_?t.push({show:!0,text:e,x:i,y:jt-Ct*(a+1)+Ct/2}):t.push({show:!0,text:e,x:i,y:jt-Ct*a}),t},[]),ht&&ht.length&&(n.axisData.yAxisLabelPoint=n.axisData.yAxisLabelPoint.map((t,e)=>{let a=ht.some(t=>t===e);return t.show=!!a,t})),A&&bt){let t=Kt;A&&rt&&(t+=se+st),A&&ut&&ee&&(t+=xt);let e=0;e=_?St?ne.length:ne.length+1:ne.length;for(let a=0;a<e;a++)_&&St?n.axisData.yAxisSplitLinePoint.push({show:!0,startX:t,startY:jt-Ct*a-Ct/2,endX:zt,endY:jt-Ct*a-Ct/2}):n.axisData.yAxisSplitLinePoint.push({show:!0,startX:t,startY:jt-Ct*a,endX:zt,endY:jt-Ct*a})}if(wt&&wt.length&&(n.axisData.yAxisSplitLinePoint=n.axisData.yAxisSplitLinePoint.map((t,e)=>{let a=wt.some(t=>t===e);return t.show=!!a,t})),A&&ut){let t=Kt;ee?A&&rt&&(t+=se+st):t=Rt-Lt/2-xt;let e=0;e=_?pt?ne.length:ne.length+1:ne.length;for(let a=0;a<e;a++)_&&pt?n.axisData.yAxisTickPoint.push({show:!0,startX:t,startY:jt-Ct*a-Ct/2,endX:t+xt,endY:jt-Ct*a-Ct/2}):n.axisData.yAxisTickPoint.push({show:!0,startX:t,startY:jt-Ct*a,endX:t+xt,endY:jt-Ct*a})}if(dt&&dt.length&&(n.axisData.yAxisTickPoint=n.axisData.yAxisTickPoint.map((t,e)=>{let a=dt.some(t=>t===e);return t.show=!!a,t})),A&&yt){let t=Kt;ee?(A&&rt&&(t+=se+st),A&&ut&&(t+=xt),t+=Lt/2):t=Rt,n.axisData.yAxisLinePoint={startX:t,startY:jt,endX:t,endY:Xt-kt/2}}if(A&&at){let t=Kt;ee?(A&&rt&&(t+=se+st),A&&ut&&(t+=xt),A&&yt&&(t+=Lt/2)):t=Rt,n.axisData.yAxisNamePoint={text:ot,x:t,y:Xt-nt}}}if("category"==f){if(n.axisData.xAxisLabelPoint=le.reduce((t,e,a)=>{let i=Qt;return u&&B&&(i-=re),p?t.push({show:!0,text:e,x:Wt+Ft*(a+1)-Ft/2,y:i}):t.push({show:!0,text:e,x:Wt+Ft*a,y:i}),t},[]),H&&H.length&&(n.axisData.xAxisLabelPoint=n.axisData.xAxisLabelPoint.map((t,e)=>{let a=H.some(t=>t===e);return t.show=!!a,t})),u&&K){let t=Qt;u&&B&&(t-=re+R),u&&F&&te&&(t-=q);let e=0;e=p?tt?ie.length:ie.length+1:ie.length;for(let a=0;a<e;a++)p&&tt?n.axisData.xAxisSplitLinePoint.push({show:!0,startX:Wt+Ft*a+Ft/2,startY:t,endX:Wt+Ft*a+Ft/2,endY:Xt}):n.axisData.xAxisSplitLinePoint.push({show:!0,startX:Wt+Ft*a,startY:t,endX:Wt+Ft*a,endY:Xt})}if(et&&et.length&&(n.axisData.xAxisSplitLinePoint=n.axisData.xAxisSplitLinePoint.map((t,e)=>{let a=et.some(t=>t===e);return t.show=!!a,t})),u&&F){let t=Qt;te?(u&&B&&(t-=re+R),u&&F&&(t-=q)):t=Yt-Mt/2;let e=0;e=p?Z?ie.length:ie.length+1:ie.length;for(let a=0;a<e;a++)p&&Z?n.axisData.xAxisTickPoint.push({show:!0,startX:Wt+Ft*a+Ft/2,startY:t,endX:Wt+Ft*a+Ft/2,endY:t+q}):n.axisData.xAxisTickPoint.push({show:!0,startX:Wt+Ft*a,startY:t,endX:Wt+Ft*a,endY:t+q})}if(U&&U.length&&(n.axisData.xAxisTickPoint=n.axisData.xAxisTickPoint.map((t,e)=>{let a=U.some(t=>t===e);return t.show=!!a,t})),u&&V){let t=Qt;te?(u&&B&&(t-=re+R),u&&F&&(t-=q),t-=Mt/2):t=Yt,n.axisData.xAxisLinePoint={startX:Wt,startY:t,endX:zt+Dt/2,endY:t}}if(u&&X){let t=Qt;te?(u&&B&&(t-=re+R),u&&F&&(t-=q),u&&V&&(t-=Mt/2)):t=Yt,n.axisData.xAxisNamePoint={text:O,x:zt+I,y:t}}}n.axisData.xStart=Wt,n.axisData.xEnd=zt,n.axisData.yStart=jt,n.axisData.yEnd=Xt,n.axisData.yIsSamePart=te,n.axisData.xIsSamePart=ee,n.axisData.yZero=Yt,n.axisData.yPlusSpacing=It,n.axisData.yMinusSpacing=Ot,n.axisData.ySpacing=Bt,n.axisData.yEachSpacing=Ct,n.axisData.xZero=Rt,n.axisData.xPlusSpacing=Nt,n.axisData.xMinusSpacing=Ht,n.axisData.xSpacing=$t,n.axisData.xEachSpacing=Ft,n.axisData.yMaxData=Gt,n.axisData.yMinData=qt,n.axisData.yDataRange=Zt,n.axisData.xMaxData=Ut,n.axisData.xMinData=Vt,n.axisData.xDataRange=Jt,this.chartData.axisData}function T(){let{context:t,opts:e,legendData:a,chartData:i}=this,{width:n,height:r,padding:s,radarAxis:h,categories:c}=e,{center:u,radius:f,splitNumber:x,axisName:p}=h,{show:d,textStyle:y}=p,{fontSize:g,margin:b}=y,[m,S]=u;i.radarAxis={center:[],radius:0,lineEndPosition:[],namePosition:[],axisNameStyle:y},"string"==typeof m&&(m=n*l(m)),"string"==typeof S&&(S=(r-a.legendHeight-s[2])*l(S)),"string"==typeof f&&(f=(r-a.legendHeight-s[2])*l(f)/2),i.radarAxis.center=[m,S],i.radarAxis.radius=f;let w=2*Math.PI/c.length,v=Math.PI/2,A=[];for(let t=0;t<x;t++){let e=(x-t)/x;A[t]=c.reduce((t,a,n)=>{let r={x:f*Math.cos(v+w*n)*e,y:f*Math.sin(v+w*n)*e};return t.push(o(r,i.radarAxis.center)),t},[])}i.radarAxis.lineEndPosition=A,i.radarAxis.namePosition=c.reduce((e,a,n)=>{let r={x:(f+g/2+b)*Math.cos(v+w*n),y:(f+g/2+b)*Math.sin(v+w*n)},l=o(r,i.radarAxis.center);return t.font=g+"px",e.push({text:a,point:r,position:l}),e},[]),this.chartData.radarAxis}function k(){let{opts:t,chartData:e,seriesMap:a}=this,{xAxis:i,yAxis:n}=t,o=a.bar,{xStart:r,xEnd:l,yStart:s,yEnd:h,yZero:u,yPlusSpacing:f,yMinusSpacing:x,ySpacing:p,yEachSpacing:d,xZero:y,xPlusSpacing:g,xMinusSpacing:b,xSpacing:m,xEachSpacing:S,yMaxData:w,yMinData:v,yDataRange:A,xMaxData:D,xMinData:M,xDataRange:_,xAxisLabelPoint:P,yAxisLabelPoint:T}=e.axisData,k=0,L=0,E=0,W="value"==i.type?D:w,z="value"==i.type?M:v,j="value"==i.type?_:A,X="value"==i.type?g:f,Y="value"==i.type?b:x,I="value"==i.type?m:p,O="category"==i.type?S:d,B="category"==i.type?i.data:n.data,C={};o.forEach(t=>{t.stack?(C[t.stack]||(C[t.stack]=[]),C[t.stack].push(t)):(C[t.name]||(C[t.name]=[]),C[t.name].push(t))});let R=[];for(let t=0,e=B.length;t<e;t++){let t=[];Object.keys(C).forEach(e=>{t.push(c(C[e]))}),R.push(t)}R.forEach((t,e)=>{t.forEach((t,a)=>{t.forEach((a,i)=>{let n=!0;if(a.showIndex&&a.showIndex.length&&(n=a.showIndex.some(t=>t==chartBarArrIndex)),a.show=n,a.data=a.data[e],0==i){let{barMaxWidth:t,barMinWidth:i,barWidth:n,barGap:o}=a;"number"==typeof n?(n>t&&(a.barWidth=t),n<i&&(a.barWidth=i),0==e&&(E+=a.barWidth)):0==e&&L++}else a.barWidth=t[0].barWidth}),0==e&&(E+=0==a?2*t[0].barGap:t[0].barGap)})}),k=E+L<O?(O-E)/L:1,R.forEach((t,e)=>{t.forEach((t,a)=>{t.forEach((a,i)=>{let{barMaxWidth:n,barWidth:o}=a;0==i&&"auto"==o?a.barWidth=k>n?n:k:(a.barWidth=t[0].barWidth,a.barGap=t[0].barGap),0==e&&0==i&&"auto"==o&&(E+=a.barWidth)})})}),"category"==i.type?R.forEach((t,e)=>{let a=P[e].x-E/2;t.forEach((t,e)=>{a+=t[0].barGap+t[0].barWidth/2;let i=0,n=0;W>=0&&z>=0?i=s:W<=0&&z<=0?n=h:(i=u,n=u),t.forEach((t,e)=>{t.x=a;let o=0;W>=0&&z>=0?0==t.data?(t.y=s,o=0):(t.y=i,o=I*(t.data-z)/j,i-=o):W<=0&&z<=0?0==t.data?(t.y=h,o=0):(t.y=n,o=I*(Math.abs(t.data)-Math.abs(W))/j,n+=o):t.data>0?(t.y=i,o=X*t.data/W,i-=o):t.data<0?(t.y=n,o=Y*Math.abs(t.data)/Math.abs(z),n+=o):(t.y=u,o=0),t.barHeight=o}),a+=t[0].barWidth/2})}):R.forEach((t,e)=>{let a=T[e].y+E/2;t.forEach((t,e)=>{a-=t[0].barGap+t[0].barWidth/2;let i=0,n=0;W>=0&&z>=0?i=r:W<=0&&z<=0?n=l:(i=y,n=y),t.forEach((t,e)=>{t.y=a;let o=0;W>=0&&z>=0?(t.x=i,o=I*(t.data-z)/j,i+=o):W<=0&&z<=0?(t.x=n,o=I*(Math.abs(t.data)-Math.abs(W))/j,n-=o):t.data>0?(t.x=i,o=X*t.data/W,i+=o):(t.x=n,o=Y*Math.abs(t.data)/Math.abs(z),n-=o),t.barHeight=o}),a-=t[0].barWidth/2})}),e.chartBar=R,e.chartBar}function L(){let{opts:t,chartData:e,seriesMap:a}=this,{xAxis:i}=t,n=c(a.line),{xStart:o,xEnd:r,yStart:l,yEnd:s,yZero:h,yPlusSpacing:u,yMinusSpacing:f,ySpacing:x,xZero:p,xPlusSpacing:d,xMinusSpacing:y,xSpacing:g,yMaxData:b,yMinData:m,yDataRange:S,xMaxData:w,xMinData:v,xDataRange:A,xAxisLabelPoint:D,yAxisLabelPoint:M}=e.axisData,_="value"==i.type?w:b,P="value"==i.type?v:m,T="value"==i.type?A:S,k="value"==i.type?d:u,L="value"==i.type?y:f,E="value"==i.type?g:x,W=[];W="category"==i.type?n.reduce((t,e)=>(e.data=e.data.reduce((t,e,a)=>{let i,n,o;return"number"==typeof e&&(i=D[a].x,e=(e=e>_?_:e)<P?P:e,_>=0&&P>=0?(o=E*(e-P)/T,n=l-o):_<=0&&P<=0?(o=E*(Math.abs(e)-Math.abs(_))/T,n=s+o):e>0?(o=k*e/_,n=h-o):(o=L*Math.abs(e)/Math.abs(P),n=h+o)),t.push({x:i,y:n,data:e,height:o}),t},[]),t.push(e),t),[]):n.reduce((t,e)=>(e.data=e.data.reduce((t,e,a)=>{let i,n,l;return"number"==typeof e&&(n=M[a].y,e=(e=e>_?_:e)<P?P:e,_>=0&&P>=0?(l=E*(e-P)/T,i=o+l):_<=0&&P<=0?(l=E*(Math.abs(e)-Math.abs(_))/T,i=r-l):e>0?(l=k*e/_,i=p+l):(l=L*Math.abs(e)/Math.abs(P),i=p-l)),t.push({x:i,y:n,data:e,height:l}),t},[]),t.push(e),t),[]),e.chartLine=W,e.chartLine}function E(){let{opts:t,legendData:e,seriesMap:a}=this,{width:i,height:n,padding:o}=t,r=c(a.pie[0]),{data:s,center:h,radius:u}=r,[f,x]=h,[p,d]=u,y=s.reduce((t,e)=>t+=null===e.value?0:e.value,0);"string"==typeof f&&(f=i*l(f)),"string"==typeof x&&(x=(n-e.legendHeight-o[2])*l(x)),"string"==typeof p&&(p=(n-e.legendHeight-o[2])*l(p)/2),"string"==typeof d&&(d=(n-e.legendHeight-o[2])*l(d)/2);let g=s.concat([]).sort((t,e)=>e.value-t.value);r.valueSum=y,r.center=[f,x],r.radius=[p,d],r.maxData=g[0].value,r.minData=g[g.length-1].value,this.chartData.chartPie=r,this.chartData.chartPie}function W(){let{opts:t,chartData:e,seriesMap:a}=this,{radarAxis:i,categories:n,series:o}=t,r=c(a.radar),{max:l}=i,{radius:s}=e.radarAxis,h=0;o.forEach(t=>{h=Math.max(h,...t.data)}),h="auto"==l?h:l;let u=2*Math.PI/n.length,f=Math.PI/2;r.forEach(t=>{t.dataPosition=t.data.reduce((t,e,a)=>{let i=e/h,n={x:s*Math.cos(f+u*a)*i,y:s*Math.sin(f+u*a)*i};return t.push({data:e,point:n,spacingRadian:u,_start_:u*a}),t},[])}),e.chartRadar=r,this.chartData.chartRadar}function z(){let{opts:t,chartData:e,seriesMap:a}=this,{xStart:o,xEnd:r,yStart:l,yEnd:s,yZero:h,yPlusSpacing:u,yMinusSpacing:f,ySpacing:x,xZero:p,xPlusSpacing:d,xMinusSpacing:y,xSpacing:g,yMaxData:b,yMinData:m,yDataRange:S,xMaxData:w,xMinData:v,xDataRange:A}=e.axisData,D=c(a.scatter);e.chartScatter=D.map(t=>{let e,a,c,D,M,_,P,T,k,{data:L,radius:E,itemStyle:W}=t,{color:z}=W;if("number"!=typeof E){e=E[1],a=E[0],c=e-a;let t=L.concat([]).sort((t,e)=>t.z-e.z);D=t[t.length-1].z,M=t[0].z?t[0].z:0,_=D-M}if("string"!=typeof z){let[e,a]=z;P=i(a),T=i(e),k=[P[0]-T[0],P[1]-T[1],P[2]-T[2]],t.label.color="#000000"}return t.data=L.concat([]).map(t=>{let e,i,{x:D,y:P,z:L}=t;if(i=b>=0&&b>=0?l-x*(P-m)/S:m<=0&&m<=0?s+x*(Math.abs(P)-Math.abs(b))/S:P>0?h-u*P/b:h+f*Math.abs(P)/Math.abs(m),e=w>=0&&w>=0?o+g*(D-v)/A:v<=0&&v<=0?r-g*(Math.abs(D)-Math.abs(w))/A:D>0?p+d*D/w:p-y*Math.abs(D)/Math.abs(v),t.positionX=e,t.positionY=i,"number"!=typeof E){t.z=L||0;let e=(L-M)/_;t.radius=a+c*e}else t.radius=E;if("string"!=typeof z){t.z=L||0;let e=(L-M)/_,a=[T[0]+k[0]*e,T[1]+k[1]*e,T[2]+k[2]*e];t.color=n(a)}else t.color=z;return t}),t}),this.chartData.chartScatter}function j(){let{opts:t,legendData:e,chartData:a,seriesMap:i}=this,{width:n,height:o,series:r,padding:s}=t,h=i.funnel[0],{data:c,width:u,height:f,top:x,left:p,right:d,bottom:y,max:g,min:b,gap:m,sort:S,shape:w,funnelAlign:v,label:A,itemStyle:D}=h,M=s[3],_=n-s[1],P=s[0],T=o-s[2]-e.legendHeight,k=_-M,L=T-P;g=g>100?100:g,b=b<0?0:b,M="auto"==p?M:M+k*l(p),_="auto"==d?_:_-k*l(d),P="auto"==x?P:P+L*l(x),T="auto"==y?T:T-L*l(y),k="auto"==u?_-M:k*l(u),L="auto"==f?T-P:L*l(f);let E,W,z=(L-(c.length-1)*m)/c.length;c.forEach(t=>{t.value=t.value>g?g:t.value,t.value=t.value<b?b:t.value,t.width=k*(t.value/g),t.height=z}),"left"==v?(E=M,W=P):"right"==v?(E=_,W=P):(E="ascending"==S?M+c[c.length-1].width/2-c[0].width/2:M,W=P),c.forEach((t,e)=>{let a,i=[];"left"==v?"descending"==S?e+1==c.length?"funnel"==w?(i.push({x:E,y:W}),i.push({x:E+t.width,y:W}),i.push({x:E+t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):"pyramid"==w&&(i.push({x:E,y:W}),i.push({x:E+t.width,y:W}),i.push({x:E,y:W+t.height})):(i.push({x:E,y:W}),i.push({x:E+t.width,y:W}),i.push({x:E+c[e+1].width,y:W+t.height}),i.push({x:E,y:W+t.height})):"ascending"==S&&(0==e?"funnel"==w?(i.push({x:E,y:W}),i.push({x:E+t.width,y:W}),i.push({x:E+t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):"pyramid"==w&&(i.push({x:E,y:W}),i.push({x:E+t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):(i.push({x:E,y:W}),i.push({x:E+c[e-1].width,y:W}),i.push({x:E+t.width,y:W+t.height}),i.push({x:E,y:W+t.height}))):"right"==v?"descending"==S?e+1==c.length?"funnel"==w?(i.push({x:E,y:W}),i.push({x:E-t.width,y:W}),i.push({x:E-t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):"pyramid"==w&&(i.push({x:E,y:W}),i.push({x:E-t.width,y:W}),i.push({x:E,y:W+t.height})):(i.push({x:E,y:W}),i.push({x:E-t.width,y:W}),i.push({x:E-c[e+1].width,y:W+t.height}),i.push({x:E,y:W+t.height})):"ascending"==S&&(0==e?"funnel"==w?(i.push({x:E,y:W}),i.push({x:E-t.width,y:W}),i.push({x:E-t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):"pyramid"==w&&(i.push({x:E,y:W}),i.push({x:E-t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):(i.push({x:E,y:W}),i.push({x:E-c[e-1].width,y:W}),i.push({x:E-t.width,y:W+t.height}),i.push({x:E,y:W+t.height}))):"descending"==S?e+1==c.length?"funnel"==w?(i.push({x:E,y:W}),i.push({x:E+t.width,y:W}),i.push({x:E+t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):"pyramid"==w&&(i.push({x:E,y:W}),i.push({x:E+t.width,y:W}),i.push({x:E+t.width/2,y:W+t.height})):(i.push({x:E,y:W}),i.push({x:E+t.width,y:W}),i.push({x:E+t.width/2+c[e+1].width/2,y:W+t.height}),i.push({x:E+t.width/2-c[e+1].width/2,y:W+t.height})):"ascending"==S&&(0==e?"funnel"==w?(i.push({x:E,y:W}),i.push({x:E+t.width,y:W}),i.push({x:E+t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):"pyramid"==w&&(i.push({x:E+t.width/2,y:W}),i.push({x:E+t.width,y:W+t.height}),i.push({x:E,y:W+t.height})):(i.push({x:E+t.width/2-c[e-1].width/2,y:W}),i.push({x:E+t.width/2+c[e-1].width/2,y:W}),i.push({x:E+t.width,y:W+t.height}),i.push({x:E,y:W+t.height}))),t.point=i,a="descending"==S?e+1==c.length?"funnel"==w?t.width:t.width/2:(t.width+c[e+1].width)/2:0==e?"funnel"==w?t.width:t.width/2:(t.width+c[e-1].width)/2,"inside"==A.position?t.textPoint="left"==v?{x:E+a/2,y:W+t.height/2}:"right"==v?{x:E-a/2,y:W+t.height/2}:{x:E+t.width/2,y:W+t.height/2}:t.textPoint="left"==v?{x:E+a+A.margin,y:W+t.height/2}:"right"==v?{x:E-a-A.margin,y:W+t.height/2}:{x:E+t.width/2+a/2+A.margin,y:W+t.height/2},e+1!==c.length&&("center"==v?(E=E+t.width/2-c[e+1].width/2,W=W+t.height+m):W=W+t.height+m)}),a.chartFunnel=h,this.chartData.chartFunnel}function X(){let{opts:t,chartData:e,legendData:a}=this,{height:i,padding:n,series:o}=t,r=o.filter(t=>"candlestick"==t.type||"k"==t.type);if(0==r.length)return;let l,s,h,c,{name:u,type:f,data:x,barMaxWidth:p,barMinWidth:d,barWidth:y,itemStyle:g,highLine:b,lowLine:m,bar:S}=r[0],{color:w,bordercolor:v,opacity:A,color0:D,bordercolor0:M,opacity0:_,borderWidth:P}=g,{show:T,lineStyle:k}=b,{show:L,lineStyle:E}=m,{show:W,height:z,margin:j,data:X,lineStyle:Y}=S,{xStart:I,xEnd:O,yStart:B,yEnd:C,yZero:R,yPlusSpacing:N,yMinusSpacing:H,ySpacing:$,xZero:F,xPlusSpacing:G,xMinusSpacing:q,xEachSpacing:Z,xSpacing:U,yMaxData:V,yMinData:J,yDataRange:K,xMaxData:Q,xMinData:tt,xDataRange:et,xAxisLabelPoint:at,yAxisLabelPoint:it}=e.axisData,nt=0,ot=1/0;if("auto"==y&&(y=Z>p?p:Z,y=y>3?y-2:y),l=x.reduce((t,e,a)=>{const[i,n,o,r,l]=e;nt=Math.max(n,nt),ot=Math.min(n,ot);let s={};return s.start=i,s.end=n,s.low=o,s.high=r,s.volumn=l,s.color=i>n?D:w,s.bordercolor=i>n?M:v,s.opacity=i>n?A:_,s.borderWidth=P,s.upLinePoint={startX:at[a].x,startY:B-$*(o-J)/K,endX:at[a].x,endY:B-$*(Math.max(i,n)-J)/K},s.downLinePoint={startX:at[a].x,startY:B-$*(r-J)/K,endX:at[a].x,endY:B-$*(Math.min(i,n)-J)/K},s.rectPoint={x:Math.floor(at[a].x-y/2),y:B-$*(Math.max(i,n)-J)/K,width:y,height:$*Math.abs(i-n)/K},t.push(s),t},[]),T&&(s={data:nt,startX:I,startY:B-$*(nt-J)/K,endX:O,endY:B-$*(nt-J)/K}),L&&(h={data:ot,startX:I,startY:B-$*(ot-J)/K,endX:O,endY:B-$*(ot-J)/K}),W){let t=Math.max(...X),e=Math.min(...X),o=t-e;c={data:[],lineStartX:I,lineStartY:null,lineEndX:O,lineEndY:null},c.data=l.reduce((t,r,l)=>{let{color:s,rectPoint:h}=r,{x:u,width:f}=h,x=i-n[2]-a.legendHeight-Y.lineWidth/2,p=.2*z+.8*z*(X[l]-e)/o;return t.push({color:s,x:u,y:x,width:f,height:p}),c.lineStartY=x,c.lineEndY=x,t},[])}this.chartData.chartCandlestick={type:f,name:u,rect:l,highLine:s,lowhLine:h,bar:c},this.chartData.chartCandlestick}function Y(){let{chartData:t,seriesMap:e}=this,{xStart:a,yStart:o,xEachSpacing:r,yEachSpacing:l}=t.axisData,s=c(e.heatmap);t.chartHeatmap=s.map(t=>{let e,s,h,c,u,f,{data:x,itemStyle:p}=t,{color:d,useSplit:y}=p,g=x.concat([]).sort((t,e)=>t[2]-e[2]);e=g[g.length-1][2],s=g[0][2],h=e-s;let[b,m]=d;return c=i(m),u=i(b),f=[c[0]-u[0],c[1]-u[1],c[2]-u[2]],t.data=x.map(t=>{let e,i,[c,x,p]=t;e=a+c*r,i=o-(x+1)*l,t.positionX=e,t.positionY=i,p=p||0;let d=(p-s)/h,g=[u[0]+f[0]*d,u[1]+f[1]*d,u[2]+f[2]*d];return t.color=n(g),t.useSplit=y,t}),t}),this.chartData.chartHeatmap}function I(t,e){return t.parent===e.parent?1:2}function O(t,e){return t+e.x}function B(t,e){return Math.max(t,e.y)}function C(t){var e=0,a=t.children,i=a&&a.length;if(i)for(;--i>=0;)e+=a[i].value;else e=1;t.value=e}function R(t,e){var a,i,n,o,r,l=new F(t),s=+t.value&&(l.value=t.value),h=[l];for(null==e&&(e=N);a=h.pop();)if(s&&(a.value=+a.data.value),(n=e(a.data))&&(r=n.length))for(a.children=new Array(r),o=r-1;o>=0;--o)h.push(i=a.children[o]=new F(n[o])),i.parent=a,i.depth=a.depth+1;return l.eachBefore($)}function N(t){return t.children}function H(t){t.data=t.data.data}function $(t){var e=0;do{t.height=e}while((t=t.parent)&&t.height<++e)}function F(t){this.data=t,this.depth=this.height=0,this.parent=null}F.prototype=R.prototype={constructor:F,count:function(){return this.eachAfter(C)},each:function(t){var e,a,i,n,o=this,r=[o];do{for(e=r.reverse(),r=[];o=e.pop();)if(t(o),a=o.children)for(i=0,n=a.length;i<n;++i)r.push(a[i])}while(r.length);return this},eachAfter:function(t){for(var e,a,i,n=this,o=[n],r=[];n=o.pop();)if(r.push(n),e=n.children)for(a=0,i=e.length;a<i;++a)o.push(e[a]);for(;n=r.pop();)t(n);return this},eachBefore:function(t){for(var e,a,i=this,n=[i];i=n.pop();)if(t(i),e=i.children)for(a=e.length-1;a>=0;--a)n.push(e[a]);return this},sum:function(t){return this.eachAfter((function(e){for(var a=+t(e.data)||0,i=e.children,n=i&&i.length;--n>=0;)a+=i[n].value;e.value=a}))},sort:function(t){return this.eachBefore((function(e){e.children&&e.children.sort(t)}))},path:function(t){for(var e=this,a=function(t,e){if(t===e)return t;var a=t.ancestors(),i=e.ancestors(),n=null;t=a.pop(),e=i.pop();for(;t===e;)n=t,t=a.pop(),e=i.pop();return n}(e,t),i=[e];e!==a;)e=e.parent,i.push(e);for(var n=i.length;t!==a;)i.splice(n,0,t),t=t.parent;return i},ancestors:function(){for(var t=this,e=[t];t=t.parent;)e.push(t);return e},descendants:function(){var t=[];return this.each((function(e){t.push(e)})),t},leaves:function(){var t=[];return this.eachBefore((function(e){e.children||t.push(e)})),t},links:function(){var t=this,e=[];return t.each((function(a){a!==t&&e.push({source:a.parent,target:a})})),e},copy:function(){return R(this).eachBefore(H)}};var G=Array.prototype.slice;function q(t){for(var e,a,i=0,n=(t=function(t){for(var e,a,i=t.length;i;)a=Math.random()*i--|0,e=t[i],t[i]=t[a],t[a]=e;return t}(G.call(t))).length,o=[];i<n;)e=t[i],a&&V(a,e)?++i:(a=K(o=Z(o,e)),i=0);return a}function Z(t,e){var a,i;if(J(e,t))return[e];for(a=0;a<t.length;++a)if(U(e,t[a])&&J(Q(t[a],e),t))return[t[a],e];for(a=0;a<t.length-1;++a)for(i=a+1;i<t.length;++i)if(U(Q(t[a],t[i]),e)&&U(Q(t[a],e),t[i])&&U(Q(t[i],e),t[a])&&J(tt(t[a],t[i],e),t))return[t[a],t[i],e];throw new Error}function U(t,e){var a=t.r-e.r,i=e.x-t.x,n=e.y-t.y;return a<0||a*a<i*i+n*n}function V(t,e){var a=t.r-e.r+1e-6,i=e.x-t.x,n=e.y-t.y;return a>0&&a*a>i*i+n*n}function J(t,e){for(var a=0;a<e.length;++a)if(!V(t,e[a]))return!1;return!0}function K(t){switch(t.length){case 1:return{x:(e=t[0]).x,y:e.y,r:e.r};case 2:return Q(t[0],t[1]);case 3:return tt(t[0],t[1],t[2])}var e}function Q(t,e){var a=t.x,i=t.y,n=t.r,o=e.x,r=e.y,l=e.r,s=o-a,h=r-i,c=l-n,u=Math.sqrt(s*s+h*h);return{x:(a+o+s/u*c)/2,y:(i+r+h/u*c)/2,r:(u+n+l)/2}}function tt(t,e,a){var i=t.x,n=t.y,o=t.r,r=e.x,l=e.y,s=e.r,h=a.x,c=a.y,u=a.r,f=i-r,x=i-h,p=n-l,d=n-c,y=s-o,g=u-o,b=i*i+n*n-o*o,m=b-r*r-l*l+s*s,S=b-h*h-c*c+u*u,w=x*p-f*d,v=(p*S-d*m)/(2*w)-i,A=(d*y-p*g)/w,D=(x*m-f*S)/(2*w)-n,M=(f*g-x*y)/w,_=A*A+M*M-1,P=2*(o+v*A+D*M),T=v*v+D*D-o*o,k=-(_?(P+Math.sqrt(P*P-4*_*T))/(2*_):T/P);return{x:i+v+A*k,y:n+D+M*k,r:k}}function et(t,e,a){var i,n,o,r,l=t.x-e.x,s=t.y-e.y,h=l*l+s*s;h?(n=e.r+a.r,n*=n,r=t.r+a.r,n>(r*=r)?(i=(h+r-n)/(2*h),o=Math.sqrt(Math.max(0,r/h-i*i)),a.x=t.x-i*l-o*s,a.y=t.y-i*s+o*l):(i=(h+n-r)/(2*h),o=Math.sqrt(Math.max(0,n/h-i*i)),a.x=e.x+i*l-o*s,a.y=e.y+i*s+o*l)):(a.x=e.x+a.r,a.y=e.y)}function at(t,e){var a=t.r+e.r-1e-6,i=e.x-t.x,n=e.y-t.y;return a>0&&a*a>i*i+n*n}function it(t){var e=t._,a=t.next._,i=e.r+a.r,n=(e.x*a.r+a.x*e.r)/i,o=(e.y*a.r+a.y*e.r)/i;return n*n+o*o}function nt(t){this._=t,this.next=null,this.previous=null}function ot(t){if(!(n=t.length))return 0;var e,a,i,n,o,r,l,s,h,c,u;if((e=t[0]).x=0,e.y=0,!(n>1))return e.r;if(a=t[1],e.x=-a.r,a.x=e.r,a.y=0,!(n>2))return e.r+a.r;et(a,e,i=t[2]),e=new nt(e),a=new nt(a),i=new nt(i),e.next=i.previous=a,a.next=e.previous=i,i.next=a.previous=e;t:for(l=3;l<n;++l){et(e._,a._,i=t[l]),i=new nt(i),s=a.next,h=e.previous,c=a._.r,u=e._.r;do{if(c<=u){if(at(s._,i._)){a=s,e.next=a,a.previous=e,--l;continue t}c+=s._.r,s=s.next}else{if(at(h._,i._)){(e=h).next=a,a.previous=e,--l;continue t}u+=h._.r,h=h.previous}}while(s!==h.next);for(i.previous=e,i.next=a,e.next=a.previous=a=i,o=it(e);(i=i.next)!==a;)(r=it(i))<o&&(e=i,o=r);a=e.next}for(e=[a._],i=a;(i=i.next)!==a;)e.push(i._);for(i=q(e),l=0;l<n;++l)(e=t[l]).x-=i.x,e.y-=i.y;return i.r}function rt(t){return null==t?null:lt(t)}function lt(t){if("function"!=typeof t)throw new Error;return t}function st(){return 0}function ht(t){return function(){return t}}function ct(t){return Math.sqrt(t.value)}function ut(t){return function(e){e.children||(e.r=Math.max(0,+t(e)||0))}}function ft(t,e){return function(a){if(i=a.children){var i,n,o,r=i.length,l=t(a)*e||0;if(l)for(n=0;n<r;++n)i[n].r+=l;if(o=ot(i),l)for(n=0;n<r;++n)i[n].r-=l;a.r=o+l}}}function xt(t){return function(e){var a=e.parent;e.r*=t,a&&(e.x=a.x+t*e.x,e.y=a.y+t*e.y)}}function pt(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function dt(t,e,a,i,n){for(var o,r=t.children,l=-1,s=r.length,h=t.value&&(i-e)/t.value;++l<s;)(o=r[l]).y0=a,o.y1=n,o.x0=e,o.x1=e+=o.value*h}var yt={depth:-1},gt={};function bt(t){return t.id}function mt(t){return t.parentId}function St(t,e){return t.parent===e.parent?1:2}function wt(t){var e=t.children;return e?e[0]:t.t}function vt(t){var e=t.children;return e?e[e.length-1]:t.t}function At(t,e,a){var i=a/(e.i-t.i);e.c-=i,e.s+=a,t.c+=i,e.z+=a,e.m+=a}function Dt(t,e,a){return t.a.parent===e.parent?t.a:a}function Mt(t,e){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=e}function _t(t,e,a,i,n){for(var o,r=t.children,l=-1,s=r.length,h=t.value&&(n-a)/t.value;++l<s;)(o=r[l]).x0=e,o.x1=i,o.y0=a,o.y1=a+=o.value*h}Mt.prototype=Object.create(F.prototype);var Pt=(1+Math.sqrt(5))/2;function Tt(t,e,a,i,n,o){for(var r,l,s,h,c,u,f,x,p,d,y,g=[],b=e.children,m=0,S=0,w=b.length,v=e.value;m<w;){s=n-a,h=o-i;do{c=b[S++].value}while(!c&&S<w);for(u=f=c,y=c*c*(d=Math.max(h/s,s/h)/(v*t)),p=Math.max(f/y,y/u);S<w;++S){if(c+=l=b[S].value,l<u&&(u=l),l>f&&(f=l),y=c*c*d,(x=Math.max(f/y,y/u))>p){c-=l;break}p=x}g.push(r={value:c,dice:s<h,children:b.slice(m,S)}),r.dice?dt(r,a,i,n,v?i+=h*c/v:o):_t(r,a,i,v?a+=s*c/v:n,o),v-=c,m=S}return g}var kt=function t(e){function a(t,a,i,n,o){Tt(e,t,a,i,n,o)}return a.ratio=function(e){return t((e=+e)>1?e:1)},a}(Pt);function Lt(){var t=kt,e=!1,a=1,i=1,n=[0],o=st,r=st,l=st,s=st,h=st;function c(t){return t.x0=t.y0=0,t.x1=a,t.y1=i,t.eachBefore(u),n=[0],e&&t.eachBefore(pt),t}function u(e){var a=n[e.depth],i=e.x0+a,c=e.y0+a,u=e.x1-a,f=e.y1-a;u<i&&(i=u=(i+u)/2),f<c&&(c=f=(c+f)/2),e.x0=i,e.y0=c,e.x1=u,e.y1=f,e.children&&(a=n[e.depth+1]=o(e)/2,i+=h(e)-a,c+=r(e)-a,(u-=l(e)-a)<i&&(i=u=(i+u)/2),(f-=s(e)-a)<c&&(c=f=(c+f)/2),t(e,i,c,u,f))}return c.round=function(t){return arguments.length?(e=!!t,c):e},c.size=function(t){return arguments.length?(a=+t[0],i=+t[1],c):[a,i]},c.tile=function(e){return arguments.length?(t=lt(e),c):t},c.padding=function(t){return arguments.length?c.paddingInner(t).paddingOuter(t):c.paddingInner()},c.paddingInner=function(t){return arguments.length?(o="function"==typeof t?t:ht(+t),c):o},c.paddingOuter=function(t){return arguments.length?c.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):c.paddingTop()},c.paddingTop=function(t){return arguments.length?(r="function"==typeof t?t:ht(+t),c):r},c.paddingRight=function(t){return arguments.length?(l="function"==typeof t?t:ht(+t),c):l},c.paddingBottom=function(t){return arguments.length?(s="function"==typeof t?t:ht(+t),c):s},c.paddingLeft=function(t){return arguments.length?(h="function"==typeof t?t:ht(+t),c):h},c}var Et=function t(e){function a(t,a,i,n,o){if((r=t._squarify)&&r.ratio===e)for(var r,l,s,h,c,u=-1,f=r.length,x=t.value;++u<f;){for(s=(l=r[u]).children,h=l.value=0,c=s.length;h<c;++h)l.value+=s[h].value;l.dice?dt(l,a,i,n,i+=(o-i)*l.value/x):_t(l,a,i,a+=(n-a)*l.value/x,o),x-=l.value}else t._squarify=r=Tt(e,t,a,i,n,o),r.ratio=e}return a.ratio=function(e){return t((e=+e)>1?e:1)},a}(Pt),Wt=Object.freeze({__proto__:null,cluster:function(){var t=I,e=1,a=1,i=!1;function n(n){var o,r=0;n.eachAfter((function(e){var a=e.children;a?(e.x=function(t){return t.reduce(O,0)/t.length}(a),e.y=function(t){return 1+t.reduce(B,0)}(a)):(e.x=o?r+=t(e,o):0,e.y=0,o=e)}));var l=function(t){for(var e;e=t.children;)t=e[0];return t}(n),s=function(t){for(var e;e=t.children;)t=e[e.length-1];return t}(n),h=l.x-t(l,s)/2,c=s.x+t(s,l)/2;return n.eachAfter(i?function(t){t.x=(t.x-n.x)*e,t.y=(n.y-t.y)*a}:function(t){t.x=(t.x-h)/(c-h)*e,t.y=(1-(n.y?t.y/n.y:1))*a})}return n.separation=function(e){return arguments.length?(t=e,n):t},n.size=function(t){return arguments.length?(i=!1,e=+t[0],a=+t[1],n):i?null:[e,a]},n.nodeSize=function(t){return arguments.length?(i=!0,e=+t[0],a=+t[1],n):i?[e,a]:null},n},hierarchy:R,pack:function(){var t=null,e=1,a=1,i=st;function n(n){return n.x=e/2,n.y=a/2,t?n.eachBefore(ut(t)).eachAfter(ft(i,.5)).eachBefore(xt(1)):n.eachBefore(ut(ct)).eachAfter(ft(st,1)).eachAfter(ft(i,n.r/Math.min(e,a))).eachBefore(xt(Math.min(e,a)/(2*n.r))),n}return n.radius=function(e){return arguments.length?(t=rt(e),n):t},n.size=function(t){return arguments.length?(e=+t[0],a=+t[1],n):[e,a]},n.padding=function(t){return arguments.length?(i="function"==typeof t?t:ht(+t),n):i},n},packSiblings:function(t){return ot(t),t},packEnclose:q,partition:function(){var t=1,e=1,a=0,i=!1;function n(n){var o=n.height+1;return n.x0=n.y0=a,n.x1=t,n.y1=e/o,n.eachBefore(function(t,e){return function(i){i.children&&dt(i,i.x0,t*(i.depth+1)/e,i.x1,t*(i.depth+2)/e);var n=i.x0,o=i.y0,r=i.x1-a,l=i.y1-a;r<n&&(n=r=(n+r)/2),l<o&&(o=l=(o+l)/2),i.x0=n,i.y0=o,i.x1=r,i.y1=l}}(e,o)),i&&n.eachBefore(pt),n}return n.round=function(t){return arguments.length?(i=!!t,n):i},n.size=function(a){return arguments.length?(t=+a[0],e=+a[1],n):[t,e]},n.padding=function(t){return arguments.length?(a=+t,n):a},n},stratify:function(){var t=bt,e=mt;function a(a){var i,n,o,r,l,s,h,c=a.length,u=new Array(c),f={};for(n=0;n<c;++n)i=a[n],l=u[n]=new F(i),null!=(s=t(i,n,a))&&(s+="")&&(f[h="$"+(l.id=s)]=h in f?gt:l);for(n=0;n<c;++n)if(l=u[n],null!=(s=e(a[n],n,a))&&(s+="")){if(!(r=f["$"+s]))throw new Error("missing: "+s);if(r===gt)throw new Error("ambiguous: "+s);r.children?r.children.push(l):r.children=[l],l.parent=r}else{if(o)throw new Error("multiple roots");o=l}if(!o)throw new Error("no root");if(o.parent=yt,o.eachBefore((function(t){t.depth=t.parent.depth+1,--c})).eachBefore($),o.parent=null,c>0)throw new Error("cycle");return o}return a.id=function(e){return arguments.length?(t=lt(e),a):t},a.parentId=function(t){return arguments.length?(e=lt(t),a):e},a},tree:function(){var t=St,e=1,a=1,i=null;function n(n){var s=function(t){for(var e,a,i,n,o,r=new Mt(t,0),l=[r];e=l.pop();)if(i=e._.children)for(e.children=new Array(o=i.length),n=o-1;n>=0;--n)l.push(a=e.children[n]=new Mt(i[n],n)),a.parent=e;return(r.parent=new Mt(null,0)).children=[r],r}(n);if(s.eachAfter(o),s.parent.m=-s.z,s.eachBefore(r),i)n.eachBefore(l);else{var h=n,c=n,u=n;n.eachBefore((function(t){t.x<h.x&&(h=t),t.x>c.x&&(c=t),t.depth>u.depth&&(u=t)}));var f=h===c?1:t(h,c)/2,x=f-h.x,p=e/(c.x+f+x),d=a/(u.depth||1);n.eachBefore((function(t){t.x=(t.x+x)*p,t.y=t.depth*d}))}return n}function o(e){var a=e.children,i=e.parent.children,n=e.i?i[e.i-1]:null;if(a){!function(t){for(var e,a=0,i=0,n=t.children,o=n.length;--o>=0;)(e=n[o]).z+=a,e.m+=a,a+=e.s+(i+=e.c)}(e);var o=(a[0].z+a[a.length-1].z)/2;n?(e.z=n.z+t(e._,n._),e.m=e.z-o):e.z=o}else n&&(e.z=n.z+t(e._,n._));e.parent.A=function(e,a,i){if(a){for(var n,o=e,r=e,l=a,s=o.parent.children[0],h=o.m,c=r.m,u=l.m,f=s.m;l=vt(l),o=wt(o),l&&o;)s=wt(s),(r=vt(r)).a=e,(n=l.z+u-o.z-h+t(l._,o._))>0&&(At(Dt(l,e,i),e,n),h+=n,c+=n),u+=l.m,h+=o.m,f+=s.m,c+=r.m;l&&!vt(r)&&(r.t=l,r.m+=u-c),o&&!wt(s)&&(s.t=o,s.m+=h-f,i=e)}return i}(e,n,e.parent.A||i[0])}function r(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function l(t){t.x*=e,t.y=t.depth*a}return n.separation=function(e){return arguments.length?(t=e,n):t},n.size=function(t){return arguments.length?(i=!1,e=+t[0],a=+t[1],n):i?null:[e,a]},n.nodeSize=function(t){return arguments.length?(i=!0,e=+t[0],a=+t[1],n):i?[e,a]:null},n},treemap:Lt,treemapBinary:function(t,e,a,i,n){var o,r,l=t.children,s=l.length,h=new Array(s+1);for(h[0]=r=o=0;o<s;++o)h[o+1]=r+=l[o].value;!function t(e,a,i,n,o,r,s){if(e>=a-1){var c=l[e];return c.x0=n,c.y0=o,c.x1=r,void(c.y1=s)}var u=h[e],f=i/2+u,x=e+1,p=a-1;for(;x<p;){var d=x+p>>>1;h[d]<f?x=d+1:p=d}f-h[x-1]<h[x]-f&&e+1<x&&--x;var y=h[x]-u,g=i-y;if(r-n>s-o){var b=(n*g+r*y)/i;t(e,x,y,n,o,b,s),t(x,a,g,b,o,r,s)}else{var m=(o*g+s*y)/i;t(e,x,y,n,o,r,m),t(x,a,g,n,m,r,s)}}(0,s,t.value,e,a,i,n)},treemapDice:dt,treemapSlice:_t,treemapSliceDice:function(t,e,a,i,n){(1&t.depth?_t:dt)(t,e,a,i,n)},treemapSquarify:kt,treemapResquarify:Et});function zt(){let{opts:t,legendData:e,chartData:a,seriesMap:i}=this,{width:n,height:o,padding:r}=t,l=i.treemap[0];l.tile=(l.tile,l.tile);let s=R(l,(function(t){return t.data})).sum((function(t){return t.value})),h=Lt().tile(Wt[l.tile]).size([n,o]).paddingTop(r[0]).paddingRight(r[1]).paddingBottom(r[2]+e.legendHeight).paddingLeft(r[3])(s);a.chartTreemap=h,a.chartTreemap}const jt=Math.PI/180;let Xt,Yt;function It(t){return t.text}function Ot(){return"Serif"}function Bt(){return"normal"}function Ct(t){return t.value}function Rt(){return 90*~~(2*Math.random())}function Nt(){return 1}function Ht(t,e,a,i){if(e.sprite)return;const n=t.context,o=t.ratio;n.clearRect(0,0,(Xt<<5)/o,Yt/o);let r=0,l=0,s=0;const h=a.length;for(--i;++i<h;){e=a[i],n.save(),n.fillStyle=n.strokeStyle="#ff0000",n.textAlign="center",n.font=`${e.style} ${e.weight} ${~~((e.size+1)/o)}px ${e.font}`;let t=n.measureText(e.text+"m").width*o,h=e.size<<1;if(e.rotate){const a=Math.sin(e.rotate*jt),i=Math.cos(e.rotate*jt),n=t*i,o=t*a,r=h*i,l=h*a;t=Math.max(Math.abs(n+l),Math.abs(n-l))+31>>5<<5,h=~~Math.max(Math.abs(o+r),Math.abs(o-r))}else t=t+31>>5<<5;if(h>s&&(s=h),r+t>=Xt<<5&&(r=0,l+=s,s=0),l+h>=Yt)break;n.translate((r+(t>>1))/o,(l+(h>>1))/o),e.rotate&&n.rotate(e.rotate*jt),n.fillText(e.text,0,0),e.padding&&(n.lineWidth=2*e.padding,n.strokeText(e.text,0,0)),n.restore(),e.width=t,e.height=h,e.xoff=r,e.yoff=l,e.x1=t>>1,e.y1=h>>1,e.x0=-e.x1,e.y0=-e.y1,e.hasText=!0,r+=t}const c=n.getImageData(0,0,(Xt<<5)/o,Yt/o).data,u=[];for(;--i>=0;){if(!(e=a[i]).hasText)continue;const t=e.width,n=t>>5;let o=e.y1-e.y0;for(let t=0;t<o*n;t++)u[t]=0;if(r=e.xoff,null==r)return;l=e.yoff;let s=0,h=-1;for(let a=0;a<o;a++){for(let e=0;e<t;e++){let t=n*a+(e>>5),i=c[(l+a)*(Xt<<5)+(r+e)<<2]?1<<31-e%32:0;u[t]|=i,s|=i}s?h=a:(e.y0++,o--,a--,l++)}e.y1=e.y0+h,e.sprite=u.slice(0,(e.y1-e.y0)*n)}}function $t(t,e,a){a>>=5;const i=t.sprite,n=t.width>>5,o=t.x-(n<<4),r=127&o,l=32-r,s=t.y1-t.y0;let h,c=(t.y+t.y0)*a+(o>>5);for(let t=0;t<s;t++){h=0;for(let a=0;a<=n;a++)if((h<<l|(a<n?(h=i[t*n+a])>>>r:0))&e[c+a])return!0;c+=a}return!1}function Ft(t,e){const a=t[0],i=t[1];e.x+e.x0<a.x&&(a.x=e.x+e.x0),e.y+e.y0<a.y&&(a.y=e.y+e.y0),e.x+e.x1>i.x&&(i.x=e.x+e.x1),e.y+e.y1>i.y&&(i.y=e.y+e.y1)}function Gt(t){let e=t[0]/t[1];return function(t){return[e*(t*=.1)*Math.cos(t),t*Math.sin(t)]}}function qt(t){const e=[];let a=-1;for(;++a<t;)e[a]=0;return e}function Zt(t){return"function"==typeof t?t:function(){return t}}const Ut={archimedean:Gt,rectangular:function(t){const e=4*t[0]/t[1];let a=0,i=0;return function(t){const n=t<0?-1:1;switch(Math.sqrt(1+4*n*t)-n&3){case 0:a+=e;break;case 1:i+=4;break;case 2:a-=e;break;default:i-=4}return[a,i]}}};function Vt(){let t=[256,256],e=It,a=Ot,i=Ct,n=Bt,o=Bt,r=Rt,l=Nt,s=Gt,h=[],c=1/0,u=Math.random,f=null,x=function(){};const p={};function d(e,a,i){const n=a.x,o=a.y,r=Math.sqrt(t[0]*t[0]+t[1]*t[1]),l=s(t),h=u()<.5?1:-1;let c,f,x,p=-h;for(;(c=l(p+=h))&&(f=~~c[0],x=~~c[1],!(Math.min(Math.abs(f),Math.abs(x))>=r));)if(a.x=n+f,a.y=o+x,!(a.x+a.x0<0||a.y+a.y0<0||a.x+a.x1>t[0]||a.y+a.y1>t[1]||i&&$t(a,e,t[0])||i&&(y=i,!((d=a).x+d.x1>y[0].x&&d.x+d.x0<y[1].x&&d.y+d.y1>y[0].y&&d.y+d.y0<y[1].y)))){const i=a.sprite,n=a.width>>5,o=t[0]>>5,r=a.x-(n<<4),l=127&r,s=32-l,h=a.y1-a.y0;let c,u=(a.y+a.y0)*o+(r>>5);for(let t=0;t<h;t++){c=0;for(let a=0;a<=n;a++)e[u+a]|=c<<s|(a<n?(c=i[t*n+a])>>>l:0);u+=o}return delete a.sprite,!0}var d,y;return!1}return p.end=function(t){return arguments.length?(x=Zt(t),p):x},p.canvas=function(t){return arguments.length?(f=Zt(t),p):f},p.start=function(){const s=Math.min(...t);Xt=s>>5,Yt=s;const y=function(t){t.width=t.height=1;const e=Math.sqrt(t.getContext("2d").getImageData(0,0,1,1).data.length>>2);t.width=(Xt<<5)/e,t.height=Yt/e;const a=t.getContext("2d");return a.fillStyle=a.strokeStyle="#ff0000",a.textAlign="center",{context:a,ratio:e}}(f()),g=p.board?p.board:qt((t[0]>>5)*t[1]),b=h.length,m=[],S=h.map((function(t,s){return t.text=e.call(this,t,s),t.font=a.call(this,t,s),t.style=n.call(this,t,s),t.weight=o.call(this,t,s),t.rotate=r.call(this,t,s),t.size=~~i.call(this,t,s),t.padding=l.call(this,t,s),t})).sort((function(t,e){return e.size-t.size}));let w=-1,v=p.board?[{x:0,y:0},{x:t[0],y:t[1]}]:null;return function(){const e=Date.now();for(;Date.now()-e<c&&++w<b;){const e=S[w];e.x=t[0]*(u()+.5)>>1,e.y=t[1]*(u()+.5)>>1,Ht(y,e,S,w),e.hasText&&d(g,e,v)&&(m.push(e),v?p.hasImage||Ft(v,e):v=[{x:e.x+e.x0,y:e.y+e.y0},{x:e.x+e.x1,y:e.y+e.y1}],e.x-=t[0]>>1,e.y-=t[1]>>1)}w>=b&&x(m);p._tags=m,p._bounds=v}(),p},p.createMask=e=>{const a=f(),[i,n]=t,o=i>>5,r=qt((i>>5)*n);a.width=i,a.height=n;const l=a.getContext("2d");l.drawImage(e,0,0,e.width,e.height,0,0,i,n);const s=l.getImageData(0,0,i,n).data;for(let t=0;t<n;t++)for(let e=0;e<i;e++){const a=o*t+(e>>5),n=t*i+e<<2,l=s[n]>=250&&s[n+1]>=250&&s[n+2]>=250?1<<31-e%32:0;r[a]|=l}return p.board=r,p.hasImage=!0,p},p.timeInterval=function(t){return arguments.length?(c=null==t?1/0:t,p):c},p.words=function(t){return arguments.length?(h=t,p):h},p.size=function(e){return arguments.length?(t=[+e[0],+e[1]],p):t},p.font=function(t){return arguments.length?(a=Zt(t),p):a},p.fontStyle=function(t){return arguments.length?(n=Zt(t),p):n},p.fontWeight=function(t){return arguments.length?(o=Zt(t),p):o},p.rotate=function(t){return arguments.length?(r=Zt(t),p):r},p.text=function(t){return arguments.length?(e=Zt(t),p):e},p.spiral=function(t){return arguments.length?(s=Ut[t]||t,p):s},p.fontSize=function(t){return arguments.length?(i=Zt(t),p):i},p.padding=function(t){return arguments.length?(l=Zt(t),p):l},p.random=function(t){return arguments.length?(u=t,p):u},p}function Jt(){let{opts:t,chartData:e,seriesMap:a}=this,{element:i,width:n,height:o}=t,r=c(a.tagCloud[0]);const l=Vt();l.canvas(r.canvas||i),l.size(r.size||[n,o]),l.words(r.data),l.end((function(t){r.data=t,e.chartTagCloud=r})),r.font&&l.font(r.font),r.fontSize&&l.fontSize(r.fontSize),r.padding&&l.padding(r.padding),r.rotate&&l.rotate(r.rotate),r.spiral&&l.spiral(r.spiral),r.timeInterval&&l.timeInterval(r.timeInterval),r.imageMask&&l.createMask(r.imageMask),l.start(),e.chartTagCloud}function Kt(){f.call(this),D.call(this),M.call(this),_.call(this),(this.seriesMap.line||this.seriesMap.bar||this.seriesMap.scatter||this.seriesMap.candlestick||this.seriesMap.heatmap)&&P.call(this),Object.keys(this.seriesMap).forEach(t=>{if(this.seriesMap[t])switch(t){case"bar":k.call(this);break;case"line":L.call(this);break;case"pie":E.call(this);break;case"radar":T.call(this),W.call(this);break;case"scatter":z.call(this);break;case"funnel":j.call(this);break;case"candlestick":X.call(this);break;case"heatmap":Y.call(this);break;case"treemap":zt.call(this);break;case"tagCloud":Jt.call(this)}})}var Qt={easeIn:function(t){return Math.pow(t,3)},easeOut:function(t){return Math.pow(t-1,3)+1},easeInOut:function(t){return(t/=.5)<1?.5*Math.pow(t,3):.5*(Math.pow(t-2,3)+2)},linear:function(t){return t}};class te{constructor(t){this.isStop=!1;let{animation:e,animationDuration:a,animationTiming:i,onProcess:n,onAnimationFinish:o}=t,r="undefined"!=typeof requestAnimationFrame?requestAnimationFrame:"undefined"!=typeof setTimeout?function(t){setTimeout((function(){let e=+new Date;t(e)}),17)}:void 0;if(e){let e=Qt[i],l=null,s=function(){if(!0===this.isStop)return n(1),void o();let i=+new Date;if(l||(l=i),i-l<a){let n=(i-l)/a;n=e(n),t.onProcess(n),r(s)}else n(1),o()};s=s.bind(this),r(s)}else n(1),o()}stop(){this.isStop=!0}}function ee(){const{context:t,opts:e,tooltipData:a}=this,{yAxis:i,xAxis:n,tooltip:o}=e,{data:r,axisPointerData:l,tooltipX:s,tooltipY:h,tooltipWidth:c,tooltipHeight:u}=a,{show:f,axisPointer:x,backgroundColor:p,backgroundRadius:d,backgroundOpacity:y,padding:g,itemGap:b,iconRadius:m,iconGap:S,textStyle:w}=o,{fontSize:v,color:A,lineHeight:D}=w,{type:M,lineStyle:_,shadowStyle:P,cross:T}=x,{lineWdith:k,lineDash:L,color:E,opacity:W}=_,{color:z,opacity:j}=P,{show:X,lineWidth:Y,lineDash:I,lineColor:O,lineOpacity:B,backgroundColor:C,backgroundOpacity:R,fontColor:N,fontPadding:H}=T;if(!f||0==r.length)return;if(l){const{crossPointer:e}=l,{yAxisLabel:a,yAxisLabelWidth:n,yAxisLabelHeight:o,yAxisLabelX:r,yAxisLabelY:s,yAxisLineX0:h,yAxisLineY0:c,yAxisLineX1:u,yAxisLineY1:f}=e;if(X){const e=i.axisLabel.textStyle.fontSize;t.save(),t.lineWidth=Y,t.setLineDash(I),t.strokeStyle=O,t.globalAlpha=B,t.beginPath(),t.moveTo(h,c),t.lineTo(u,f),t.stroke(),t.restore(),t.save(),t.fillStyle=C,t.globalAlpha=R,t.fillRect(r+H,s-e/2-H,-n,o),t.restore(),t.save(),t.fillStyle=N,t.font=e+"px",t.textBaseline="middle",t.textAlign="right",t.fillText(a,r,s),t.restore()}}if(e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&t.translate(e._scrollDistance_,0),l){const{xAxisPointer:e,yAxisPointer:a,crossPointer:i}=l,{yAxisLineX0:o,yAxisLineY0:r,yAxisLineX1:s,yAxisLineY1:h,xAxisLabel:c,xAxisLabelWidth:u,xAxisLabelHeight:f,xAxisLabelX:x,xAxisLabelY:p,xAxisLineX0:d,xAxisLineY0:y,xAxisLineX1:g,xAxisLineY1:b}=i;if(X){const e=n.axisLabel.textStyle.fontSize;t.save(),t.lineWidth=Y,t.setLineDash(I),t.strokeStyle=O,t.globalAlpha=B,t.beginPath(),t.moveTo(o,r),t.lineTo(s,h),t.stroke(),t.restore(),t.save(),t.lineWidth=Y,t.setLineDash(I),t.strokeStyle=O,t.globalAlpha=B,t.beginPath(),t.moveTo(d,y),t.lineTo(g,b),t.stroke(),t.restore(),t.save(),t.fillStyle=C,t.globalAlpha=R,t.fillRect(x-u/2,p-H,u,f),t.restore(),t.save(),t.fillStyle=N,t.font=e+"px",t.textBaseline="top",t.textAlign="center",t.fillText(c,x,p),t.restore()}if("line"==M){if(e){const{x0:a,y0:i,x1:n,y1:o}=e;t.save(),t.globalAlpha=W,L&&t.setLineDash(L),t.lineWidth=k,t.strokeStyle=E,t.beginPath(),t.moveTo(a,i),t.lineTo(n,o),t.stroke(),t.restore()}}else if("shadow"==M&&e){const{x:a,y:i,width:n,height:o}=e;t.save(),t.globalAlpha=j,t.fillStyle=z,t.fillRect(a,i,n,o),t.restore()}}r.forEach(e=>{if("line"==e.type||"radar"==e.type){const{x:a,y:i,color:n,symbolType:o,symbolSize:r,symbolColor:l}=e;switch(o){case"circle":t.beginPath(),t.fillStyle="auto"==l?n:l,t.arc(a,i,(r+5)/2,0,2*Math.PI),t.fill(),t.beginPath(),t.fillStyle="#ffffff",t.arc(a,i,(r+6)/4,0,2*Math.PI),t.fill(),t.restore()}}if("pie"==e.type){const{color:a,center:i,radius:n,_start_:o,_end_:r}=e,[l,s]=i,[h,c]=n;t.save(),t.beginPath(),t.moveTo(l,s),t.fillStyle=a,t.arc(l,s,c+8,o,r),t.fill(),h>0&&(t.beginPath(),t.moveTo(l,s),t.fillStyle=this.opts.backgroundColor,t.arc(l,s,h,o,r),t.fill()),t.restore()}}),function(t,e,a,i,n,o,r="#000000",l=.7){t.save(),t.fillStyle=r,t.globalAlpha=l,t.beginPath(),t.moveTo(e+o,a),t.lineTo(e+i-o,a),t.arc(e+i-o-.5,a+o+.5,o,-Math.PI/2,0),t.lineTo(e+i,a+n-o),t.arc(e+i-o-.5,a+n-o-.5,o,0,Math.PI/2),t.lineTo(e+o,a+n),t.arc(e+o+.5,a+n-o-.5,o,Math.PI/2,Math.PI),t.lineTo(e,a+o),t.arc(e+o+.5,a+o+.5,o,Math.PI,3*Math.PI/2),t.fill(),t.restore()}(t,s,h,c,u,d,p,y);let $=s+g+m,F=h+g+D/2;a.tooltipTitle&&(t.save(),t.fillStyle=A,t.font=v+"px",t.textBaseline="middle",t.textAlign="left",t.fillText(a.tooltipTitle,$-m,F),t.save(),F+=D+b),r.forEach(e=>{if("candlestick"==e.type||"k"==e.type){const{name:a,start:i,end:n,high:o,low:r,volumn:l,color:h}=e;t.save(),t.beginPath(),t.fillStyle=h,t.arc($,F,m,0,2*Math.PI),t.fill(),$+=m+S,t.beginPath(),t.fillStyle=A,t.font=v+"px",t.textBaseline="middle",t.textAlign="left",t.fillText(a,$,F),F+=D+b,t.fillText(i,$,F),F+=D+b,t.fillText(n,$,F),F+=D+b,t.fillText(r,$,F),F+=D+b,t.fillText(o,$,F),F+=D+b,l&&(t.fillText(l,$,F),F+=D+b),t.restore(),$=s+g+m}else{const{text:a,color:i}=e;t.save(),t.beginPath(),t.fillStyle=i,t.arc($,F,m,0,2*Math.PI),t.fill(),$+=m+S,t.beginPath(),t.fillStyle=A,t.font=v+"px",t.textBaseline="middle",t.textAlign="left",t.fillText(a,$,F),t.restore(),$=s+g+m,F+=D+b}})}function ae(t=0,e=0,a=this.opts.width,i=this.opts.height){this.context.clearRect(t,e,a,i),this.context.fillStyle=this.opts.backgroundColor,this.context.fillRect(t,e,a,i)}function ie(){let{context:t,opts:e,legendData:i}=this,{width:n,height:o,legend:r,padding:l}=e;if(!r.show)return;let s,{shapeWidth:h,shapeHeight:c,shapeRadius:u,itemGap:f,marginTop:x,textStyle:p}=r,{fontSize:d,color:y,padding:g}=p,{legendList:b,legendWidth:m,legendHeight:S}=i,w=o-l[2]-S+x,v=l[3]+(n-l[1]-l[3]-m)/2;b.forEach((e,i)=>{v=l[3]+(n-l[1]-l[3]-m)/2,e.forEach(e=>{let{legendType:i,color:n,name:o,measureText:r}=e;switch(i){case"circle":s=Math.max(2*u,d),t.beginPath(),t.moveTo(v+u,w+s/2),t.arc(v+u,w+s/2,u,0,2*Math.PI),t.closePath(),t.fillStyle=n,t.fill(),v+=2*u+g;break;case"line":s=Math.max(c,d);let e=(h-c)/2;t.beginPath(),t.moveTo(v,w+s/2),t.lineTo(v+e-2,w+s/2),t.closePath(),t.lineWidth=2,t.strokeStyle=n,t.stroke(),t.beginPath(),t.moveTo(v+h/2,w+s/2),t.arc(v+h/2,w+s/2,c/2,0,2*Math.PI),t.closePath(),t.fillStyle=n,t.fill(),t.beginPath(),t.moveTo(v+e+c+2,w+s/2),t.lineTo(v+h,w+s/2),t.closePath(),t.lineWidth=2,t.strokeStyle=n,t.stroke(),v+=h+g;break;case"rect":s=Math.max(c,d),t.fillStyle=a(n,t,v,w+s/2-c/2,v+h,w+s/2+c/2),t.fillRect(v,w+s/2-c/2,h,c),v+=h+g}t.save(),t.textAlign="left",t.textBaseline="middle",t.font=d+"px",t.fillStyle=n,t.fillText(o,v,w+s/2),t.restore(),v+=r+f}),w+=s+f})}function ne(){let{context:t,opts:e,chartData:a}=this,{xAxis:i}=e,{show:n,type:o,axisName:r,axisLabel:l,axisTick:s,axisLine:h}=i,{show:c,textStyle:u}=r,{show:f,textStyle:x,rotate:p}=l,{show:d,lineStyle:y}=s,{show:g,lineStyle:b}=h,{color:m,fontSize:S}=u,{color:w,fontSize:v}=x,{color:A,lineWidth:D}=y,{color:M,lineWidth:_}=b,{xAxisLabelPoint:P,xAxisTickPoint:T,xAxisLinePoint:k,xAxisNamePoint:L}=a.axisData;n&&(f&&(t.save(),e._scrollDistance_&&0!==e._scrollDistance_&&t.translate(e._scrollDistance_,0),t.font=v+"px",t.fillStyle=w,t.textBaseline="top",0==p?t.textAlign="center":p>0?t.textAlign="right":p<0&&(t.textAlign="left"),P.forEach(e=>{("value"==o||e.show)&&(0==p?t.fillText(e.text,e.x,e.y):(t.save(),t.translate(e.x,e.y),t.rotate(-p*Math.PI/180),t.fillText(e.text,0,0),t.restore()))}),t.restore()),d&&(t.save(),e._scrollDistance_&&0!==e._scrollDistance_&&t.translate(e._scrollDistance_,0),t.lineWidth=D,t.strokeStyle=A,T.forEach(e=>{("value"==o||e.show)&&(t.beginPath(),t.moveTo(e.startX,e.startY),t.lineTo(e.endX,e.endY),t.closePath(),t.stroke())}),t.restore()),g&&(t.beginPath(),t.moveTo(k.startX,k.startY),t.lineTo(k.endX,k.endY),t.closePath(),t.lineWidth=_,t.strokeStyle=M,t.stroke()),e.enableScroll&&(t.save(),t.fillStyle=e.backgroundColor||"#ffffff",e._scrollDistance_<0&&t.fillRect(0,0,Math.ceil(a.axisData.xStart),e.height),t.fillRect(Math.ceil(a.axisData.xEnd),0,e.width-Math.ceil(a.axisData.xEnd),e.height),t.restore()),c&&(t.save(),t.font=S+"px",t.fillStyle=m,t.textAlign="left",t.textBaseline="middle",t.fillText(L.text,L.x,L.y),t.restore()))}function oe(){let{context:t,opts:e,chartData:a}=this,{xAxis:i}=e,{show:n,type:o,axisSplitLine:r}=i,{show:l,lineStyle:s}=r,{color:h,lineWidth:c}=s,{xAxisSplitLinePoint:u}=a.axisData;n&&l&&(t.save(),e._scrollDistance_&&0!==e._scrollDistance_&&t.translate(e._scrollDistance_,0),t.lineWidth=c,t.strokeStyle=h,u.forEach((e,a)=>{("value"==o||e.show)&&(t.beginPath(),t.moveTo(e.startX,e.startY),t.lineTo(e.endX,e.endY),t.closePath(),t.stroke())}),t.restore())}function re(){let{context:t,opts:e,chartData:a}=this,{yAxis:i}=e,{show:n,type:o,axisName:r,axisLabel:l,axisTick:s,axisLine:h}=i,{show:c,textStyle:u}=r,{show:f,textStyle:x}=l,{show:p,lineStyle:d}=s,{show:y,lineStyle:g}=h,{color:b,fontSize:m}=u,{color:S,fontSize:w}=x,{color:v,lineWidth:A}=d,{color:D,lineWidth:M}=g,{yAxisLabelPoint:_,yAxisTickPoint:P,yAxisLinePoint:T,yAxisNamePoint:k}=a.axisData;n&&(f&&(t.save(),t.font=w+"px",t.fillStyle=S,t.textAlign="right",t.textBaseline="middle",_.forEach(e=>{("value"==o||e.show)&&t.fillText(e.text,e.x,e.y)}),t.restore()),c&&(t.save(),t.font=m+"px",t.fillStyle=b,t.textAlign="center",t.textBaseline="bottom",t.fillText(k.text,k.x,k.y),t.restore()),p&&(t.lineWidth=A,t.strokeStyle=v,P.forEach(e=>{("value"==o||e.show)&&(t.beginPath(),t.moveTo(e.startX,e.startY),t.lineTo(e.endX,e.endY),t.closePath(),t.stroke())})),y&&(t.beginPath(),t.moveTo(T.startX,T.startY),t.lineTo(T.endX,T.endY),t.closePath(),t.lineWidth=M,t.strokeStyle=D,t.stroke()))}function le(){let{context:t,opts:e,chartData:a}=this,{yAxis:i}=e,{show:n,type:o,axisSplitLine:r}=i,{show:l,lineStyle:s}=r,{color:h,lineWidth:c}=s,{yAxisSplitLinePoint:u}=a.axisData;n&&l&&(t.lineWidth=c,t.strokeStyle=h,u.forEach((e,a)=>{("value"==o||e.show)&&(t.beginPath(),t.moveTo(e.startX,e.startY),t.lineTo(e.endX,e.endY),t.closePath(),t.stroke())}))}function se(){let{context:t,opts:e,chartData:a}=this,{backgroundColor:i,radarAxis:n,categories:o}=e,{shape:r,splitNumber:l,axisName:s,axisLine:h,splitLine:c,splitArea:u}=n,{show:f,textStyle:x}=s,{show:p,lineStyle:d}=h,{show:y,lineStyle:g}=c,{color:b,fontSize:m}=x,{color:S,lineWidth:w}=d,{color:v,lineWidth:A}=g,{odd:D,even:M}=u,{show:_,color:P,opacity:T}=D,{show:k,color:L,opacity:E}=M,{center:W,radius:z,lineEndPosition:j,namePosition:X}=a.radarAxis,[Y,I]=W;if("polygon"==r)j.forEach((e,a)=>{let n=(l-a)%2;t.beginPath(),e.forEach((e,a)=>{0==a?t.moveTo(e.x,e.y):t.lineTo(e.x,e.y)}),t.closePath(),0===n&&_&&(t.fillStyle=i,t.fill(),t.save(),t.globalAlpha=E,t.fillStyle=L,t.fill(),t.restore()),1===n&&k&&(t.fillStyle=i,t.fill(),t.save(),t.globalAlpha=T,t.fillStyle="auto"==P?i:P,t.fill(),t.restore()),y&&(t.lineWidth=A,t.strokeStyle=v,t.stroke())});else for(let e=0;e<l;e++){let a=(l-e)/l,n=(l-e)%2;t.beginPath(),t.arc(Y,I,z*a,0,2*Math.PI),0===n&&_&&(t.fillStyle=i,t.fill(),t.save(),t.globalAlpha=E,t.fillStyle=L,t.fill(),t.restore()),1===n&&k&&(t.fillStyle=i,t.fill(),t.save(),t.globalAlpha=T,t.fillStyle="auto"==P?i:P,t.fill(),t.restore()),y&&(t.lineWidth=A,t.strokeStyle=v,t.stroke())}p&&j[0].forEach(e=>{t.beginPath(),t.moveTo(Y,I),t.lineTo(e.x,e.y),t.lineWidth=w,t.strokeStyle=S,t.stroke()}),f&&X.forEach(e=>{let{text:a,point:i,position:n}=e,{x:o}=i,{x:r,y:l}=n;t.save(),r==Y?t.textAlign="center":o>0?t.textAlign="left":o<0&&(t.textAlign="right"),t.textBaseline="middle",t.font=m+"px",t.fillStyle=b,t.fillText(a,r,l),t.restore()})}function he(t){let{context:e,opts:i,chartData:n}=this,{label:o,xAxis:r}=i,{yMaxData:l,yMinData:s,xMaxData:h,xMinData:c}=n.axisData;r.type,r.type;"category"==r.type?(n.chartBar.forEach((n,o)=>{n.forEach((n,o)=>{n.forEach((n,o)=>{let{x:r,y:l,data:s,barWidth:h,barHeight:c,itemStyle:u}=n,{color:f}=u;e.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&e.translate(i._scrollDistance_,0),e.fillStyle=a(f,e,r-h/2,l-c,r+h,l),s>=0?e.fillRect(r-h/2,l,h,-c*t):e.fillRect(r-h/2,l,h,c*t),e.restore()})})}),1==t&&n.chartBar.forEach((t,a)=>{t.forEach((t,a)=>{t.forEach((t,a)=>{let{show:n,x:r,y:l,barWidth:s,barHeight:h,data:c,label:u,itemStyle:f}=t,{show:x,fontSize:p,color:d,margin:y,format:g}=u,{color:b}=f;const m=g?g(c):c;x=o&&"boolean"==typeof o.show?o.show:x,p=o&&o.fontSize?o.fontSize:p,d=o&&o.color?o.color:d,y=o&&o.margin?o.margin:y,x&&n&&(e.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&e.translate(i._scrollDistance_,0),e.font=p+"px",e.strokeStyle="auto"==d?b:d,e.fillStyle="#ffffff",e.textBaseline="middle",e.textAlign="center",c>=0?(e.strokeText(m,r,l-h/2),e.fillText(m,r,l-h/2)):(e.strokeText(m,r,l+h/2),e.fillText(m,r,l+h/2)),e.restore())})})})):(n.chartBar.forEach((a,i)=>{a.forEach((a,i)=>{a.forEach((a,i)=>{let{x:n,y:o,data:r,barWidth:l,barHeight:s,itemStyle:h}=a,{color:c}=h;e.save(),e.fillStyle=c,r>0?e.fillRect(n,o-l*t/2,s,l*t):e.fillRect(n,o-l*t/2,-s,l*t),e.restore()})})}),1==t&&n.chartBar.forEach((t,a)=>{t.forEach((t,a)=>{t.forEach((t,a)=>{let{show:i,x:n,y:r,barWidth:l,barHeight:s,data:h,label:c,itemStyle:u}=t,{show:f,fontSize:x,color:p,margin:d,format:y}=c,{color:g}=u;const b=y?y(h):h;f=o&&"boolean"==typeof o.show?o.show:f,x=o&&o.fontSize?o.fontSize:x,p=o&&o.color?o.color:p,d=o&&o.margin?o.margin:d,f&&i&&(e.save(),e.font=x+"px",e.strokeStyle="auto"==p?g:p,e.fillStyle="#ffffff",e.textBaseline="middle",e.textAlign="center",h>=0?(e.strokeText(b,n+s/2,r),e.fillText(b,n+s/2,r)):(e.strokeText(b,n-s/2,r),e.fillText(b,n-s/2,r)),e.restore())})})}))}function ce(t){let{context:e,opts:i,chartData:n}=this,{label:o,xAxis:r}=i,{xStart:l,xEnd:s,yStart:h,yEnd:u,yZero:f,xZero:x,yMaxData:p,yMinData:d,xMaxData:y,xMinData:g}=n.axisData,b="value"==r.type?y:p,m="value"==r.type?g:d;function S(t,e){return!(!t[e-1]||!t[e+1])&&(t[e].y>=Math.max(t[e-1].y,t[e+1].y)||t[e].y<=Math.min(t[e-1].y,t[e+1].y))}function w(t,a){let{show:n,lineWidth:o,color:r,opacity:l}=t,{color:s}=a;n&&(e.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&e.translate(i._scrollDistance_,0),e.lineJoin="round",e.globalAlpha=l,e.lineWidth=o,e.strokeStyle="auto"==r?s:r,e.stroke(),e.restore())}function v(t,n,o,c,p,d){let{show:y,color:g,opacity:S}=t,{color:w}=n;y&&("category"==r.type?b>=0&&m>=0?(e.lineTo(p,h),e.lineTo(o,h)):b<=0&&m<=0?(e.lineTo(p,u),e.lineTo(o,u)):(e.lineTo(p,f),e.lineTo(o,f)):b>=0&&m>=0?(e.lineTo(l,d),e.lineTo(l,c)):b<=0&&m<=0?(e.lineTo(s,d),e.lineTo(s,c)):(e.lineTo(x,d),e.lineTo(x,c)),e.closePath(),e.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&e.translate(i._scrollDistance_,0),e.globalAlpha=S,e.fillStyle=a("auto"==g?w:g,e,l,u,s,h),e.fill(),e.restore())}c(n.chartLine).forEach(a=>{let n,l,s,h,{itemStyle:c,line:u,symbol:f,area:x,label:p,smooth:d,connectNulls:y}=a,{color:g}=c,{show:A,type:D,size:M,color:_}=f,{show:P,fontSize:T,color:k,margin:L,format:E}=p;if(d){a.data=a.data.map(e=>{let{x:a,y:i,height:n,data:o}=e;return"category"==r.type?e.y=b>=0&&m>=0?i+n-n*t:b<=0&&m<=0?i-n+n*t:o>0?i+n-n*t:i-n+n*t:e.x=b>=0&&m>=0?a-n+n*t:b<=0&&m<=0?a+n-n*t:o>0?a-n+n*t:a+n-n*t,e}),a.validData=a.data.filter(t=>"number"==typeof t.data),(y?a.validData:a.data).forEach((t,a,i)=>{const o=.2,r=.2;let f=null,p=null,d=null,g=null,{x:b,y:m,data:A}=t;if("number"==typeof A)if(n&&l){let t=a-1;if(t<1?(f=i[0].x+(i[1].x-i[0].x)*o,p=i[0].y+(i[1].y-i[0].y)*o):(f=i[t].x+(i[t+1].x-i[t-1].x)*o,p=i[t].y+(i[t+1].y-i[t-1].y)*o),t>i.length-3){let t=i.length-1;d=i[t].x-(i[t].x-i[t-1].x)*r,g=i[t].y-(i[t].y-i[t-1].y)*r}else d=i[t+1].x-(i[t+2].x-i[t].x)*r,g=i[t+1].y-(i[t+2].y-i[t].y)*r;S(i,t+1)&&(g=i[t+1].y),S(i,t)&&(p=i[t].y),e.bezierCurveTo(f,p,d,g,b,m),s=b,h=m}else e.beginPath(),e.moveTo(b,m),n=b,l=m;(!y&&"number"!=typeof A||a+1==i.length)&&(s&&h&&(w(u,c),v(x,c,n,l,s,h),s=null,h=null),n=null,l=null)})}else a.data.forEach((a,i,o)=>{let{x:f,y:p,height:d,data:g}=a;"category"==r.type?p=b>=0&&m>=0?p+d-d*t:b<=0&&m<=0?p-d+d*t:g>0?p+d-d*t:p-d+d*t:f=b>=0&&m>=0?f-d+d*t:b<=0&&m<=0?f+d-d*t:g>0?f-d+d*t:f+d-d*t,"number"==typeof g&&(n&&l?(e.lineTo(f,p),s=f,h=p):(e.beginPath(),e.moveTo(f,p),n=f,l=p)),(!y&&"number"!=typeof g||i+1==o.length)&&s&&h&&(w(u,c),v(x,c,n,l,s,h),n=null,l=null,s=null,h=null)});1==t&&(A&&(e.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&e.translate(i._scrollDistance_,0),a.data.forEach(t=>{let{x:a,y:i,data:n}=t;if("number"==typeof n)switch(D){case"circle":e.beginPath(),e.arc(a,i,M/2,0,2*Math.PI),e.fillStyle="auto"==_?g:_,e.fill(),e.beginPath(),e.arc(a,i,M/4,0,2*Math.PI),e.fillStyle="#ffffff",e.fill()}}),e.restore()),P=o&&"boolean"==typeof o.show?o.show:P,T=o&&o.fontSize?o.fontSize:T,k=o&&o.color?o.color:k,L=o&&o.margin?o.margin:L,P&&(e.save(),i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&e.translate(i._scrollDistance_,0),e.font=T+"px",e.fillStyle="auto"==k?g:k,e.textAlign="center",a.data.forEach(t=>{let{x:a,y:i,data:n}=t;if("number"!=typeof n)return;const o=E?E(n):n;"category"==r.type?b>=0&&m>=0?(e.textBaseline="bottom",e.fillText(o,a,i-L)):b<=0&&m<=0?(e.textBaseline="top",e.fillText(o,a,i+L)):n?(e.textBaseline="bottom",e.fillText(o,a,i-L)):(e.textBaseline="top",e.fillText(o,a,i+L)):(e.textBaseline="bottom",e.fillText(n,a,i-L))}),e.restore()))})}function ue(t){let{context:e,opts:a,chartData:i}=this,{backgroundColor:n,label:l}=a,{data:s,center:h,radius:c,offsetAngle:u,disablePieStroke:f,valueSum:x,maxData:p,roseType:d}=i.chartPie,[y,g]=h,[b,m]=c,S=0!==u?u*Math.PI/180:0;if(s.forEach((a,i)=>{a._start_=S,a._proportion_="area"==d?1/s.length*t:a.value/x*t,a._end_=S+2*a._proportion_*Math.PI;let o=m;"radius"!=d&&"area"!=d||(o=b+(m-b)*a.value/p),a.radius=o,e.beginPath(),e.moveTo(y,g),e.arc(y,g,o,a._start_,a._end_),e.lineWidth=2,e.strokeStyle=n,e.fillStyle=a.itemStyle.color,e.fill(),f||e.stroke(),b>0&&(e.beginPath(),e.moveTo(y,g),e.arc(y,g,b,a._start_,a._end_),e.fillStyle=n,e.strokeStyle=n,e.stroke(),e.fill()),S=a._end_}),1==t){let{label:t,labelLine:a,title:n}=i.chartPie,{show:c,fontSize:u,color:f,margin:p,format:d}=t,{length1:b,length2:S,lineWidth:w,lineDotRadius:v}=a,A=m+b,D=null,{show:M,text:_,textStyle:P,subtext:T,subtextStyle:k,itemGap:L,backgroundColor:E,borderColor:W,borderWidth:z}=n,{fontSize:j,color:X,lineHeight:Y}=P,{fontSize:I,color:O,lineHeight:B}=k;if(c=l&&"boolean"==typeof l.show?l.show:c,u=l&&l.fontSize?l.fontSize:u,f=l&&l.color?l.color:f,p=l&&l.margin?l.margin:p,d=l&&l.format?l.format:d,c&&s.forEach((t,a)=>{let i=2*Math.PI-(t._start_+2*Math.PI*t._proportion_/2),n=d?d({name:t.name,value:t.value,percent:(t.value/x*100).toFixed(2)}):(t.value/x*100).toFixed(2)+"%",l={x:Math.cos(i)*t.radius,y:Math.sin(i)*t.radius},s={x:Math.cos(i)*A,y:Math.sin(i)*A},c={x:s.x>=0?s.x+S:s.x-S,y:s.y};c=function(t,e,a){if(e)for(;r(t,e,a);)t.x>0?t.y--:t.x<0&&t.y++;return t}(c,D,2*Math.max(v,u/2)),D=c;let y=o(l,h),g=o(s,h),b=o(c,h);e.font=u+"px";let m=e.measureText(n).width,M=Object.assign({},b);c.x>0?M.x+=v+p:M.x-=m+v+p,e.beginPath(),e.moveTo(y.x,y.y),e.quadraticCurveTo(g.x,g.y,b.x,b.y),e.lineWidth=w,e.strokeStyle=t.itemStyle.color,e.stroke(),e.closePath(),e.beginPath(),e.moveTo(b.x,b.y),e.arc(b.x,b.y,v,0,2*Math.PI),e.closePath(),e.fillStyle=t.itemStyle.color,e.fill(),e.font=u+"px",e.textBaseline="middle",e.fillStyle="auto"==f?t.itemStyle.color:f,e.fillText(n,M.x,M.y)}),M){const t=_.split("\n").filter(t=>!!t),a=T.split("\n").filter(t=>!!t);let i=g,n=Y*t.length+B*a.length;T&&(n+=L),i-=n/2,e.save(),e.textAlign="center",e.textBaseline="middle",_&&(i+=Y/2,e.font=j+"px",e.fillStyle=X,t.forEach((t,a,n)=>{e.fillText(t,y,i),a+1==n.length?i+=Y/2:i+=Y}),i+=L),T&&(i+=B/2,e.font=I+"px",e.fillStyle=O,a.forEach(t=>{e.fillText(t,y,i),i+=B})),e.restore()}}}function fe(t){let{context:e,opts:a,chartData:i}=this,{label:n}=a,{center:r}=i.radarAxis;i.chartRadar.forEach(a=>{let{dataPosition:i,itemStyle:l,area:s,line:h,symbol:c,label:u}=a,{show:f,color:x,opacity:p}=s,{show:d,lineWidht:y,color:g,opacity:b}=h,{show:m,type:S,size:w,color:v}=c,{show:A,fontSize:D,color:M,margin:_}=u;if(e.beginPath(),i.forEach((a,i)=>{let n=a.point,l=o({x:n.x*t,y:n.y*t},r),{x:s,y:h}=l;a.position=l,0==i?e.moveTo(s,h):e.lineTo(s,h)}),e.closePath(),f&&(e.save(),e.globalAlpha=p,e.fillStyle="auto"==x?l.color:x,e.fill(),e.stroke(),e.restore()),d&&(e.save(),e.lineWidht=y,e.globalAlpha=b,e.strokeStyle="auto"==g?l.color:g,e.stroke(),e.restore()),1==t){if(m)switch(S){case"circle":e.save(),i.forEach(t=>{let{x:a,y:i}=t.position;e.beginPath(),e.arc(a,i,w/2,0,2*Math.PI),e.fillStyle="auto"==v?l.color:v,e.fill(),e.beginPath(),e.arc(a,i,w/4,0,2*Math.PI),e.fillStyle="#fff",e.fill()}),e.restore()}A=n&&"boolean"==typeof n.show?n.show:A,D=n&&n.fontSize?n.fontSize:D,M=n&&n.color?n.color:M,_=n&&n.margin?n.margin:_,A&&(e.save(),e.font=D+"px",e.fillStyle="auto"==M?l.color:M,e.textAlign="center",e.textBaseline="bottom",i.forEach(t=>{let{x:a,y:i}=t.position;e.fillText(t.data,a,i-_)}))}})}function xe(t){let{context:e,opts:a,chartData:i}=this,{label:n}=a;i.chartScatter.forEach(a=>{let{name:i,data:o,label:r,itemStyle:l,opacity:s,lineWidth:h,strokeColor:c}=a,{show:u,fontSize:f,color:x,margin:p}=r,{color:d}=l;o.forEach(a=>{let{positionX:i,positionY:n,radius:o,color:r}=a;e.save(),e.beginPath(),e.arc(i,n,o*t,0,2*Math.PI),h>0&&(e.strokeStyle="auto"==c?r:c,e.lineWidth=0,e.stroke()),e.fillStyle=r,e.globalAlpha=s,e.fill(),e.restore()}),1==t&&(u=n&&"boolean"==typeof n.show?n.show:u,f=n&&n.fontSize?n.fontSize:f,x=n&&n.color?n.color:x,p=n&&n.margin?n.margin:p,u&&(e.save(),e.font=f+"px",e.fillStyle="auto"==x?d:x,o.forEach(t=>{let{y:a,z:n,radius:o,name:r,positionX:l,positionY:s}=t,h=r||(n||i);"string"!=typeof d?(e.textAlign="center",e.textBaseline="bottom",e.fillText(h,l,s-o-p)):(e.textAlign="center",e.textBaseline="middle",e.fillText(h,l,s))}),e.restore()))})}function pe(t){let{context:e,opts:a,chartData:i}=this,{data:n,funnelAlign:o,itemStyle:r,label:l}=i.chartFunnel,{borderColor:s,borderWidth:h}=r;if(n.forEach(a=>{let{point:i,itemStyle:n}=a,{color:o}=n;e.beginPath(),i.forEach((a,i)=>{let{x:n,y:o}=a;0==i?e.moveTo(n,o*t):e.lineTo(n,o*t)}),e.closePath(),h>0&&(e.strokeStyle=s,e.lineWidth=h,e.stroke()),e.fillStyle=o,e.fill()}),1==t){let{label:t}=a,{show:i,fontSize:r,color:s,margin:h,position:c}=l;i=t&&"boolean"==typeof t.show?t.show:i,r=t&&t.fontSize?t.fontSize:r,s=t&&t.color?t.color:s,h=t&&t.margin?t.margin:h,i&&(e.save(),n.forEach(t=>{let{name:a,itemStyle:i,textPoint:n}=t,{x:l,y:h}=n;"inside"==c?(e.textAlign="center",e.textBaseline="middle",e.font=r+"px",e.strokeStyle=i.color,e.fillStyle="#ffffff",e.strokeText(a,l,h),e.fillText(a,l,h)):(e.textAlign="right"==o?"right":"left",e.textBaseline="middle",e.font=r+"px",e.fillStyle="auto"==s?i.color:s,e.fillText(a,l,h))}),e.restore())}}function de(t){let{context:e,opts:a,chartData:i,seriesMap:n}=this,o=c(n.candlestick),{highLine:r,lowLine:l,bar:s}=o[0],{rect:h,highLine:u,lowhLine:f,bar:x}=i.chartCandlestick;if(e.save(),h.forEach(t=>{let{color:a,bordercolor:i,opacity:n,borderWidth:o,rectPoint:r,upLinePoint:l,downLinePoint:s}=t,{x:h,y:c,width:u,height:f}=r,{startX:x,startY:p,endX:d,endY:y}=l,{startX:g,startY:b,endX:m,endY:S}=s;e.strokeStyle=i,e.fillStyle=a,e.globalAlpha=n,e.strokeRect(h+o/2,c+o/2,u-o,f-o),e.fillRect(h,c,u,f),e.beginPath(),e.lineWidth=1,e.strokeStyle=i,e.moveTo(x,p),e.lineTo(d,y),e.stroke(),e.moveTo(g,b),e.lineTo(m,S),e.stroke()}),e.restore(),1==t){let{show:t,lineStyle:a}=r,{show:i,lineStyle:n}=l,{show:o,itemStyle:h,lineStyle:c}=s,{color:p,lineWidth:d,lineDash:y,opacity:g}=a,{color:b,lineWidth:m,lineDash:S,opacity:w}=n,{startX:v,startY:A,endX:D,endY:M}=u,{startX:_,startY:P,endX:T,endY:k}=f,{color:L,opacity:E}=h,{lineWidth:W,lineColor:z}=c;if(t&&(e.save(),e.beginPath(),e.moveTo(v,A),e.lineTo(D,M),e.strokeStyle=p,e.lineWidth=d,e.setLineDash(y),e.globalAlpha=g,e.stroke(),e.restore()),i&&(e.save(),e.beginPath(),e.moveTo(_,P),e.lineTo(T,k),e.strokeStyle=b,e.lineWidth=m,e.setLineDash(S),e.globalAlpha=w,e.stroke(),e.restore()),o){const{lineStartX:t,lineStartY:a,lineEndX:i,lineEndY:n,data:o}=x;e.save(),e.lineWidth=W,e.strokeStyle=z,e.moveTo(t,a),e.lineTo(i,n),e.stroke(),e.restore(),o.forEach(t=>{let{color:a,x:i,y:n,width:o,height:r}=t;e.save(),e.beginPath(),e.fillStyle="auto"==L?a:L,e.globalAlpha=E,e.fillRect(i,n,o,-r),e.restore()})}}}function ye(t){let{context:e,opts:a,chartData:i}=this,{xEachSpacing:n,yEachSpacing:o}=i.axisData,{label:r}=a;const l=a.xAxis.axisSplitLine.lineStyle.lineWidth,s=a.yAxis.axisSplitLine.lineStyle.lineWidth;i.chartHeatmap.forEach(a=>{let{data:i,label:h}=a,{show:c,fontSize:u,color:f,margin:x}=h;i.forEach(a=>{let{positionX:i,positionY:r,color:h,useSplit:c}=a;e.save(),e.beginPath(),c?e.rect(i+2*s,r+2*l,n-4*s,o-4*l):e.rect(i,r,n,o),e.fillStyle=h,e.globalAlpha=t,e.fill(),e.restore()}),1==t&&(c=r&&"boolean"==typeof r.show?r.show:c,u=r&&r.fontSize?r.fontSize:u,f=r&&r.color?r.color:f,x=r&&r.margin?r.margin:x,c&&(e.save(),e.font=u+"px",e.fillStyle="auto"==f?"#ffffff":f,e.textAlign="center",e.textBaseline="middle",i.forEach(t=>{let{positionX:a,positionY:i}=t,r=t[2];e.fillText(r,a+n/2,i+o/2)}),e.restore()))})}function ge(t){let{context:e,opts:a,chartData:i}=this,{label:n}=a,{label:o,splitLine:r}=i.chartTreemap.data,{show:l,fontSize:s,color:h,margin:c}=o,{show:u,lineWidth:f,color:x}=r;e.save(),i.chartTreemap.children.forEach((a,i)=>{let{x0:n,y0:o,x1:r,y1:l,data:s}=a,h=r-n,c=l-o;e.fillStyle=s.itemStyle.color,e.globalAlpha=t,u&&(e.lineWidth=f,e.strokeStyle=x,e.strokeRect(n,o,h,c)),e.fillRect(n,o,h,c)}),e.restore(),1==t&&(l=n&&"boolean"==typeof n.show?n.show:l,s=n&&n.fontSize?n.fontSize:s,h=n&&n.color?n.color:h,c=n&&n.margin?n.margin:c,l&&(e.save(),i.chartTreemap.children.forEach(t=>{let{x0:a,y0:i,x1:n,y1:o,data:r}=t,{name:l,itemStyle:s}=r,c=a+(n-a)/2,u=i+(o-i)/2,f=Math.min(n-a,o-i);e.textAlign="center",e.textBaseline="middle",e.font=.2*f+"px",e.strokeStyle="auto"==h?s.color:h,e.fillStyle="#ffffff",e.strokeText(l,c,u),e.fillText(l,c,u)}),e.restore()))}function be(t){let{opts:e,chartData:a}=this,{element:i,width:n,height:o}=e;i.width=n,i.height=o;const r=i.getContext("2d");a.chartTagCloud.data.forEach(t=>{let{text:e,x:a,y:i,font:l,size:s,rotate:h,itemStyle:c}=t;r.save(),r.beginPath(),r.font=`${s}px ${l}`,r.fillStyle=c.color,r.textAlign="center",r.translate(n/2+a,o/2+i),r.rotate(h*Math.PI/180),r.fillText(e,0,0),r.restore()})}function me(){const{animation:t,animationDuration:e,animationTiming:a}=this.opts;this.animationInstance&&this.animationInstance.stop(),this.animationInstance=new te({animation:t,animationDuration:e,animationTiming:a,onProcess:t=>{ae.call(this),(this.seriesMap.line||this.seriesMap.bar||this.seriesMap.scatter||this.seriesMap.candlestick||this.seriesMap.heatmap)&&(oe.call(this),le.call(this)),Object.keys(this.seriesMap).forEach(e=>{switch(e){case"bar":he.call(this,t);break;case"line":ce.call(this,t);break;case"pie":ue.call(this,t);break;case"radar":se.call(this),fe.call(this,t);break;case"scatter":xe.call(this,t);break;case"funnel":pe.call(this,t);break;case"candlestick":de.call(this,t);break;case"heatmap":ye.call(this,t);break;case"treemap":ge.call(this,t);break;case"tagCloud":be.call(this,t)}}),(this.seriesMap.line||this.seriesMap.bar||this.seriesMap.scatter||this.seriesMap.candlestick||this.seriesMap.heatmap)&&(ne.call(this),re.call(this)),1===t&&(ie.call(this),ee.call(this))},onAnimationFinish:()=>{this.event.trigger("renderComplete")}})}export default class{constructor(a={}){this.config=Object.assign({},e),this.opts=Object.assign({},a),this.context=this.opts.element.getContext("2d"),this.tooltipData={tooltipTitle:"",data:[],maxTextWidth:0,offset:{}},this.legendData={},this.seriesMap={},this.chartData={},this.event=new t,this.event.addEventListener("renderComplete",a.onRenderComplete),this.scrollOption={currentOffset:0,startTouchX:0,distance:0},Kt.call(this),me.call(this)}updateData(t={}){Object.keys(t).forEach(e=>{"series"==e?(this.opts.series=c(t.series),x.call(this)):u(t,e,this.opts,e,!0)}),Kt.call(this),me.call(this)}showTooltip(t){const{animation:e,tooltip:a}=this.opts,i=e;if(!a.show)return;const{currentData:n,offset:o}=this.getCurrentIndex(t);this.tooltipData={tooltipTitle:"",data:[],maxTextWidth:0,offset:o},Object.keys(n).forEach(t=>{const e=n[t];switch(t){case"bar":S.call(this,e);break;case"line":m.call(this,e);break;case"pie":w.call(this,e);break;case"radar":v.call(this,e);break;case"candlestick":case"k":A.call(this,e)}}),Object.keys(n).forEach(t=>{switch(t){case"bar":case"line":case"candlestick":case"k":this.tooltipData.axisPointerData||b.call(this,n[t])}}),g.call(this),this.opts,this.opts.animation=!1,me.call(this),this.opts.animation=i}hideTooltip(){const{opts:t,tooltipData:e}=this,{animation:a}=t,i=a;this.tooltipData={tooltipTitle:"",data:[],maxTextWidth:0,offset:{}},this.opts.animation=!1,me.call(this),this.opts.animation=i}getCurrentIndex(t){const e=t.touches&&t.touches.length?t.touches:t.changedTouches,a={x:e[0].offsetX||0,y:e[0].offsetY||0};let i={};return Object.keys(this.seriesMap).forEach(t=>{switch(t){case"bar":case"line":case"candlestick":case"k":i[t]=p.call(this,a);break;case"pie":i[t]=d.call(this,a);break;case"radar":i[t]=y.call(this,a)}}),this.chartData,{currentData:i,offset:a}}scrollStart(t){t.touches[0]&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=t.touches[0].clientX)}scroll(t){if(t.touches[0]&&!0===this.opts.enableScroll){let e=t.touches[0].clientX-this.scrollOption.startTouchX,{currentOffset:a}=this.scrollOption,i=function(t,e,a,i){let n=i.width-a.padding[0]-e.axisData.xAxisLabelPoint[0].x,o=e.axisData.xEachSpacing*i.xAxis.data.length,r=t;return t>=0?r=0:Math.abs(t)>=o-n&&(r=n-o-e.axisData.xEachSpacing/2),r}(a+e,this.chartData,this.opts,this.opts);this.scrollOption.distance=e=i-a;let n=Object.assign({},this.opts,{_scrollDistance_:a+e,animation:!1});this.opts=n,n._scrollDistance_,me.call(this)}}scrollEnd(t){if(!0===this.opts.enableScroll){let{currentOffset:t,distance:e}=this.scrollOption;this.scrollOption.currentOffset=t+e,this.scrollOption.distance=0}}}
//# sourceMappingURL=qacharts-min.js.map
