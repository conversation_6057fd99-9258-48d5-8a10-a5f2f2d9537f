<template>
  <div class="{{tabbar.length !== 0 ? 'wrap' : ''}}">
    <slot content="content"></slot>
    <div class="tab-bar" if="{{tabbar.length !== 0}}">
      <div class="shadow"></div>
      <div class="tab tab-recommend">
        <div for="{{tab in tabbar}}">
          <image
            data-path="{{tab.pagePath}}"
            onclick="changePage"
            src="{{tab.active?tab.selectedIconPath:tab.iconPath}}"
            if="{{tab.iconPath}}"
          ></image>
          <text
            data-path="{{tab.pagePath}}"
            onclick="changePage"
            class="{{tab.active?'selected':''}}"
            >{{ tab.text }}</text
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";

.wrap {
  padding-bottom: 108px * @ratio;
}

.tab-bar {
  .shadow {
    position: fixed;
    bottom: 108px * @ratio;
    height: 9px * @ratio;
    width: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.09));
  }

  .tab {
    background-color: rgba(255, 255, 255, 0.98);
    position: fixed;
    bottom: 0;
    flex-direction: row;
    height: 108px * @ratio;
    width: 100%;
    justify-content: space-around;

    div {
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    image {
      width: 58px * @ratio;
      height: 58px * @ratio;
    }

    text {
      font-size: 30px * @ratio;
    }
  }

  .selected {
    color: #0091ff;
  }

  .tab-recommend {
    left: 0;
  }

  .tab-mine {
    right: 0;
  }
}
</style>

<script>
import router from "@system.router";

export default {
  data() {
    return {
      isRecommendPage: true
    };
  },
  props: {
    tabbar: {
      default: []
    }
  },
  changePage(e) {
    router.push({
      uri: e.target.dataset.path,
      params: {
        ___PARAM_LAUNCH_FLAG___: "clearTask"
      }
    });
  }
};
</script>
