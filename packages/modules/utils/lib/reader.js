/**
 * 创建章节内容：主要是创建移除换行，每一段都放入一个数组，每一段前面加两个空格
 * 每一段是已换行识别的
 * 如果不移除这些换行符，后面计算很容易出错
 * @returns *[]
 * @param {string} chapterContent  章节内容
 */
export function createChapterContent(chapterContent) {
  if (chapterContent && 'string' === typeof chapterContent) {
    let result = []
    chapterContent
      .replace(/(\r\n)|(\n\r)|(\n)/g, '\n')
      .split('\n')
      .forEach(t => {
        let txt = t.replace(/(^\s*)|(\s*$)/g, '')
        txt && result.push('　　' + txt)
      })

    return result
  }
}

/**
 * 把内容转为很多页的数组
 *
 * @param {Array<string>} chapterContent  章节内容,每一项都是一段内容
 * @param {number} width 容器宽度
 * @param {number} height    容器高度
 * @param {number} fontSize
 * @param {number} lineHeight
 * @param {string} chapterName
 * @param type
 * @returns {Array<{type: string, lines: 0, content: string[]}>} 每一页的数据,type: 标注内容是小说还是广告或者是标题之类的
 */
export function calcChapterContent({
  chapterContent,
  width = 750,
  height,
  fontSize,
  lineHeight,
  chapterName = '第一章',
}) {
  let pageContent = [] // 每一页的内容，一项就是一页内容
  let lineWord = [] // 每一行的字

  if (width && height) {
    let maxLines = Math.floor(height / Math.ceil(lineHeight)), // 最大行数
      lineWordCount = Math.floor(width / fontSize) // 每行字数

    chapterName && lineWord.push(chapterName)

    chapterContent.forEach(e => {
      for (let i = 0; i < e.length; i += lineWordCount) {
        // 一个数组就是一段内容
        // 把一段内容拆成很多数组
        lineWord.push(e.slice(i, i + lineWordCount))
      }
    })

    for (let i = 0; i < lineWord.length; i += maxLines) {
      pageContent.push({
        type: 'content', // 说明是展示的内容
        lines: i, // 每一页第几行的起点
        content: lineWord.slice(i, i + maxLines), // 第几页的内容
      })
    }

    return pageContent
  }
}

/**
 * 把段落转为很多页的数组
 * @param chapterContent 章节内容
 * @param fontSize
 * @param lineHeight
 * @param height
 * @param width
 * @param chapterName
 * @return {Array<{type: string, lines: 0, content: string[]}>} 每一页的内容
 */
export function loadReader({
  chapterContent,
  fontSize,
  lineHeight,
  height,
  width,
  chapterName,
}) {
  console.log('----------loadReader----------渲染翻页阅读器')
  let chapterList = createChapterContent(chapterContent)
  console.log('chapterList', chapterList)
  return calcChapterContent({
    chapterContent: chapterList,
    fontSize,
    lineHeight,
    height,
    width,
    chapterName,
  })
}
