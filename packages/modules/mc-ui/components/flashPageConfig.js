import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'
import globalData from '@quickapp/utils/lib/globalData'

export default setPageMenuConfig({
  public: {
    clickid: '',
    click_type: '',
    auto_agree: '0', // 1：自动跳转；0：点击跳转
    browser_info: '',
    book_id: null, // 小说id 66或5cfe0aaa56c75f15e0ba8139
    chapter_id: null, // 章节id 45275
    account_id: null,
  },

  private: {
    userPageParams: {
      uri: __MANIFEST__.userUrl,
      title: '用户协议',
    },
    privacyPageParams: {
      uri: __MANIFEST__.privacyUrl,
      title: '隐私协议',
    },
  },

  onInit() {
    if (this.$app.$def && this.$app.$def.protected) {
      this.userPageParams.uri += this.$app.$def.protected.appName
      this.privacyPageParams.uri += this.$app.$def.protected.appName
    }
    if (this.account_id) {
      globalData.accountId = this.account_id
    }
  },
})
