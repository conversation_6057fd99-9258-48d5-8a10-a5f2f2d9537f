<template>
  <div class="container">
    <div if="{{showTitleBar}}" class="titleBar">
      <div class="titleBar-back">
        <text show="{{showBack}}" class="titleBar-text" onClick="back">
          返回
        </text>
      </div>
      <text class="titleBar-text titleBar-title">
        {{ title }}
      </text>
      <div class="titleBar-refresh">
        <!--<text class="titleBar-text" onClick="refresh">首页</text>-->
      </div>
    </div>
    <web
      class="web-page"
      src="{{webSrc}}"
      trustedurl="{{list}}"
      onpagestart="onPageStart"
      onpagefinish="onPageFinish"
      ontitlereceive="onTitleReceive"
      onerror="onError"
      onmessage="onMessage"
      id="web"
    ></web>
  </div>
</template>

<script>
export default {
  props: {
    webSrc: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    showTitleBar: {
      type: Boolean,
    },
  },

  data: {
    // title: '',
    // webSrc: '',
    arrowRightShow: true,
    isRefreshing: false,
    // list: [/.*/, "new RegExp('https?.*')"],
    list: [/.*/],
    msg: '',
    showBack: false,
  },
  onInit() {
    this.$on('arrowLeft', this.arrowLeftIcon)
    this.$on('arrowRight', this.arrowRightIcon)
    console.info('webSrc: ' + this.webSrc)
  },

  onPageStart(e) {
    this.isRefreshing = false
    console.info('pagestart: ' + e.url)
  },
  // 每次页面的切换会触发
  onPageFinish(e) {
    // console.info('pagefinish: '+e.url, e.canBack, e.canForward)
    // 根据数据是否可以前进历史页面，隐藏右侧图标
    this.arrowRightShow = e.canForward
    this.changeTitleBarVisible()
  },

  changeTitleBarVisible() {
    this.$element('web').canBack({
      callback: e => {
        this.showBack = e
      },
    })
  },

  onTitleReceive(e) {
    console.error('onTitleReceive', e.title)
    // this.title = e.title
  },
  onError() {
    console.info('pageError')
  },
  onMessage(e) {
    console.info('onmessage e = ' + e.message + ', url = ' + e.url)
    this.msg = e.message
  },
  arrowLeftIcon() {
    this.isCanBack()
  },
  arrowRightIcon() {
    this.isCanForward()
  },
  isCanForward() {
    this.$element('web').canForward({
      callback: function (e) {
        if (e) {
          this.$element('web').forward()
        }
      }.bind(this),
    })
  },
  isCanBack() {
    this.$element('web').canBack({
      callback: function (e) {
        this.showBack = e
        if (e) {
          this.$element('web').back()
        } else {
        }
      }.bind(this),
    })
  },
  back() {
    this.$dispatch('arrowLeft')
  },
  next() {
    this.$dispatch('arrowRight')
  },
  refresh: function (e) {
    this.$element('web').reload()
    // this.$emit('refresh')
  },
  sendMessage: function () {
    this.$element('web').postMessage({ message: 'message to Web page' })
  },
}
</script>

<style>
.container {
  width: 100%;
  height: 100%;
  flex-direction: column;
}

.titleBar {
  flex-direction: row;
  padding: 10px 30px;
  height: 100px;
  width: 100%;
}

.titleBar-refresh,
.titleBar-back {
  width: 100px;
}

.titleBar-refresh {
  justify-content: flex-end;
}

.titleBar-text {
  lines: 1;
  text-overflow: ellipsis;
  color: #000000;
  align-content: center;
}

.titleBar-title {
  flex: 1;
  text-align: center;
  font-size: 36px;
  font-weight: bolder;
}

.web-page {
  width: 100%;
  height: 800px;
}
</style>
