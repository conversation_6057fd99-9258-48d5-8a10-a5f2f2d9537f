<!--
  图片和文字的布局
  例如：
    左侧图片，右侧文字
-->
<template>
  <div class="wrapper" style="align-items: center">
    <image src="{{imgUrl}}" style="{{imgStyle}}"></image>
    <div class="main" style="margin-left: {{space}}">
      <slot name="main">
        <text class="main__title" style="{{titleStyle}}">{{ title }}</text>
        <text class="main__sub-title" style="{{subTitleStyle}}">
          {{ subTitle }}
        </text>
      </slot>
    </div>
    <div class="slide">
      <slot name="slide"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    imgUrl: {
      default:
        'http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/logo.png',
    },
    imgStyle: {
      default: 'width: 120px; height: 120px; border-radius: 60px;',
    },
    title: {
      default: '标题',
    },
    titleStyle: {
      default: '',
    },
    subTitle: {
      default: '中间部分',
    },
    subTitleStyle: {
      default: '',
    },
    space: {
      default: '30px',
    },
  },

  data() {
    return {}
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
}

.main {
  flex: 1;
  flex-direction: column;

  &__title {
    font-weight: bold;
    font-size: 36px;
  }

  &__sub-title {
    font-size: 24px;
    margin-top: 20px;
  }
}

.slide {
}
</style>
