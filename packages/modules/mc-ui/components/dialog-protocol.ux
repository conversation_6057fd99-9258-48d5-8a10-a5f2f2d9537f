<!--
  同意协议弹窗
-->
<template>
  <div show="{{showDialog}}" class="dialog-wrapper">
    <div class="dialog">
      <text class="title">欢迎您使用{{ appName }}</text>
      <div class="content">
        <text class="text">
          <span>
            感谢您使用{{ appName }}！为了给您提供更优质的服务，{{
              appName
            }}将使用您的个人信息，详情可阅读
          </span>
          <a @click="toPrivacyProtocol" style="color: #677dff">《隐私政策》</a>
          <span>及</span>
          <a @click="toUserProtocol" style="color: #677dff">《用户协议》</a>
          <span>
            使用前请认真阅读，点击“同意”，视为您同意上述内容，点击“不同意”，则退出本应用。
          </span>
        </text>
      </div>
      <div class="footer">
        <input
          class="agree"
          @click="handleAgree"
          type="button"
          value="同意并进入"
        />
        <text class="reject" @click="handleReject">不同意</text>
      </div>
    </div>
  </div>
</template>

<script>
import storage from '@system.storage'

const STORAGE_KEY_AGREE = '__is-agree__'

export default {
  props: {
    autoClick: {
      type: Boolean,
      default: false,
    },
  },

  data: {
    showDialog: false,
  },

  onInit() {
    console.log('dialog-protocol')
    storage.get({
      key: STORAGE_KEY_AGREE,
      success: data => {
        if (data) {
          this.showDialog = false
          this.$emit('close')
        } else {
          if (this.autoClick) {
            this.handleAgree()
          } else {
            this.showDialog = true
            this.$emit('show')
          }
        }
      },
    })
  },
  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },
  toPrivacyProtocol() {
    this.$emit('toPrivacy')
  },
  toUserProtocol() {
    this.$emit('toUser')
  },
  handleAgree() {
    storage.set({
      key: STORAGE_KEY_AGREE,
      value: 'true',
      success: () => {
        this.showDialog = false

        this.$emit('close')
        this.$emit('agree')
      },
      fail: (data, code) => {
        console.log(`handling fail, code = ${code}`)
      },
    })
  },
  handleReject() {
    // this.$page.finish()
    this.$app.exit()
    this.$emit('reject')
  },
}
</script>

<style lang="less">
.dialog-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 99;

  .dialog {
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 560px;
    /* height: 850px; */
    padding: 50px 50px 40px;
    background-color: #ffffff;
    border-radius: 32px;

    .title {
      font-size: 36px;
      font-weight: bolder;
      color: #111111;
      line-height: 44px;
    }

    .content {
      flex: 1;
      flex-direction: column;
      margin: 26px 0;

      .text {
        line-height: 45px;
      }
    }

    .footer {
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;

      .agree {
        width: 460px;
        height: 88px;
        background-color: #677dff;
        border-radius: 44px;
        font-size: 32px;
        font-weight: bolder;
        color: #ffffff;
      }

      .reject {
        font-size: 28px;
        color: #999999;
        margin-top: 24px;
      }
    }
  }
}
</style>
