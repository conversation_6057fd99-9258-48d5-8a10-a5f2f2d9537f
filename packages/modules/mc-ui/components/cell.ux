<template>
  <div class="wrapper" @click="handleClick">
    <image if="{{icon}}" class="icon-left" src="{{icon}}"></image>
    <text class="title">{{ title }}</text>
    <text class="placeholder">{{ placeholder }}</text>
    <image
      if="{{showIconRight}}"
      class="icon-right"
      src="{{iconRight}}"
    ></image>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  props: {
    icon: {
      default: '',
    },
    title: {
      default: '',
    },
    placeholder: {
      default: '',
    },
    iconRight: {
      default:
        'http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_arrow_right.webp',
    },
    showIconRight: {
      default: true,
    },
    url: {
      default: '',
    },
    isLink: {
      default: false,
    },
  },
  handleClick() {
    if (this.isLink && this.url) {
      router.push({
        uri: this.url,
      })
      return
    }
    this.$emit('click-item', {
      title: this.title,
      placeholder: this.placeholder,
    })
  },
}
</script>

<style lang="less">
.wrapper {
  align-items: center;
  height: 100px;
}

.icon-left {
  width: 46px;
  height: 46px;
  margin-right: 14px;
}

.title {
  font-size: 26px;
  font-weight: bold;
  color: #333333;
}

.placeholder {
  margin-left: auto;
  font-size: 28px;
  color: #666666;
}

.icon-right {
  width: 58px;
  height: 58px;
}
</style>
