import device from '@quickapp/business/lib/device'
import { isFileExists, registerDevice } from '@quickapp/utils'
import { API_HOST } from '@quickapp/utils/lib/request'
import router from '@system.router'
import config from '@quickapp/business/lib/config'
import { register, trackEvent } from '@quickapp/business'
import ad from '@service.ad'
import globalData from '@quickapp/utils/lib/globalData'
import adsdk from '@quickapp/business/lib/adsdk'

const adChannel = new BroadcastChannel('ad')
const DEBUG = process.env.NODE_ENV !== 'production'

export default {
  public: {
    showSplash: true,
    clickid: '',
    click_type: '',
    account_id: null,
    channel: null,
  },

  async onInit() {
    if (this.account_id) {
      globalData.accountId = this.account_id
    }
    if (this.channel) {
      globalData.channel = this.channel
    }
    adsdk.startAdPreload(this)
    adChannel.onmessage = e => {
      if (e.data === 'onAdLimit') {
        this.toMainPage()
      }
    }
    if (!device.parseClickId(this.clickid, this.click_type)) {
      await this.toMainPage()
      return
    }
    setTimeout(() => {
      this.initConfig()
    })
  },

  async initConfig() {
    await config.updateConfig()
    this.$broadcast('onConfigLoaded')
    let realClickId = globalData.realClickId || this.clickid
    await register(realClickId)
    globalData.isDebug =
      DEBUG || (await isFileExists('internal://mass/debugtxt'))
    if (!globalData.isDebug) {
      let blockAd = config.getAdConfig()['block_ad'] || false
      if (blockAd) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'block_ad',
        })
        await this.toMainPage()
        return
      }
      if (!globalData.marketCode) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'market_code',
        })
        await this.toMainPage()
        return
      }
      if (await device.isBanned()) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'black_app',
        })
        await this.toMainPage()
        return
      }
      if (!(await adsdk.checkAdLimit())) {
        trackEvent({
          category: 'device',
          action: 'ban',
          opt_label: 'ad_limit',
        })
        await this.toMainPage()
        return
      }
    }
    trackEvent({
      category: 'device',
      action: 'ad_open',
    })
  },

  onShow() {
    setTimeout(() => {
      this.requestFullscreen()
    })
    this.$broadcast('onPageShow')
  },
  onHide() {
    setTimeout(() => {
      if (!this.$visible) {
        try {
          let provider = ad.getProvider()?.toLowerCase()
          if (provider === 'honor') {
          } else {
            this.$element('test2')?.exitFullscreen()
          }
        } catch (e) {}
      }
    }, 1000)
    this.$broadcast('onPageHide')
  },
  onRefresh(query) {
    // console.log('onRefresh', JSON.stringify(query))
    if (query.clickid) {
      trackEvent({
        category: 'device',
        action: 'pullup',
      })
    }
    this.$broadcast('onPageRefresh')
  },
  onBackPress() {
    this.$broadcast('onPageBack')
    return true
  },
  requestFullscreen() {
    try {
      let provider = ad.getProvider()?.toLowerCase()
      if (provider === 'honor') {
        this.$element('stack').requestFullscreen()
      } else {
        this.$element('test2')?.requestFullscreen()
      }
    } catch (e) {}
  },
  registerDeviceRequest() {
    const type = this.appType || 'novel'
    return registerDevice(API_HOST + `/${type}/user/register_device`)
  },
  async toMainPage() {
    await this.registerDeviceRequest()
    router.replace({
      uri: 'pages/Home',
      params: {
        ___PARAM_PAGE_ANIMATION___: {
          openEnter: `none`,
          closeEnter: `slide`,
          openExit: `slide`,
          closeExit: `slide`,
        },
      },
    })
  },
}
