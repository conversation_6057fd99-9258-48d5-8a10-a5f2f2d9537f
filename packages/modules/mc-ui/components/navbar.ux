<import name="apex-icon" src="@quickapp/apex-ui/components/icon/index"></import>

<template>
  <div class="navbar">
    <div
      class="wrapper"
      style="background-color: {{bgColor}}; position: {{fixed ? 'fixed' : 'relative'}}"
    >
      <div if="showStatusBar" style="{{statusBarStyle}}"></div>
      <div class="inner">
        <text
          class="title"
          style="color: {{titleColor}}; text-align: {{textAlign}}"
          >{{ title }}</text
        >
        <image
          if="showBack"
          @click="toBack"
          class="ic-back"
          src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/{{theme === 'dark' ? 'ic_back_light' : 'ic_back'}}.png"
        ></image>
      </div>
    </div>
    <div if="{{showStatusBar && fixed}}" style="{{statusBarStyle}}"></div>
    <div if="{{fixed}}" class="placeholder"></div>
  </div>
</template>

<script>
import router from "@system.router";

export default {
  props: {
    showStatusBar: {
      type: Boolean,
    },
    bgColor: {
      type: String,
      default: "#fff",
    },
    titleColor: {
      type: String,
      default: "#212121",
    },
    textAlign: {
      type: String,
      default: "center",
    },
    showBack: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    fixed: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: String,
      default: "light",
    },
  },

  computed: {
    statusBarStyle() {
      return {
        height: this.$page.statusBarHeight + "px",
      };
    },
  },

  onInit() {},

  toBack() {
    router.back();
  },
};
</script>

<style lang="less">
.navbar {
  flex-direction: column;
}

.wrapper {
  //position: fixed;
  //position: relative;
  width: 100%;
  top: 0;
  left: 0;
  flex-direction: column;
}

.placeholder {
  width: 100%;
  height: 88px;
}

.inner {
  position: relative;
  height: 88px;
  padding: 0 30px;
  align-items: center;

  .title {
    width: 100%;
    padding: 0 30px;
    font-size: 36px;
  }

  .ic-back {
    position: absolute;
    left: 30px;
    width: 60px;
    height: 60px;
  }
}
.title {
  //text-align: center;
  //color: #212121;
}
</style>
