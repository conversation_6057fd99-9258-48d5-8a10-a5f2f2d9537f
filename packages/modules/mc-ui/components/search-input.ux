<template>
  <div class="wrapper">
    <div class="box">
      <image
        class="box__icon"
        src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_search.png"
      ></image>
      <input
        class="box__inp"
        type="text"
        placeholder="{{defaultPlaceholder}}"
        @change="updateTextValue"
        value="{{inputValue}}"
      />
      <image
        if="{{inputValue}}"
        @click="handleClear"
        class="box__close"
        src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_close.png"
      ></image>
    </div>
    <text class="btn" @click="handleSearch">搜索</text>
  </div>
</template>

<script>
export default {
  props: {
    defaultText: {
      default: '',
    },
    defaultPlaceholder: {
      default: '请输入小说名或作者名',
    },
  },

  data() {
    return {
      inputValue: '',
    }
  },

  onInit() {
    this.inputValue = this.defaultText
  },

  updateTextValue(e) {
    this.inputValue = e.value
    this.$emit('change', {
      text: this.inputValue,
    })
  },

  handleClear() {
    this.inputValue = ''
  },

  handleSearch() {
    this.$emit('search', {
      text: this.inputValue,
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 77px;
  align-items: center;
  padding: 0 30px;
}

.box {
  flex: 1;
  height: 77px;
  background-color: #f7f7f7;
  border-radius: 20px;
  align-items: center;
  padding: 0 20px;

  &__icon {
    width: 48px;
    height: 48px;
    margin-right: 20px;
  }

  &__close {
    width: 32px;
    height: 32px;
    margin-left: 20px;
  }

  &__inp {
    flex: 1;
    font-size: 28px;
    color: #333;
    line-height: 28px;
  }
}

.btn {
  padding-left: 26px;
  color: #333333;
}
</style>
