<template>
  <picker
    id="picker"
    class="picker"
    type="multi-text"
    range="{{range}}"
    selected="{{selected}}"
    value="{{value}}"
    @change="handleChange"
    @columnchange="handleColumnChange"
    @cancel="handleCancel"
  ></picker>
</template>

<script>
export default {
  props: {
    // 年的取值范围，在今年上下 10 年内
    threshold: {
      default: 10
    },
    showDay: {
      default: false
    },
    rangeType: {
      // 取值: all | before | after
      // 往后的阈值，还是往前，后者前后都加
      default: 'all'
    }
  },

  data() {
    return {
      range: [],
      value: "",
      // 取的是索引值
      selected: [0, 0],
      yearList: [],
      monthList: []
    };
  },

  onInit() {
    const date = new Date();
    const fullYear = date.getFullYear();
    const month = date.getMonth()
    const dayList = []
    let start = fullYear - this.threshold
    let end = fullYear + this.threshold

    if (this.rangeType === 'before') {
      end = fullYear
    }

    if (this.rangeType === 'after') {
      start = fullYear
    }

    for (let i = start; i < end; i++) {
      this.yearList.push(i);
    }

    for (let i = 1; i <= 12; i++) {
      this.monthList.push(i);
    }

    // 默认年的索引
    const defaultYearIndex = this.threshold
    // 对应的年
    const selectYear = this.yearList[defaultYearIndex]
    // 默认月的索引
    const defaultMonthIndex = 0
    // 对应的月份
    const selectMonth = this.monthList[defaultMonthIndex]

    this.selected = [defaultYearIndex, defaultMonthIndex];
    this.range = [this.yearList, this.monthList];

    if (this.showDay) {
      this.setDaysListAndSelectedDay(selectYear, selectMonth)
    }
  },

  // 设置天
  setDaysListAndSelectedDay(year, month) {
    const dayList = []
    // 对应的月份有多少天
    const monthDays = this.getMonthDays(year, month)
    for (let i = 0; i < monthDays; i++) {
      dayList.push(i + 1)
    }
    this.range = [
    this.yearList,
    this.monthList,
    dayList,
    ]
    this.selected = [
      this.selected[0],
      this.selected[1],
      0
    ]
  },

  handleChange(e) {
    this.$emit("dateChange", e);
  },

  handleColumnChange(e) {
    this.selected[e.column] = e.newSelected
    if (e.column === 1 && this.showDay) {
      this.setDaysListAndSelectedDay(Number(this.selected[0]), Number(this.selected[1]))
    }
    this.$emit("dateColumnChange", e);
  },

  handleCancel(e) {
    this.$emit("dataCancel", e);
  },

  show() {
    this.$element("picker").show();
  },

  // 获取某年某月有多少天,
  // 如果用日期对象获取的月份需要加 1
  getMonthDays(year, month) {
    const date = new Date(year, month, 0)
    return date.getDate()
  },
};
</script>
