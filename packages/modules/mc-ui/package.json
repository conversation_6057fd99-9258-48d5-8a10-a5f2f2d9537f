{"name": "@quickapp/mc-ui", "version": "0.0.0", "description": "> TODO: description", "author": "guojun <<EMAIL>>", "homepage": "", "license": "ISC", "main": "lib/index.js", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "publishConfig": {"registry": "https://registry.npm.taobao.org"}, "repository": {"type": "git", "url": "https://codeup.aliyun.com/muchun/quickapp.git"}, "scripts": {"test": "echo \"Error: run tests from root\" && exit 1"}, "devDependencies": {"less": "^4.1.1", "less-loader": "^10.0.1"}, "dependencies": {"@quickapp/business": "^0.0.0", "@quickapp/apex-ui": "^1.9.5", "@quickapp/utils": "^0.0.0"}}