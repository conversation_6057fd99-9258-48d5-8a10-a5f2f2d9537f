import { trackEvent } from '../index'

class YlhRewardedAd {
  rewardVideo = null
  loadResolve = null
  ecpm = -1

  constructor(context, adUnitId) {
    this.context = context
    this.adUnitId = adUnitId
    this.initRewardedVideo()
  }
  initRewardedVideo() {
    if (!this.adUnitId) return
    try {
      this.rewardVideo = this.context.$app.$def.ylh_sdk.createRewardVideoAd({
        placementId: this.adUnitId,
        muted: false,
      })
      const onLoadCb = () => {
        console.log('onLoad: 加载成功')
        this.ecpm = this.rewardVideo.getECPM() || -1
        trackEvent({
          category: 'advertise',
          action: 'fill',
          opt_label: 'ylh_reward_ad',
          opt_extra: {
            event_time: Date.now(),
            unit_id: this.adUnitId,
            ecpm: this.ecpm,
          },
        })
        this.loadResolve?.(true)
      }
      this.rewardVideo.onLoad(onLoadCb)
      this.rewardVideo.onError((err = {}) => {
        console.log('onError:', err)
        this.loadResolve?.(false)
        trackEvent({
          category: 'advertise',
          action: 'fail',
          opt_label: 'ylh_reward_ad',
          opt_extra: {
            event_time: Date.now(),
            unit_id: this.adUnitId,
          },
        })
      })
      this.rewardVideo.onExpose(() => {
        console.log('onExpose: 广告曝光')
        trackEvent({
          category: 'advertise',
          action: 'exposure',
          opt_label: 'ylh_reward_ad',
          opt_extra: {
            event_time: Date.now(),
            unit_id: this.adUnitId,
            ecpm: this.ecpm,
          },
        })
      })
      this.rewardVideo.onClick(() => {
        console.log('onClick: 广告点击')
        trackEvent({
          category: 'advertise',
          action: 'click',
          opt_label: 'ylh_reward_ad',
          opt_extra: {
            event_time: Date.now(),
            unit_id: this.adUnitId,
            ecpm: this.ecpm,
          },
        })
      })
      this.rewardVideo.onReward(() => {
        console.log('onReward: 激励发放')
      })
      this.rewardVideo.onVideoFinish(() => {
        console.log('onVideoFinish: 视频播放完成')
      })
      this.rewardVideo.onClose(() => {
        console.log('onClose:广告关闭')
      })
    } catch (error) {
      console.warn('创建激励视频异常:', error)
    }
  }

  load() {
    return new Promise((resolve, reject) => {
      if (!this.adUnitId) {
        resolve(false)
        return
      }
      trackEvent({
        category: 'advertise',
        action: 'request',
        opt_label: 'ylh_reward_ad',
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.adUnitId,
        },
      })
      this.loadResolve = resolve
      this.rewardVideo.load()
    })
  }
  show() {
    if (this.rewardVideo) {
      this.rewardVideo.show()
    }
  }

  isValid() {
    if (this.rewardVideo) {
      const isValid = this.rewardVideo.isValid()
      console.log('isValid 广告是否有效:', isValid)
      return isValid
    }
    return false
  }

  sendLossNotification(winEcpm) {}
  sendWinNotification(lossEcpm) {}

  destroy() {
    if (this.rewardVideo) {
      this.rewardVideo.destroy()
      this.rewardVideo = null
    }
  }
}

export default YlhRewardedAd
