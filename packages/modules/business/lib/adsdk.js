import ad from '@service.ad'
import config from './config'
import OppoAd from './ads/oppoAd'
import { trackEvent } from './index'
import VivoAd from './ads/vivoAd'
import YlhAd from './ads/ylhAd'
import BaiduAd from './ads/baiduAd'
import HonorAd from './ads/honorAd'
import XiaomiAd from './ads/xiaomiAd'
import YLHQuickAppSDK from 'ylh-quick-app-ad-sdk'
import UNION_AD_SDK from 'union-quick-app-ad/app'
import globalData from '@quickapp/utils/lib/globalData'
import { storage } from '@quickapp/utils'

const debug = process.env.NODE_ENV !== 'production'

const adChannel = new BroadcastChannel('ad')

function sortByEcpm(a, b) {
  return b.ecpm - a.ecpm
}

function createAd(context, { unit_id, type, ecpm_floor, network, app_id }) {
  if (!unit_id) return null
  if (network) {
    switch (network) {
      case 'ylh':
        return new YlhAd(context, app_id, unit_id, type)
      case 'baidu':
        return new BaiduAd(context, app_id, unit_id, type)
    }
  }
  let provider = ad.getProvider()?.toLowerCase()
  switch (provider) {
    case 'oppo':
      return new OppoAd(unit_id, type, ecpm_floor)
    case 'vivo':
      return new VivoAd(unit_id, type, ecpm_floor)
    case 'honor':
      return new HonorAd(unit_id, type, ecpm_floor)
    case 'xiaomi':
      return new XiaomiAd(unit_id, type, ecpm_floor)
    default:
      return new OppoAd(unit_id, type, ecpm_floor)
  }
}

function getAdComponent(adObj) {
  if (adObj instanceof BaiduAd) {
    return 'baidu-ad'
  } else if (adObj instanceof HonorAd) {
    return 'honor-ad2'
  } else if (adObj instanceof OppoAd) {
    return 'oppo-ad2'
  } else if (adObj instanceof VivoAd) {
    return 'vivo-ad2'
  } else if (adObj instanceof XiaomiAd) {
    return 'xiaomi-ad2'
  } else if (adObj instanceof YlhAd) {
    return 'ylh-ad-custom'
  }
  return 'oppo-ad2'
}

function getNativeAdGroup(context, num) {
  let nativeAll = config.getAdConfig()['native_all'] || []
  let waterfall_all = config.getAdConfig()['waterfall_all'] || []
  let ylh_all = config.getConfig().ad?.ylh?.['native_all'] || []
  let baidu_all = config.getConfig().ad?.baidu?.['native_all'] || []
  let ylh_app_id = config.getConfig().ad?.ylh?.app_id
  let baidu_app_id = config.getConfig().ad?.baidu?.app_id
  if (nativeAll.length <= 0) return []
  if (ylh_app_id && !context.$app.$def.ylh_sdk) {
    try {
      const config = {
        appId: ylh_app_id,
      }
      new YLHQuickAppSDK(context.$app, config)
    } catch (error) {
      console.log('ylh_sdk init error', error)
    }
  }
  nativeAll = nativeAll.sort(() => 0.5 - Math.random())
  waterfall_all = waterfall_all.sort(() => 0.5 - Math.random())
  ylh_all = ylh_all.sort(() => 0.5 - Math.random())
  baidu_all = baidu_all.sort(() => 0.5 - Math.random())
  const group1 = []
  for (let i = 0; i < nativeAll.length; i++) {
    group1.push([
      { unit_id: nativeAll[i], type: 'bidding' },
      waterfall_all[i],
      {
        unit_id: ylh_all[i],
        type: 'bidding',
        network: 'ylh',
        app_id: ylh_app_id,
      },
      {
        unit_id: baidu_all[i],
        type: 'bidding',
        network: 'baidu',
        app_id: baidu_app_id,
      },
    ])
  }
  // const group2 = nativeAll.slice(num, nativeAll.length)
  return group1
}

function getNativeAdRequest(context, items, index) {
  return {
    unitId: items[0]?.unit_id,
    adObj: null,
    component: null,
    success: false,
    _loading: false,
    _loadPromise: null,
    async load() {
      if (this._loading && this._loadPromise) {
        await this._loadPromise
        return this.adObj
      }

      // Set loading flag and create a new promise
      this._loading = true
      this._loadPromise = (async () => {
        let parallelNum = config.getAdConfig()['ad_parallel_num'] || 4
        let that = globalData.$adsdk
        if (this.adObj) {
          this._loading = false
          return this.adObj
        }
        try {
          let adCache = that.adCacheList.pop()
          if (!adCache && index < parallelNum) {
            adCache = await adBidding(context, items)
          }
          if (!adCache) {
            while (true) {
              adCache = that.adCacheList.pop()
              if (adCache) {
                break
              }
              await delay(50)
            }
          }
          this.adObj = adCache
          this.component = getAdComponent(this.adObj)
          this.success = this.adObj != null && this.adObj.adData != null
          this.unitId = this.adObj?.adUnitId
        } catch (e) {
          globalData.$adsdk.log(`ad load err ${e}`)
        } finally {
          this._loading = false
        }
        return this.adObj
      })()

      return this._loadPromise
    },
  }
}

async function adBidding(context, items) {
  if (!items) return null
  if (!(await globalData.$adsdk.checkAdLimit())) {
    adChannel.postMessage('onAdLimit')
    return null
  }
  let adList = await Promise.all(
    items.map(async item => {
      if (!item) return null
      let adObj = createAd(context, item)
      if (!adObj) return null
      await adObj.preloadAd()
      return adObj
    })
  ).then(res =>
    res.filter(adObj => adObj != null && adObj.adData != null).sort(sortByEcpm)
  )
  globalData.$adsdk.onAdRequest()
  if (adList.length <= 0) return null
  const winData = adList[0]
  const lossList = adList.slice(1)
  const native_ecpm_floor = config.getAdConfig()['native_ecpm_floor']
  for (const lossData of lossList) {
    if (lossData.type === 'bidding' && lossData.ecpm >= 0) {
      lossData.sendLossNotification(winData.ecpm)
    }
  }
  if (
    native_ecpm_floor &&
    winData.ecpm > 0 &&
    winData.ecpm < native_ecpm_floor
  ) {
    if (winData.type === 'bidding') {
      winData.sendLossNotification(native_ecpm_floor)
    }
    return null
  } else {
    if (winData.type === 'bidding' && winData.ecpm > 0) {
      winData.sendWinNotification(lossList[0]?.ecpm)
    }
  }
  return winData
}

function splashListener() {
  try {
    if (ad.getProvider()?.toLowerCase() === 'oppo') {
      ad.onSplashStatus({
        reserved: false,
        callback: ({ statusCode, data }) => {
          if (statusCode === '0') {
            trackEvent({
              category: 'advertise',
              action: 'exposure',
              opt_label: 'splash_ad',
              opt_extra: {
                event_time: Date.now(),
                unit_id: data?.adId,
                ecpm: data?.ecpm,
              },
            })
          } else if (statusCode === '100') {
            trackEvent({
              category: 'advertise',
              action: 'click',
              opt_label: 'splash_ad',
              opt_extra: {
                event_time: Date.now(),
                unit_id: data?.adId,
                ecpm: data?.ecpm,
              },
            })
          }
        },
      })
    }
  } catch (e) {
    globalData.$adsdk.log(`onSplashStatus err ${e}`)
  }
}

async function delay(timeout) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve()
    }, timeout)
  })
}

function today() {
  const date = new Date()
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}${month}${day}`
}

if (!globalData.$adsdk) {
  globalData.$adsdk = {
    logs: [],
    recommendAdList: [],
    adCacheList: [],
    additionRequestList: [],
    startAdditionAdFlag: false,
    adRequestCount: 0,
    adShowCount: 0,
    biddingCount: 0,
    async onCreate(context) {
      splashListener()
      try {
        new UNION_AD_SDK(context)
      } catch (error) {
        globalData.$adsdk.log(`error ${error}`)
      }
      this.adRequestCount = (await storage.get(`ad_request_${today()}`)) || 0
      this.adShowCount = (await storage.get(`ad_show_${today()}`)) || 0
      setTimeout(() => {
        this.startAdPreload(context)
      }, 1500)
    },
    startAdPreload(context) {
      if (!this.startAdditionAdFlag) {
        this.startAdditionAdFlag = true
        let groups = getNativeAdGroup(context)
        this.additionRequestList = [...groups]
        this.startLoadAdQueue(context)
      }
    },
    onAdRequest() {
      this.adRequestCount++
      const key = `ad_request_${today()}`
      storage.set(key, this.adRequestCount).then(() => {
        console.log(`ad_request ${key} ${this.adRequestCount}`)
      })
    },
    onAdShow() {
      setTimeout(() => {
        this.adShowCount++
        const key = `ad_show_${today()}`
        storage.set(key, this.adShowCount).then(() => {
          console.log(`ad_show_ ${key} ${this.adShowCount}`)
        })
      }, Math.trunc(100 * Math.random()))
    },
    async checkAdLimit() {
      const adRequestCount = (await storage.get(`ad_request_${today()}`)) || 0
      const adShowCount = (await storage.get(`ad_show_${today()}`)) || 0
      const native_request_max = config.getAdConfig()['native_request_max']
      const native_show_max = config.getAdConfig()['native_show_max']
      if (native_request_max && adRequestCount > native_request_max) {
        return false
      }
      if (native_show_max && adShowCount > native_show_max) {
        return false
      }
      return true
    },
    async startLoadAdQueue(context) {
      let maxNum = config.getAdConfig()['ad_cache_num'] || 4
      while (true) {
        if (!this.startAdditionAdFlag) break
        if (!(await this.checkAdLimit())) {
          adChannel.postMessage('onAdLimit')
          break
        }
        if (this.additionRequestList.length <= 0) {
          await delay(200)
          continue
        }
        if (!globalData.appVisible) {
          await delay(200)
          continue
        }
        if (this.adCacheList.length >= maxNum) {
          await delay(500)
          continue
        }
        let items = this.additionRequestList.pop()
        let adObj = await adBidding(context, items)
        if (adObj && adObj.adData != null) {
          this.adCacheList.push(adObj)
          this.adCacheList = this.adCacheList.sort((a, b) => a.ecpm - b.ecpm)
        } else {
          this.additionRequestList.unshift(items)
        }
        await delay(50)
      }
    },
    initRecommendAd(context, num = 4) {
      let groups = getNativeAdGroup(context)
      this.recommendAdList = groups
        .slice(0, num)
        .map((items, index) => getNativeAdRequest(context, items, index))
    },
    updateRecommend(context) {
      try {
        let num = config.getAdConfig()['ad_num'] || 4
        let groups = getNativeAdGroup(context)
        this.additionRequestList = [...groups]
        if (num > this.recommendAdList.length) {
          this.recommendAdList = this.recommendAdList.concat(
            groups
              .filter(
                items =>
                  !this.recommendAdList.some(
                    item => item.unitId === items[0]?.unit_id
                  )
              )
              .slice(0, num - this.recommendAdList.length)
              .map((items, index) =>
                getNativeAdRequest(
                  context,
                  items,
                  index + this.recommendAdList.length
                )
              )
          )
        } else {
          this.recommendAdList = this.recommendAdList.slice(0, num)
        }
      } catch (e) {
        this.log(e)
      }
    },
    log(message) {
      this.logs.unshift(message)
      console.log(message)
    },
  }
}

export default globalData.$adsdk
