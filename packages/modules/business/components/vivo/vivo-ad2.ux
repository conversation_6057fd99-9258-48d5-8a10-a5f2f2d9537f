<import name="icon" src="@quickapp/apex-ui/components/icon/index"></import>

<template>
  <div>
    <block if="showAd">
      <ad
        adid="{{adData.adId}}"
        class="ad-native"
        closeBtnPosition="right-top"
        @adshow="adShow"
        @adclick="adClick"
        @error="adError(err, evt)"
        type="native"
      >
        <div class="click-container">
          <ad-clickable-area type="click" class="container">
            <block
              if="{{adData.creativeType === 4 || adData.creativeType === 5}}"
            >
              <ad-clickable-area
                class="ad-video"
                type="video"
              ></ad-clickable-area>
            </block>
            <ad-clickable-area class="ad-logo" type="logo"></ad-clickable-area>
            <div class="debug" if="{{debugText}}">
              <text>{{ debugText }}</text>
            </div>
          </ad-clickable-area>
          <div
            class="ad-clickbtn-container"
            style="{{adbtnStyle}}"
            if="showAdBtn"
          >
            <div class="{{adbtnClz}}">
              <ad-clickable-area
                class="ad-clickbtn"
                type="button"
              ></ad-clickable-area>
              <div class="ad-clickbtn-text"></div>
              <div class="ad-clickbtn-1"></div>
              <div class="ad-clickbtn-2"></div>
            </div>
          </div>
        </div>
      </ad>
    </block>
  </div>
</template>

<script>
import { trackEvent } from '../../lib'
import adsdk from '../../lib/adsdk'
import config from '../../lib/config'
import globalData from '@quickapp/utils/lib/globalData'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data() {
    return {
      adObj: {},
      adData: {},
      showAd: false,
      showDebug: globalData.isDebug,
      debugText: '',
      showAdBtn: false,
      adbtnStyle: '',
      adbtnClz: 'ad-clickbtn-container',
    }
  },
  props: {
    adIndex: {
      type: Number,
      default: -1,
    },
    overlay: {
      type: String,
    },
  },
  onReady() {
    if (this.adIndex > -1) {
      this.adObj = adsdk.recommendAdList[this.adIndex]?.adObj
      this.adData = this.adObj?.adData
      if (this.adData) {
        if (!this.$valid || !this.$visible) {
          adsdk.adCacheList.push(this.adObj)
          return
        }
        this.$emit('load')
        this.showAdButton()
        this.showAd = true
        if (this.showDebug) {
          this.debugText = 'id: ' + this.adObj.adUnitId
        }
      }
    }
  },
  showAdButton() {
    let adBtnPercent = config.getAdConfig()['ad_btn_percent'] || 60
    if (Math.random() * 100 >= adBtnPercent) {
      return
    }
    this.showAdBtn =
      this.adData.clickBtnTxt &&
      (this.adData.clickBtnTxt.includes('下载') ||
        this.adData.clickBtnTxt.includes('安装'))
    // this.showAdBtn = true
    let left = Math.trunc(Math.random() * 600)
    if (this.adIndex === 0) {
      this.adbtnClz = 'ad-clickbtn-box'
      this.adbtnStyle = `left: 40px;top:40px;`
    } /* else if (this.adIndex === 1) {
      this.adbtnClz = 'ad-clickbtn-box2'
      this.adbtnStyle = `left: 620px;`
    } else if (this.adIndex === 2) {
      this.adbtnClz = 'ad-clickbtn-box5'
      this.adbtnStyle = `left: 620px;`
    } else if (this.adIndex === 3 || this.adIndex === 4 || this.adIndex === 5) {
      this.adbtnClz = 'ad-clickbtn-box3'
      this.adbtnStyle = `left: 175px;`
    } else if (this.adIndex === 7) {
      this.adbtnClz = 'ad-clickbtn-box4'
      this.adbtnStyle = `left: 0;bottom:88px;`
    } */ else {
      this.adbtnClz = 'ad-clickbtn-box4'
      this.adbtnStyle = `left: 0;`
    }
  },
  adShow() {
    this.$emit('show', { ecpm: this.adObj.ecpm })
    adsdk.log(
      `adShow 信息流广告展示成功：${this.adObj.adUnitId} ${this.adObj.ecpm}`
    )
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adObj.adUnitId
    }
    adsdk.onAdShow()
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  adError(err, evt) {
    // this.showAd = false
    this.$emit('error')
    adsdk.log(
      `adError 信息流广告加载出错：${this.adObj.adUnitId} ${evt.errCode} ${evt.errMsg}`
    )
    trackEvent({
      category: 'advertise',
      action: 'fail',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
      },
    })
  },
  adClick() {
    console.log('ad int vivo click')
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  close() {
    console.log('ad int vivo close')
    this.showAd = false
    this.$emit('close')
  },
}
</script>

<style lang="less">
@keyframes adout {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.4);
  }
}
@keyframes adinner {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.7);
  }
}
.ad-native {
  background-color: rgba(0, 0, 0, 0);
  animation-name: adout;
  animation-duration: 0ms;
  animation-delay: 0ms;
  transform-origin: 0 0;
}
.click-container {
  position: relative;
  animation-name: adinner;
  animation-duration: 0ms;
  animation-delay: 0ms;
  transform-origin: 0 0;
}
.container {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  flex-direction: column;
  position: relative;

  .ad-video {
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0);
    opacity: 0;
  }

  .ad-title {
    position: absolute;
    bottom: 64px;
    height: 56px;
    left: 32px;
    text {
      font-size: 28px;
      padding: 10px;
      color: #ffffff;
      font-weight: bold;
    }
  }

  .ad-logo {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    margin-top: -50px;
  }

  .debug {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 56px;
    align-items: center;
    justify-content: center;
    background-color: #2d40e9;
    text {
      font-size: 28px;
      color: white;
      font-weight: bold;
    }
  }
}
.ad-clickbtn-container {
  position: absolute;

  //back
  .ad-clickbtn-box {
    position: relative;
    width: 300px;
    height: 100px;

    .ad-clickbtn {
      width: 300px;
      height: 100px;
      background-color: #000000;
      border-radius: 50px;
      align-items: center;
      justify-content: center;
      font-size: 8px;
    }

    .ad-clickbtn-text {
      position: absolute;
      left: 110px;
      top: 41px;
      width: 80px;
      height: 18px;
      background-color: #000000;
      align-items: center;
      justify-content: center;
    }

    .ad-clickbtn-1 {
      position: absolute;
      left: 62px;
      top: 25px;
      width: 38px;
      height: 4px;
      transform: rotate(135deg);
      transform-origin: 0 0;
      background-color: #ffffff;
      align-items: center;
      justify-content: center;
    }

    .ad-clickbtn-2 {
      position: absolute;
      left: 37px;
      top: 50px;
      width: 38px;
      height: 4px;
      transform: rotate(45deg);
      transform-origin: 0 0;
      background-color: #ffffff;
      align-items: center;
      justify-content: center;
    }
  }
  //close black
  .ad-clickbtn-box2 {
    position: relative;
    width: 100px;
    height: 100px;

    .ad-clickbtn {
      width: 100px;
      height: 100px;
      background-color: #000000;
      border-radius: 50px;
      align-items: center;
      justify-content: center;
      font-size: 8px;
    }

    .ad-clickbtn-text {
      position: absolute;
      left: 10px;
      top: 41px;
      width: 80px;
      height: 18px;
      background-color: #000000;
      align-items: center;
      justify-content: center;
    }

    .ad-clickbtn-1 {
      position: absolute;
      left: 25px;
      top: 25px;
      width: 70px;
      height: 4px;
      transform: rotate(45deg);
      transform-origin: 0 0;
      background-color: #ffffff;
      align-items: center;
      justify-content: center;
    }

    .ad-clickbtn-2 {
      position: absolute;
      left: 75px;
      top: 25px;
      width: 70px;
      height: 4px;
      transform: rotate(135deg);
      transform-origin: 0 0;
      background-color: #ffffff;
      align-items: center;
      justify-content: center;
    }
  }
  // button
  .ad-clickbtn-box3 {
    position: relative;
    width: 400px;
    height: 100px;

    .ad-clickbtn {
      width: 400px;
      height: 100px;
      background-color: #ffffff;
      color: #000000;
      align-items: center;
      justify-content: center;
      font-size: 8px;
    }

    .ad-clickbtn-text {
    }

    .ad-clickbtn-1 {
    }

    .ad-clickbtn-2 {
    }
  }
  // black
  .ad-clickbtn-box4 {
    position: relative;
    width: 740px;
    height: 185px;

    .ad-clickbtn {
      width: 740px;
      height: 100px;
      background-color: #000000;
      align-items: center;
      justify-content: center;
      font-size: 8px;
    }

    .ad-clickbtn-text {
      position: absolute;
      left: 330px;
      top: 41px;
      width: 80px;
      height: 18px;
      background-color: #000000;
      align-items: center;
      justify-content: center;
    }

    .ad-clickbtn-1 {
    }

    .ad-clickbtn-2 {
    }
  }
  //close white
  .ad-clickbtn-box5 {
    position: relative;
    width: 100px;
    height: 100px;

    .ad-clickbtn {
      width: 100px;
      height: 100px;
      background-color: #ffffff;
      color: #000000;
      border-radius: 50px;
      align-items: center;
      justify-content: center;
      font-size: 8px;
    }

    .ad-clickbtn-text {
      position: absolute;
      left: 10px;
      top: 41px;
      width: 80px;
      height: 18px;
      background-color: #ffffff;
      align-items: center;
      justify-content: center;
    }

    .ad-clickbtn-1 {
      position: absolute;
      left: 25px;
      top: 25px;
      width: 70px;
      height: 4px;
      transform: rotate(45deg);
      transform-origin: 0 0;
      background-color: #000000;
      align-items: center;
      justify-content: center;
    }

    .ad-clickbtn-2 {
      position: absolute;
      left: 75px;
      top: 25px;
      width: 70px;
      height: 4px;
      transform: rotate(135deg);
      transform-origin: 0 0;
      background-color: #000000;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
