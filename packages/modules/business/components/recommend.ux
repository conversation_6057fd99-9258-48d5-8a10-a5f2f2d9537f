<script>
import adsdk from '../lib/adsdk'
import { showDialog } from '@system.prompt'
import config from '../lib/config'
import globalData from '@quickapp/utils/lib/globalData'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data: {
    adList: [],
    refreshTag: 0,
    bgStyle: '',
    showMarketDialog: false,
  },
  onInit() {
    this.$on('onPageBack', this.onPageBack)
    this.$on('onPageRefresh', this.showAd)
  },
  onReady() {
    this.showAd()
  },
  updateBackground() {
    if (globalData.recommendIndex >= 4 || !globalData.recommendIndex) {
      globalData.recommendIndex = 1
    } else {
      globalData.recommendIndex++
    }
    this.bgStyle = `background-image: url('http://fishing-h5.springdance.cn/images/cover/${globalData.recommendIndex}.webp?v=1');`
  },
  showAd() {
    this.updateBackground()
    let num = config.getAdConfig()['ad_num'] || 4
    adsdk.initRecommendAd(this, num)
    this.adList = []
    setTimeout(() => {
      this.filterAd()
    })
  },
  onConfigLoaded() {
    this.adList = []
    setTimeout(() => {
      this.filterAd()
    })
    let timeout = config.getAdConfig()['native_refresh']
    if (timeout) {
      this.refreshTag = setInterval(() => {
        if (!this.$visible) return
        this.showAd()
      }, timeout)
    }
  },
  filterAd() {
    let successSize = adsdk.recommendAdList.filter(item => item.success).length
    let lastSuccessIndex = adsdk.recommendAdList
      .map(item => item.success)
      .lastIndexOf(true)
    this.adList = adsdk.recommendAdList.map((item, index, arr) => {
      if (!item.success) {
        item.style = ''
      } else if (successSize > 1 && index === lastSuccessIndex) {
        item.style = `height: 560px;`
      } else {
        item.style = 'flex: 1;'
      }
      return item
    })
  },
  onAdClick() {
    this.showMarketDialog = false
    this.$emit('adclick')
  },
  onAdLoad() {
    this.filterAd()
    setTimeout(() => {
      const showMarketConfig = config.getAdConfig()['show_market_dialog']
      if (showMarketConfig) {
        this.showMarketDialog = true
      }
    }, 800)
  },
  onAdError() {
    this.filterAd()
  },
  onPageBack() {
    this.bgStyle = `background-image: url('http://fishing-h5.springdance.cn/images/cover/5.webp?v=1');`
    if (debug) {
      showDialog({
        message: adsdk.logs.join('\n'),
      })
    }
  },
}
</script>
<import
  name="ad-block"
  src="@quickapp/business/components/ad-block.ux"
></import>
<template>
  <div class="wrapper" style="{{bgStyle}}">
    <image
      class="market-dialog"
      src="http://fishing-h5.springdance.cn/images/market_dialog.webp"
      alt="blank"
      if="showMarketDialog"
    ></image>
    <div class="ad-root">
      <div class="item" for="{{item in adList}}" style="{{item.style}}">
        <ad-block
          id="ad{{$idx}}"
          ad-index="{{$idx}}"
          @adclick="onAdClick"
          @load="onAdLoad"
          @error="onAdError"
        ></ad-block>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@keyframes translateAnim {
  from {
    transform: translate(0, 435px);
  }
  to {
    transform: translate(0, 0);
  }
}
.wrapper {
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-size: cover;
  position: relative;

  .market-dialog {
    position: absolute;
    left: 35px;
    width: 680px;
    height: 352px;
    bottom: 84px;
    animation-name: translateAnim;
    animation-duration: 0.5s;
    animation-timing-function: ease;
    animation-fill-mode: forwards;
  }

  .ad-root {
    width: 100%;
    flex: 1;
    flex-direction: column;

    .item {
      width: 100%;
    }
  }
}
</style>
