<script>
import adsdk from '../../lib/adsdk'
import { trackEvent } from '../../lib'
import file from '@system.file'
import globalData from '@quickapp/utils/lib/globalData'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data() {
    return {
      refresh: '',
      adObj: {},
      adData: {},
      showAd: false,
      showDebug: debug,
      debugText: '',
    }
  },
  props: {
    adIndex: {
      type: Number,
      default: -1,
    },
  },
  async onReady() {
    file.readText({
      uri: 'internal://mass/debugtxt',
      success: data => {
        this.showDebug = true
      },
      fail: (data, code) => {},
    })

    await this.loadAd()
  },
  async loadAd() {
    if (this.adIndex > -1) {
      this.adObj = adsdk.recommendAdList[this.adIndex]?.adObj
    }
    this.adData = this.adObj?.adData
    // adsdk.log(`oppo-ad ${this.adIndex} ${this.adIndex2} ${!!this.adData}`)
    if (this.adData) {
      this.$emit('load')
      this.showAd = true
      setTimeout(() => {
        // this.$element(`baidu-ad-${this.adIndex}`)?.focus()
      }, 20)
      if (this.showDebug) {
        this.debugText = 'id: ' + this.adObj.adUnitId
      }
    }
  },
  onAdLoad(evt) {
    adsdk.log(`adShow 信息流广告加载成功：${JSON.stringify(evt)}`)
  },
  onAdShow(evt) {
    this.$emit('show')
    adsdk.log(`adShow 信息流广告展示成功：${JSON.stringify(evt)}`)
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adObj.adUnitId
    }
    adsdk.onAdShow()
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'baidu_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  onAdError(e) {
    this.$emit('error')
    adsdk.log(`adError 信息流广告加载出错：${this.adObj.adUnitId}`)
    trackEvent({
      category: 'advertise',
      action: 'fail',
      opt_label: 'baidu_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
      },
    })
  },
  onAdClick() {
    console.log('ad int baidu click')
    globalData.isNativeAdClick = true
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'baidu_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  onAdClosed() {
    console.log('ad int baidu close')
    this.$emit('close')
  },
  onDownloadClick() {},
  onDownloadClose() {},
  onDownloadShow() {},
  onDownloaded() {},
  onAdAppear(evt) {
    /*globalData.eventsProto = evt.__proto__.constructor
    setTimeout(() => {
      let container = this.$element('baidu-ad-' + this.adIndex)
      console.log(`baidu ad appear ${!!container}`)
      container.getBoundingClientRect({
        success: function (data) {
          const { top, bottom, left, right, width, height } = data
          let event = new globalData.eventsProto('click', {
            bubbles: true,
            cancelable: false,
          })
          event.clientX = left + Math.random() * width
          event.clientY = top + Math.random() * height
          container.dispatchEvent(event)
        },
        fail: (errorData, errorCode) => {},
        complete: function () {},
      })
    }, 2000)*/
  },
  onTouchStart(evt) {
    /*setTimeout(() => {
      let container = this.$element('baidu-ad-' + this.adIndex)
      console.log(`baidu touch ${!!container}`)
      if (container) {
        let event = new globalData.eventsProto('click', {
          bubbles: true,
          cancelable: false,
        })
        event.clientX = evt._changedTouches[0].clientX
        event.clientY = evt._changedTouches[0].clientY
        container.dispatchEvent(event)
      }
    }, 100)*/
  },
}
</script>
<import name="mobads-ad" src="union-quick-app-ad/components/mobadsAd"></import>
<template>
  <div>
    <div id="baidu-ad-{{adIndex}}" style="opacity: 0" if="showAd">
      <mobads-ad
        apid="{{adObj.adUnitId}}"
        appid="{{adObj.appId}}"
        type="feed"
        downloadpanel="{{false}}"
        videoautoplay="{{false}}"
        @ad-load="onAdLoad"
        @ad-show="onAdShow"
        @ad-click="onAdClick"
        @ad-touchstart="onAdClick"
        @ad-closed="onAdClosed"
        @ad-error="onAdError"
      ></mobads-ad>
    </div>
    <div class="debug" if="{{debugText}}">
      <text>{{ debugText }}</text>
    </div>
  </div>
</template>

<style scoped lang="less">
.debug {
  position: absolute;
  bottom: 0;
  right: 0;
  height: 56px;
  align-items: center;
  justify-content: center;
  background-color: #2d40e9;
  text {
    font-size: 28px;
    color: white;
    font-weight: bold;
  }
}
</style>
