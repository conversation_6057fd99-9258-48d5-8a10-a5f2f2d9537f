{"id": "10304", "package": "com.fortunetap.luck.app", "configCode": "cf_luckclick", "name": "幸运点击", "versionName": "1.1.37", "versionCode": 47, "minPlatformVersion": 1080, "privacyUrl": "https://xy.serveclouds.com/landing/10304/PrivacyPolicy.html", "userUrl": "https://xy.serveclouds.com/landing/10304/UserPolicy.html", "guidelinesForTortClaimsUrl": "https://app-h5.springtool.cn/rltq/agreement/guidelines-for-tort-claims.html", "questionUrl": "https://wj.qq.com/s2/9845028/fff1", "serviceUrl": "https://app-h5.springtool.cn/static/views/service/novel.html", "icon": "/assets/images/logo.webp", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.canvas"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.geolocation"}, {"name": "system.device"}, {"name": "system.audio"}, {"name": "system.image"}, {"name": "system.network"}, {"name": "system.webview"}, {"name": "system.request"}, {"name": "system.package"}, {"name": "system.storage"}, {"name": "service.account"}, {"name": "system.file"}, {"name": "service.ad"}, {"name": "system.nfc"}, {"name": "system.clipboard"}, {"name": "system.sensor"}], "permissions": [{"origin": "*"}], "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Home": {"component": "index"}, "pages/ToDo": {"component": "index"}, "pages/Camera": {"component": "index"}, "pages/MyDoc": {"component": "index"}, "pages/ImgPreview": {"component": "index"}, "pages/CertificatesList": {"component": "index"}, "pages/PreViewDoc": {"component": "index"}, "pages/Web": {"component": "index"}, "pages/Flash": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "pages/Service": {"component": "index"}, "weather/City": {"component": "index"}, "clean/cleaning": {"component": "index"}, "novel/bookstore-detail": {"component": "index"}, "novel/introduce": {"component": "index"}, "novel/detail": {"component": "index"}, "novel/reader": {"component": "index"}, "UnionAd/AdLanding": {"component": "index"}, "UnionAd/AdReward": {"component": "index"}}}, "display": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "menuBarData": {"menuBar": false}, "pages": {"pages/Home": {"titleBarText": "首页", "titleBar": false, "menuBarData": {"menuBar": false}, "statusBarImmersive": false, "statusBarBackgroundOpacity": 0}, "pages/ToDo": {"titleBarText": ""}, "pages/Web": {"titleBarText": ""}, "pages/Service": {"titleBarText": "在线客服"}, "pages/Flash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/ImgPreview": {"component": "index", "titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Camera": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/MyDoc": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "menuBarData": {"menuBar": false}, "fullScreen": false}, "pages/CertificatesList": {"titleBarText": "证件扫描", "titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141"}, "pages/MyDox": {"titleBarText": "我的文档", "titleBarBackgroundColor": "#EDF6FF", "titleBarTextColor": "#000000"}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}, "UnionAd/AdReward": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}, "UnionAd/AdLanding": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}}}}