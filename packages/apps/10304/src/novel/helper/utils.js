/**
 * 您可以将常用的方法、或系统 API，统一封装，暴露全局，以便各页面、组件调用，而无需 require / import.
 */
const prompt = require('@system.prompt')
const sysStorage = require('@system.storage')

const storage = {
  get(key) {
    return new Promise((resolve, reject) => {
      sysStorage.get({
        key: key,
        success: function (data) {
          console.log('本地....', key, data)
          if (data) {
            try {
              resolve(JSON.parse(data))
            } catch (e) {
              console.error('storage.get 报错了...', e)
              reject(e)
            }
          } else {
            resolve(null)
          }
        },
        fail: function (data, code) {
          console.log('错误....')
          reject({ data, code })
        },
      })
    })
  },
  set(key, value) {
    return new Promise((resolve, reject) => {
      sysStorage.set({
        key: key,
        value: JSON.stringify(value),
        success: function (data) {
          resolve(data)
        },
        fail: function (data, code) {
          console.error('storage.set 报错了...', e)
          reject({ data, code })
        },
      })
    })
  },
}

/**
 * 拼接 url 和参数
 */
function queryString(url, query) {
  let str = []
  for (let key in query) {
    str.push(key + '=' + query[key])
  }
  let paramStr = str.join('&')
  return paramStr ? `${url}?${paramStr}` : url
}

function showToast(message = '', duration = 0) {
  if (!message) return
  prompt.showToast({
    message: message,
    duration,
  })
}

export default {
  showToast,
  queryString,
  storage,
}
