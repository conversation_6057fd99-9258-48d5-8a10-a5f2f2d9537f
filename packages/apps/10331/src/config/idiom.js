export const answerList = [
  { id: 1, answer: '宽宏大度', success: '宏', finally: '大' },
  { id: 2, answer: '足智多谋', success: '智', finally: '多' },
  { id: 3, answer: '文质彬彬', success: '质', finally: '彬' },
  { id: 4, answer: '眉飞色舞', success: '飞', finally: '色' },
  { id: 5, answer: '冰清玉洁', success: '清', finally: '玉' },
  { id: 6, answer: '融会贯通', success: '会', finally: '贯' },
  { id: 7, answer: '风度翩翩', success: '度', finally: '翩' },
  { id: 8, answer: '喜笑颜开', success: '笑', finally: '颜' },
  { id: 9, answer: '持之以恒', success: '之', finally: '以' },
  { id: 10, answer: '学贯中西', success: '贯', finally: '中' },
  { id: 11, answer: '相貌堂堂', success: '貌', finally: '堂' },
  { id: 12, answer: '欣喜若狂', success: '喜', finally: '若' },
  { id: 13, answer: '锲而不舍', success: '而', finally: '不' },
  { id: 14, answer: '博古通今', success: '古', finally: '通' },
  { id: 15, answer: '落落大方', success: '落', finally: '大' },
  { id: 16, answer: '神采奕奕', success: '采', finally: '奕' },
  { id: 17, answer: '废寝忘食', success: '寝', finally: '忘' },
  { id: 18, answer: '才华横溢', success: '华', finally: '横' },
  { id: 19, answer: '斗志昂扬', success: '志', finally: '昂' },
  { id: 20, answer: '喜出望外', success: '出', finally: '望' },
  { id: 21, answer: '大义凛然', success: '义', finally: '凛' },
  { id: 22, answer: '出类拔萃', success: '类', finally: '拔' },
  { id: 23, answer: '意气风发', success: '气', finally: '风' },
  { id: 24, answer: '无动于衷', success: '动', finally: '于' },
  { id: 25, answer: '临危不俱', success: '危', finally: '不' },
  { id: 26, answer: '博大精深', success: '大', finally: '精' },
  { id: 27, answer: '威风凛凛', success: '风', finally: '凛' },
  { id: 28, answer: '垂头丧气', success: '头', finally: '丧' },
  { id: 29, answer: '鞠躬尽瘁', success: '躬', finally: '尽' },
  { id: 30, answer: '不屈不挠', success: '屈', finally: '不' },
  { id: 31, answer: '举一反三', success: '一', finally: '反' },
  { id: 32, answer: '神采奕奕', success: '采', finally: '奕' },
  { id: 33, answer: '勃然大怒', success: '然', finally: '大' },
  { id: 34, answer: '光明磊落', success: '明', finally: '磊' },
  { id: 35, answer: '集思广益', success: '思', finally: '广' },
  { id: 36, answer: '容光焕发', success: '光', finally: '焕' },
  { id: 37, answer: '呆若木鸡', success: '若', finally: '木' },
  { id: 38, answer: '死而后已', success: '而', finally: '后' },
]

function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[array[i], array[j]] = [array[j], array[i]]
  }
  return array
}

export const updatedAnswerList = shuffleArray(
  answerList.map(item => {
    const answerArray = item.answer.split('')

    const successIndex = answerArray.indexOf(item.success)
    if (successIndex !== -1) {
      answerArray.splice(successIndex, 1)
    }

    const swap = Math.random() < 0.5
    if (swap) {
      ;[item.success, item.finally] = [item.finally, item.success]
    }

    return { ...item, answer: answerArray }
  })
)
export const answerArr = answerList.map(item => {
  const answerArray = item.answer.split('')
  return { ...item, answer: answerArray }
})
