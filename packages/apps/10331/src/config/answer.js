export const answerList = [
  {
    id: 1,
    answer: '2, 4, 8, 16, ?',
    optionA: '18',
    optionB: '24',
    optionC: '30',
    optionD: '32',
    success: '32',
  },
  {
    id: 2,
    answer: '以下哪个不是水果？',
    optionA: '苹果',
    optionB: '香蕉',
    optionC: '胡萝卜',
    optionD: '橘子',
    success: '胡萝卜',
  },
  {
    id: 3,
    answer: '正方形旋转180°后，与原图相比：',
    optionA: '方向相反',
    optionB: '完全重合',
    optionC: '变成圆形',
    optionD: '颜色改变',
    success: '完全重合',
  },
  {
    id: 4,
    answer: '3个人3天喝3桶水，9个人9天喝几桶水？',
    optionA: '9',
    optionB: '27',
    optionC: '18',
    optionD: '81',
    success: '27',
  },
  {
    id: 5,
    answer: '小明爸爸有3个儿子，老大叫一郎，老二叫二郎，老三叫什么？',
    optionA: '小明',
    optionB: '三郎',
    optionC: '四郎',
    optionD: '小华',
    success: '小明',
  },
  {
    id: 6,
    answer: '太阳系中最大的行星是？',
    optionA: '木星',
    optionB: '土星',
    optionC: '地球',
    optionD: '火星',
    success: '木星',
  },
  {
    id: 7,
    answer: '从上午8:30到下午1:15，经过多长时间？',
    optionA: '4小时15分',
    optionB: '4小时45分',
    optionC: '5小时',
    optionD: '5小时30分',
    success: '4小时45分',
  },
  {
    id: 8,
    answer: '如果所有鸟都会飞，企鹅是鸟，那么企鹅会飞吗？',
    optionA: '会',
    optionB: '不会',
    optionC: '不确定',
    optionD: '有时会',
    success: '不会',
  },
  {
    id: 9,
    answer: '什么东西越洗越脏？',
    optionA: '衣服',
    optionB: '水',
    optionC: '手',
    optionD: '碗',
    success: '水',
  },
  {
    id: 10,
    answer: 'A, E, I, M, ?',
    optionA: 'Q',
    optionB: 'R',
    optionC: 'S',
    optionD: 'T',
    success: 'Q',
  },
  {
    id: 11,
    answer: '一个数加上它的一半等于18，这个数是多少？',
    optionA: '6',
    optionB: '9',
    optionC: '12',
    optionD: '15',
    success: '12',
  },
  {
    id: 12,
    answer: '以下哪种食物富含维生素C？',
    optionA: '鸡蛋',
    optionB: '米饭',
    optionC: '橙子',
    optionD: '牛肉',
    success: '橙子',
  },
  {
    id: 13,
    answer: '△ ○ □ △ ○ □ △ ?',
    optionA: '△',
    optionB: '○',
    optionC: '□',
    optionD: '☆',
    success: '○',
  },
  {
    id: 14,
    answer: '《蒙娜丽莎》的作者是？',
    optionA: '梵高',
    optionB: '达芬奇',
    optionC: '毕加索',
    optionD: '莫奈',
    success: '达芬奇',
  },
  {
    id: 15,
    answer: '如果昨天是周五的前天，那么明天是周几？',
    optionA: '周一',
    optionB: '周二',
    optionC: '周三',
    optionD: '周四',
    success: '周四',
  },
  {
    id: 16,
    answer: '“画蛇添足”比喻：',
    optionA: '勤奋努力',
    optionB: '节约资源',
    optionC: '多此一举',
    optionD: '坚持不懈',
    success: '多此一举',
  },
  {
    id: 17,
    answer: '世界上最长的河流是？',
    optionA: '亚马逊河',
    optionB: '尼罗河',
    optionC: '长江',
    optionD: '密西西比河',
    success: '尼罗河',
  },
  {
    id: 18,
    answer: '1+2×3=?',
    optionA: '6',
    optionB: '7',
    optionC: '7',
    optionD: '9',
    success: '7',
  },
  {
    id: 19,
    answer: '植物的光合作用需要什么气体？',
    optionA: '氧气',
    optionB: '二氧化碳',
    optionC: '氮气',
    optionD: '氢气',
    success: '二氧化碳',
  },
  {
    id: 20,
    answer: '所有猫都怕水，汤姆是猫，那么汤姆：',
    optionA: '怕水',
    optionB: '不怕水',
    optionC: '可能怕水',
    optionD: '不确定',
    success: '怕水',
  },
  {
    id: 21,
    answer: '“书山有路勤为径”下一句是？',
    optionA: '学海无涯乐作舟',
    optionB: '学海无涯苦作舟',
    optionC: '人生何处不相逢',
    optionD: '柳暗花明又一村',
    success: '学海无涯苦作舟',
  },
  {
    id: 22,
    answer: '光在真空中传播的速度是？',
    optionA: '30万米/秒',
    optionB: '30万千米/秒',
    optionC: '3万千米/秒',
    optionD: '10万千米/秒',
    success: '30万千米/秒',
  },
  {
    id: 23,
    answer: '甲比乙高，乙比丙矮，谁最矮？',
    optionA: '甲',
    optionB: '乙',
    optionC: '丙',
    optionD: '无法确定',
    success: '乙',
  },
  {
    id: 24,
    answer: '煮鸡蛋时加盐是为了？',
    optionA: '增加营养',
    optionB: '让蛋壳变硬',
    optionC: '防止蛋壳破裂',
    optionD: '加快煮熟',
    success: '防止蛋壳破裂',
  },
  {
    id: 25,
    answer: '一个圆有几条对称轴？',
    optionA: '1',
    optionB: '2',
    optionC: '无数条',
    optionD: '0',
    success: '无数条',
  },
  {
    id: 26,
    answer: '月球围绕什么天体公转？',
    optionA: '地球',
    optionB: '太阳',
    optionC: '火星',
    optionD: '银河系中心',
    success: '地球',
  },
  {
    id: 27,
    answer: '“这句话是假的”属于：',
    optionA: '科学结论',
    optionB: '数学定理',
    optionC: '悖论',
    optionD: '诗歌',
    success: '悖论',
  },
  {
    id: 28,
    answer: '水的化学式是？',
    optionA: 'CO₂',
    optionB: 'H₂O',
    optionC: 'O₂',
    optionD: 'NaCl',
    success: 'H₂O',
  },
  {
    id: 29,
    answer: '“守株待兔”讽刺哪种行为？',
    optionA: '勇敢冒险',
    optionB: '不劳而获',
    optionC: '勤奋工作',
    optionD: '团结合作',
    success: '不劳而获',
  },
  {
    id: 30,
    answer: '用3个5和1个1，如何得到24？（允许加减乘除）',
    optionA: '5×5-5+1',
    optionB: '(5-1)×5+5',
    optionC: '5×(5-1/5)',
    optionD: '5×5-1×5',
    success: '5×(5-1/5)',
  },
]

export const shuffleArray = array => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[array[i], array[j]] = [array[j], array[i]]
  }
  return array
}

export const updatedAnswerList = shuffleArray(answerList)
