<template>
  <div class="container">
    <div class="card" style="{{cardStyle}}" @click="handleFlip">
      <div class="card-face front" style="{{frontStyle}}">
        <image
          src="/assets/images/flip_after.png"
          style="width: 100%; height: 100%"
        ></image>
        <div class="question-container">
          <text class="card-text">{{ name }}</text>
        </div>
      </div>

      <div class="card-face back" style="{{backStyle}}">
        <image
          src="/assets/images/flip_after.png"
          style="width: 100%; height: 100%"
        ></image>
        <div class="text-container">
          <text class="card-text">{{ answer }}</text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    name: {
      type: String,
    },
    answer: {
      type: String,
    },
  },
  data: {
    cardStyle: '',
    frontStyle: '',
    backStyle: '',
  },

  handleFlip() {
    this.isFlipped = !this.isFlipped
    // this.cardStyle = `transform: rotateY(${
    //   this.isFlipped ? '180deg' : '0deg'
    // });`

    this.cardStyle = `transform: rotateY(180deg);`

    if (this.isFlipped) {
      this.frontStyle = 'transform: rotateY(180deg); opacity: 0;'
      this.backStyle = 'transform: rotateY(0deg); opacity: 1;'
    }
    // else {
    //   this.frontStyle = 'transform: rotateY(0deg); opacity: 1;'
    //   this.backStyle = 'transform: rotateY(-180deg); opacity: 0;'
    // }
  },
}
</script>

<style>
.container {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 255px;
  margin-bottom: 20px;
}

.card {
  width: 100%;
  height: 100%;
  position: relative;
  perspective: 1000px;
  transform-style: preserve-3d;
  transition: transform 1s;
  will-change: transform;
}

.card-face {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  backface-visibility: hidden;
  transition: transform 1s, opacity 1s;
  will-change: transform, opacity;
}

.front {
  transform: rotateY(0deg);
  opacity: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back {
  transform: rotateY(-180deg);
  opacity: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.question-container {
  position: absolute;
  text-align: center;
}
.text-container {
  position: absolute;
  /* transform: translate(-50%, -50%); */
  transform: rotateY(-180deg);
  text-align: center;
}

.card-text {
  width: 200px;
  text-overflow: ellipsis;
  lines: 2;
  font-size: 32px;
  color: #333;
  text-align: center;
}
</style>
