<import name="pay-callback-dialog" src="./PayCallbackDialog.ux"></import>
<import name="add-desk-dialog" src="./AddDeskDialog.ux"></import>

<template>
  <div class="wrapper">
    <div class="pay-btn" @click="paySoon">
      <div class="pay-btn__top">
        <text class="pay-btn__top__unit">¥</text>
        <text class="pay-btn__top__num">{{ vipPrice }}</text>
        <text class="pay-btn__top__r-price">原价 ¥{{ vipOriginalPrice }}</text>
        <text class="pay-btn__top__btn">立即支付</text>
      </div>
    </div>
    <div class="agree-box" @click="agreement">
      <div
        class="agree-box__tick {{isAgree ? 'agree-box__tick--active' : ''}}"
      ></div>
      <text class="agree-box__tip" @click="toRenewAgreement">
        同意《付费协议》
      </text>
    </div>
    <pay-callback-dialog
      if="visibleDialog"
      @pay-ready="handlePayReady"
      @repay="handleRePay"
    ></pay-callback-dialog>
    <add-desk-dialog
      if="addDeskDialogShow"
      @add-success="hideAddDeskDialog"
      @add-fail="hideAddDeskDialog"
    ></add-desk-dialog>
  </div>
</template>

<script>
import { showToast } from '@quickapp/utils'
import {
  alipay,
  getLocalRechargePro,
  setLocalRechargePro,
} from '@/helper/utils'
import router from '@system.router'

export default {
  props: {
    // 当前选中的vip id
    vipId: {
      type: [Number, String],
    },
    // 用户的vip状态
    userVipType: {
      type: [Number, String],
    },
    // 价格
    vipPrice: {
      type: Number,
    },
    // 原价
    vipOriginalPrice: {
      type: Number,
    },
  },

  data() {
    return {
      isAgree: false,
      visibleDialog: false,
      addDeskDialogShow: false,
    }
  },

  onInit() {
    getLocalRechargePro().then(res => {
      this.isAgree = !!res
    })
  },

  hideAddDeskDialog() {
    this.addDeskDialogShow = false
    this.$emit('hideAddDesk')
  },

  showAddDeskDialog() {
    this.addDeskDialogShow = true
  },

  handlePayReady() {
    this.visibleDialog = false
    this.$emit('payReady')
    this.showAddDeskDialog()
  },

  handleRePay() {
    this.visibleDialog = false
    this.paySoon()
  },

  paySoon() {
    if (!this.isAgree) {
      return showToast('请先同意付费协议')
    }
    alipay(this.vipId)
      .then(res => {
        this.visibleDialog = true
        this.$emit('payComplete')
        console.log('支付回调', JSON.stringify(res))
      })
      .catch(e => {
        showToast(e.message)
      })
  },

  agreement() {
    const isAgree = !this.isAgree
    this.setAgree(isAgree)
    setLocalRechargePro(isAgree)
  },

  setAgree(value) {
    this.isAgree = value
  },

  toRenewAgreement(evt) {
    router.push({
      uri: 'pages/Web',
      params: {
        webSrc: __MANIFEST__.paymentAgreement,
      },
    })
    evt.stopPropagation()
  },
}
</script>

<style lang="less">
.wrapper {
  width: 694px;
  align-items: center;
  flex-direction: column;
}

.pay-method {
  width: 100%;
  border-radius: 24px;
  padding: 40px 30px;
  flex-direction: column;
  background-color: #ffffff;
  border: 1px solid #eee;

  &__title {
    font-size: 32px;
    font-weight: bolder;
    color: #333333;
    line-height: 32px;
  }

  &__list {
    flex-direction: column;

    &__item {
      margin-top: 40px;
      align-items: center;

      &__icon {
        width: 48px;
        height: 48px;
      }

      &__label {
        margin-left: 16px;
        font-size: 28px;
        font-weight: bolder;
        color: #333333;
        line-height: 40px;
      }

      &__dot {
        width: 24px;
        height: 24px;
        border-radius: 24px;
        margin-left: auto;
        background-color: RGBA(218, 234, 255, 1);

        &--active {
          background-color: RGBA(66, 123, 243, 1);
        }
      }
    }
  }
}

.pay-btn {
  margin-top: 44px;

  &__top {
    width: 694px;
    height: 96px;
    background-color: #4e8cff;
    border-radius: 52px;

    &__unit {
      font-size: 32px;
      font-weight: bold;
      color: #ffffff;
      line-height: 64px;
      margin-left: 40px;
      top: 10px;
    }

    &__num {
      font-size: 64px;
      font-weight: bold;
      color: #ffffff;
      margin-left: 8px;
      line-height: 64px;
    }

    &__r-price {
      font-size: 24px;
      color: white;
      margin-left: 8px;
      top: 14px;
      text-decoration: line-through;
    }

    &__btn {
      text-align: center;
      width: 248px;
      margin-left: auto;
      background-color: rgba(255, 142, 95, 1);
      border-top-right-radius: 52px;
      border-bottom-right-radius: 52px;
      font-size: 32px;
      font-weight: bolder;
      color: #ffffff;
    }
  }
}

.agree-box {
  justify-content: center;
  align-items: center;
  width: 614px;
  height: 64px;
  background-color: #26457d;
  border-bottom-right-radius: 48px;
  border-bottom-left-radius: 48px;

  &__tick {
    width: 28px;
    height: 28px;
    opacity: 0.8;
    border: 2px solid #ffffff;
    border-radius: 28px;
    margin-right: 16px;

    &--active {
      background-color: rgba(255, 142, 95, 1);
    }
  }

  &__tip {
    font-size: 24px;
    color: #ffffff;
  }
}
</style>
