<import
  name="nativead"
  src="@quickapp/business/components/nativead.ux"
></import>

<template>
  <div class="wrapper">
    <div class="header">
      <div class="header__info">
        <image class="header__info__avatar" src="{{userInfo.avatar}}"></image>
        <text class="header__info__nick-name">{{ userInfo.nickName }}</text>
      </div>
    </div>
    <div class="tab">
      <div class="tab__header">
        <text class="tab__header__title">其他服务</text>
      </div>
      <div class="tab__list">
        <div class="tab__list__item" @click="toServiceNoAdPage">
          <image
            class="tab__list__item__icon"
            src="/assets/images/mine/ic_mine_service.webp"
          ></image>
          <text class="tab__list__item__label">联系客服</text>
        </div>
        <div class="tab__list__item" @click="toCustomerService">
          <image
            class="tab__list__item__icon"
            src="/assets/images/mine/ic_mine_message.webp"
          ></image>
          <text class="tab__list__item__label">意见反馈</text>
        </div>
        <div class="tab__list__item" @click="toUserPage">
          <image
            class="tab__list__item__icon"
            src="/assets/images/mine/ic_mine_user.webp"
          ></image>
          <text class="tab__list__item__label">用户协议</text>
        </div>
        <div class="tab__list__item" @click="toPrivacyPage">
          <image
            class="tab__list__item__icon"
            src="/assets/images/mine/ic_mine_privacy.webp"
          ></image>
          <text class="tab__list__item__label">隐私政策</text>
        </div>
        <div class="tab__list__item" @click="toAbout">
          <image
            class="tab__list__item__icon"
            src="/assets/images/mine/ic_mine_about.webp"
          ></image>
          <text class="tab__list__item__label">当前版本</text>
        </div>
      </div>
    </div>
    <div class="ad-box">
      <nativead honor-id="1910605075819003904"></nativead>
    </div>
  </div>
</template>

<script>
import {
  toGuidelinesForTortClaims,
  toPrivacyPage,
  toQuestionUrl,
  toUserPage,
} from '@quickapp/utils'

import { getUserInfo } from '@quickapp/utils/lib/api/ocr/user'
import router from '@system.router'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  data() {
    return {
      userInfo: {
        avatar: 'http://cdn-inputmethod.muchuntech.com/avatar/0500.png',
        nickName: '2894755',
        status: 1,
        userId: 5,
        vipExpireTime: '',
        vipType: 1,
      },
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      // ....
      this.getUserInfo()
    }
  },
  handleLifeCycleShow() {
    this.getUserInfo()
  },

  getUserInfo() {
    return getUserInfo().then(res => {
      this.userInfo = res
    })
  },

  toPrivacyPage() {
    toPrivacyPage()
  },

  toUserPage() {
    toUserPage()
  },

  toGuide() {
    toGuidelinesForTortClaims()
  },

  toCustomerService() {
    // toQuestionUrl()
  },

  toAbout() {
    router.push({
      uri: 'pages/About',
    })
  },

  toVipPage() {
    router.push({
      uri: 'pages/Recharge',
    })
  },

  toServiceNoAdPage() {
    router.push({
      uri: 'pages/ServiceNoAd',
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding: 200px 30px 0;
  background: linear-gradient(180deg, #e8f2ff 0%, #ffffff 100%);
}

.header {
  width: 100%;
  background-color: white;
  flex-direction: column;
  border-radius: 24px;

  &__info {
    padding: 50px 40px;

    &__avatar {
      width: 160px;
      height: 160px;
      border-radius: 80px;
    }

    &__nick-name {
      font-size: 36px;
      font-weight: bolder;
      color: #333333;
      margin-left: 28px;
    }
  }

  &__footer {
    width: 100%;
    height: 200px;

    &__bg {
      width: 100%;
      height: 100%;
    }

    &__info {
      width: 100%;
      padding: 0 30px;
      align-items: center;
      justify-content: space-between;

      &__left {
        flex-direction: column;

        &__title {
          font-size: 48px;
          color: #8a5f2c;
          font-weight: bolder;
          line-height: 48px;
          letter-spacing: 1px;
        }

        &__desc {
          font-size: 24px;
          color: #8a5f2c;
          margin-top: 14px;
        }

        &__expire {
          font-size: 24px;
          color: #8a5f2c;
          margin-top: 10px;
          font-weight: bolder;
        }
      }

      &__btn {
        width: 164px;
        height: 68px;
        background: linear-gradient(102deg, #f8895e 0%, #ee3131 100%);
        border-radius: 34px;
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
      }
    }
  }
}

.tab {
  width: 100%;
  padding: 40px;
  margin-top: 32px;
  background-color: #ffffff;
  border-radius: 24px;
  flex-direction: column;

  &__header {
    &__title {
      font-size: 32px;
      font-weight: bold;
      color: #333333;
    }
  }

  &__list {
    flex-wrap: wrap;
    margin-top: 30px;
    margin-bottom: 74px;

    &__item {
      width: 33.33%;
      height: 144px;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      &__icon {
        height: 52px;
      }

      &__label {
        font-size: 24px;
        font-weight: bold;
        color: #555555;
        margin-top: 14px;
      }
    }
  }
}

.logo {
  width: 200px;
  height: 200px;
}

.title {
  font-size: 40px;
  font-weight: bold;
  color: #292938;
  line-height: 40px;
  margin-top: 24px;
}

.list {
  flex-direction: column;
  width: 100%;
  margin-top: 180px;

  .list-item {
    justify-content: space-between;
    height: 100px;
    align-items: center;
    //border-bottom: 1px solid #eee;
  }

  .text {
    font-size: 32px;
    color: #000000;
  }

  .icon {
    width: 40px;
    height: 40px;
  }

  .ad-box {
    width: 100%;
    flex-direction: column;
  }
}
</style>
