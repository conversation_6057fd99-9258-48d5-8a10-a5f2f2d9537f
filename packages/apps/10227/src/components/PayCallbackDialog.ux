<template>
  <div class="pay-dialog">
    <div class="pay-dialog__inner">
      <text class="pay-dialog__inner__title">您是否已成功支付</text>
      <div class="pay-dialog__inner__footer">
        <text
          @click="handlePayReady"
          class="pay-dialog__inner__footer__btn pay-dialog__inner__footer__btn--already"
        >
          已付费
        </text>
        <text
          @click="handleRePay"
          class="pay-dialog__inner__footer__btn pay-dialog__inner__footer__btn--retry"
        >
          重新支付
        </text>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  handlePayReady() {
    this.$emit('payReady')
  },

  handleRePay() {
    this.$emit('repay')
  },
}
</script>

<style lang="less">
.pay-dialog {
  align-items: center;
  justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);

  &__inner {
    flex-direction: column;
    width: 540px;
    background-color: white;
    border-radius: 24px;

    &__title {
      color: #000;
      text-align: center;
      font-weight: bolder;
      font-size: 36px;
      margin-top: 40px;
    }

    &__footer {
      margin-top: 60px;
      margin-bottom: 30px;
      justify-content: space-between;
      padding: 0 40px;

      &__btn {
        width: 210px;
        height: 68px;
        text-align: center;
        border-radius: 8px;
        font-size: 28px;

        &--already {
          border: 1px solid #333;
        }

        &--retry {
          color: white;
          background-color: #4d82e7;
        }
      }
    }
  }
}
</style>
