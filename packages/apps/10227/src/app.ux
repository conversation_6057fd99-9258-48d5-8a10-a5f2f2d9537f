<script>
import { mixinsApp } from '@quickapp/business'

import globalData from '@quickapp/utils/lib/globalData'

globalData.ad = {
  oppo: {
    native_all: [
      '2152553',
      '2152578',
      '2152592',
      '2152613',
      '2152662',
      '2152686',
      '2207771',
      '2207780',
    ],
  },
  honor: {
    native_all: [
      '1910595381898903552',
      '1910604936703901696',
      '1910605075819003904',
      '1910605237589508096',
      '1910605354405855232',
      '1910605458043174912',
      '1910605576216641536',
      '1910605681191682048',
    ],
  },
}

export default mixinsApp({
  onCreate() {},
})
</script>
