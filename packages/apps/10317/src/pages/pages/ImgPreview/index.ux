<import name="apex-spin" src="@quickapp/apex-ui/components/spin/index"></import>

<template>
  <div class="wrapper">
    <div class="header">
      <div @click="back" class="header__back-btn">
        <image src="/assets/images/ic_left.webp"></image>
      </div>
      <text class="header__title">文件扫描</text>
    </div>
    <image if="{{imgUrl}}" class="img" src="{{imgUrl}}"></image>
    <div class="operator">
      <div class="operator__item" @click="editImage">
        <image
          class="operator__item__icon"
          src="/assets/images/ic_ rotate.webp"
        ></image>
        <text class="operator__item__label">旋转</text>
      </div>
      <div class="operator__item" @click="editImage">
        <image
          class="operator__item__icon"
          src="/assets/images/ic_trimming.webp"
        ></image>
        <text class="operator__item__label">裁切</text>
      </div>
      <div class="operator__item" @click="back">
        <image
          class="operator__item__icon"
          src="/assets/images/ic_delete.webp"
        ></image>
        <text class="operator__item__label">删除</text>
      </div>
    </div>
    <div class="footer">
      <text class="footer__btn" @click="knowText">提取文字</text>
      <text class="footer__btn" @click="saveFile">保存文件</text>
    </div>
    <div if="{{loading}}" class="loading">
      <apex-spin tip="{{loadingTip}}"></apex-spin>
    </div>
  </div>
</template>

<script>
import request from '@system.request'
import { getExtension, showToast } from '@quickapp/utils'
import image from '@system.image'
import media from '@system.media'
import adsdk from '@quickapp/business/lib/adsdk'
import router from '@system.router'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'
import { uploadOss } from '@quickapp/utils/lib/oss'
import { ocrIdentify } from '@quickapp/utils/lib/api/ocr/ocr'
import dayjs from 'dayjs'
import { filePath } from '@/config/files'
import file from '@system.file'
import { base64toUnit8Array } from '@/helper/utils'
import { getUserInfo } from '@quickapp/utils/lib/api/ocr/user'
import downloadtask from '@system.downloadtask'

const DEBUG = process.env.NODE_ENV === 'development'

export default setPageMenuConfig({
  private: {
    degree: 0,
    imgUrl: '',
    cacheUri: null,
    loading: false,
    loadingTip: '识别中...',
    userInfo: {},
  },

  protected: {
    uri: '/assets/images/logo.png',
    certificatesType: null,
  },

  computed: {
    certificatesTypeObj() {
      try {
        return JSON.parse(this.certificatesType)
      } catch (e) {
        return {}
      }
    },
  },

  onInit() {
    this.$page.setTitleBar({
      textColor: '#000000',
      backgroundColor: '#EDF6FF',
    })
    this.imgUrl = this.uri

    // this.download().then(data => {
    //   this.cacheUri = data.uri
    // })
  },

  back() {
    router.back()
  },

  editImage() {
    image.editImage({
      uri: this.imgUrl,
      success: data => {
        this.imgUrl = data.uri
      },
    })
  },

  moveToDownloadDir(srcUri) {
    return new Promise((resolve, reject) => {
      const fileName = this.genFileName()
      const extension = getExtension(srcUri)

      file.move({
        srcUri: srcUri,
        dstUri: filePath + fileName + extension,
        success: function (uri) {
          console.log(`moveToDownloadDir success: ${uri}`)
          resolve(uri)
        },
        fail: function (data, code) {
          console.log(`moveToDownloadDir fail, code = ${code}`)
          reject({ data, code })
        },
      })
    })
  },

  // 写入文件
  writeText(text, extension = '.txt') {
    // const fileName = dayjs().format('YYYY年MM月DD日HH点mm分ss秒')
    const fileName = this.genFileName()
    const uri = filePath + fileName + extension

    return new Promise((resolve, reject) => {
      file.writeText({
        uri: uri,
        text: text,
        success: () => resolve({ uri }),
        fail: (data, code) => reject({ data, code }),
      })
    })
  },

  download(url = this.imgUrl) {
    return new Promise((resolve, reject) => {
      const filename = this.genFileName() + '.pdf'

      request.download({
        url, // 只能下载http(s)的
        filename,
        success: data => {
          request.onDownloadComplete({
            token: data.token,
            success: function (data) {
              console.log('download --->', JSON.stringify(data))
              resolve(data)
            },
            fail: function (data, code) {
              reject('下载失败，网络错误')
            },
          })
        },
        fail: (data, code) => {
          reject('下载失败，网络错误')
        },
      })
    })
  },

  downloadImg() {
    if (this.cacheUri) {
      this.saveToPhotosAlbum(this.cacheUri).then(showToast).catch(showToast)
    } else {
      showToast('正在下载，请稍后...')
      this.download()
        .then(({ uri }) => uri)
        .then(this.saveToPhotosAlbum)
        .then(({ message }) => showToast(message))
        .catch(showToast)
    }
  },

  saveToPhotosAlbum(uri) {
    return new Promise((resolve, reject) => {
      media.saveToPhotosAlbum({
        uri: uri,
        success: () => resolve({ uri, message: '保存成功' }),
        fail: () => reject('保存失败,您可能未开权限'),
      })
    })
  },

  handleRotate() {
    this.cacheUri
      ? this.applyOperations(this.cacheUri)
      : this.download()
          .then(({ uri }) => uri)
          .then(this.applyOperations)
  },

  applyOperations(uri) {
    image.applyOperations({
      uri: uri,
      operations: [
        {
          action: 'rotate',
          degree: (this.degree += 90),
        },
      ],
      success: data => {
        this.imgUrl = data.uri
      },
      fail: function (data, code) {
        console.log(`handleRotate handling fail, code = ${code}`)
      },
    })
  },

  async saveFile() {
    const isVip = this.isVip()

    if (isVip) {
      // showToast('正在识别，请稍后...')
      this.loading = true
      this.loadingTip = '正在保存...'
      let res = null
      try {
        res = await this.knowTextByOcr(
          this.certificatesTypeObj.category !== 'table'
        )
        if (!res) {
          console.log('文件上传oss失败', JSON.stringify(res))
          showToast('保存失败')
          this.loading = false
          return
        }
      } catch (e) {
        showToast('识别失败')
        this.loading = false
        return
      }
      if (this.certificatesTypeObj.category === 'table') {
        try {
          await this.writeArrayBuffer(base64toUnit8Array(res[0]), '.xls')
          showToast('保存成功')
          this.toDocPage()
        } catch (e) {
          console.log('writeArrayBuffer error', JSON.stringify(e))
          showToast(e.message || '保存失败')
        }
      } else {
        try {
          // await this.downloadTask(res[0])
          await this.download(res[0])
          showToast('保存成功')
          this.toDocPage()
        } catch (e) {
          showToast(e.message || '保存失败')
        }
      }
      this.loading = false
    } else {
      showToast('您还不是vip')
      this.toRecharge()
    }
  },

  toDocPage() {
    if (this.certificatesTypeObj.category === 'certificates') {
      router.push({
        uri: 'pages/CertificatesList',
        params: {
          filterType: 'certificates',
          // readPath: 'internal://mass/download',
        },
      })
    } else {
      router.push({
        uri: 'pages/CertificatesList',
        params: {
          filterType: 'not_certificates',
        },
      })
    }
  },

  downloadTask(url, saveFilePath = filePath) {
    return new Promise((resolve, reject) => {
      const filename = this.genFileName()
      console.log('downloadTask file name', filename)

      downloadtask.downloadFile({
        url,
        filePath: saveFilePath + filename + '.pdf',
        success: function (res) {
          console.log('downloadTask success', JSON.stringify(res))
          resolve(res)
        },
        fail: function (data, code) {
          console.log('downloadTask', JSON.stringify(data), code)
          reject({ data, code })
        },
      })
    })
  },

  isVip() {
    return true //getUserInfo().then(({ vipType }) => vipType !== 0)
  },

  savePic() {
    showToast('正在保存，请稍后...')
    return this.saveToPhotosAlbum(this.uri)
      .then(() => showToast('保存成功'))
      .catch(e => {
        console.log('保存失败', e)
        showToast('保存失败')
      })
  },

  async knowText() {
    const isVip = this.isVip()

    console.log(
      'this.certificatesTypeObj',
      JSON.stringify(this.certificatesTypeObj)
    )

    if (isVip) {
      if (
        this.certificatesTypeObj &&
        this.certificatesTypeObj.category === 'table'
      ) {
        showToast('该文件为表格，请点击保存文件')
        return
      }
      // showToast('正在识别，请稍后...')
      this.loading = true
      this.loadingTip = '识别中...'
      let result
      try {
        result = await this.knowTextByOcr()

        if (!result) {
          this.loading = false
          return showToast('识别错误，未识别到文字')
        }
      } catch (e) {
        showToast(DEBUG ? 'knowTextByOcr 错误' : '识别失败')
        this.loading = false
        return
      }

      try {
        const { uri } = await this.writeText(result.join('\n'))
        router.push({
          uri: 'pages/PreViewDoc',
          params: {
            fileUri: uri,
          },
        })
      } catch (e) {
        showToast(DEBUG ? '文件写入失败' : '识别失败')
      }
      this.loading = false
    } else {
      showToast('您还不是vip')
      this.toRecharge()
    }
  },

  toRecharge() {
    router.push({
      uri: 'pages/Recharge',
    })
  },

  genFileName() {
    return (
      this.certificatesTypeObj.id +
      '_' +
      this.certificatesTypeObj.category +
      '_' +
      dayjs()
    )
  },

  writeArrayBuffer(buf, extension = '.xls') {
    const fileName = this.genFileName()
    const uri = filePath + fileName + extension

    return new Promise((resolve, reject) => {
      file.writeArrayBuffer({
        uri: uri,
        buffer: buf,
        success: function () {
          resolve({ uri })
        },
        fail: function (data, code) {
          console.log('array buffer 写入失败.....')
          reject({ data, code })
        },
      })
    })
  },

  async knowTextByOcr(isToPdf = false) {
    return (
      // 压缩图片
      this.compressImage(this.imgUrl)
        // 上传到oss
        .then(({ uri }) => uploadOss(uri))
        // 处理参数
        .then(res => this.handleOcrParams(res.data, isToPdf))
        // ocr识别
        .then(ocrIdentify)
    )
  },

  handleOcrParams(url, isToPdf = false) {
    let certificatesType = {}
    try {
      certificatesType = JSON.parse(this.certificatesType)
    } catch (e) {}
    return {
      type: isToPdf ? 9 : certificatesType.type, // 图片转pdf默认是9
      parma: certificatesType.params,
      url,
    }
  },

  // 压缩图片
  compressImage(uri) {
    return new Promise((resolve, reject) => {
      image.compressImage({
        uri,
        quality: 30,
        // ratio: 2, // 变为原图的1/2大小
        success: data => resolve(data),
        fail: err => {
          reject('网络错误')
        },
      })
    })
  },
})
</script>

<style lang="less">
@import './../../assets/styles/style.less';

.wrapper {
  flex-direction: column;
  align-items: center;
  height: 100%;
  background-color: #f0f2f5;
}

.loading {
  justify-content: center;
  align-items: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
}

.header {
  height: 88px;
  margin-top: 88px;
  align-items: center;
  justify-content: center;
  width: 100%;

  &__title {
    font-size: 36px;
    font-weight: bolder;
    color: #333333;
    line-height: 36px;
  }

  &__back-btn {
    position: absolute;
    left: 40px;
    top: 16px;
    width: 56px;
    height: 56px;
    background-color: #ffffff;
    border-radius: 20px;
    align-items: center;
    justify-content: center;

    image {
      width: 30px;
      height: 30px;
    }
  }
}

.img {
  width: 100%;
  flex: 1;
  object-fit: contain;
}

.operator {
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 34px 84px;
  background-color: white;

  &__item {
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &__icon {
      width: 40px;
      height: 40px;
    }

    &__label {
      margin-top: 20px;
      font-size: 24px;
      color: #333333;
    }
  }
}

.footer {
  width: 100%;
  height: 120px;
  border-top: 1px solid #eee;
  background-color: white;

  &__btn {
    flex: 1;
    text-align: center;
    color: #4e8cff;
    font-size: 36px;
    font-weight: bolder;
    border-right: 1px solid #eee;
  }
}
</style>
