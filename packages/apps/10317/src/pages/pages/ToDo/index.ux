<import name="option-input" src="./components/options-input.ux"></import>

<template>
  <div class="wrapper">
    <div class="header" @click="back">
      <image src="/assets/images/arrow_left.webp"></image>
    </div>
    <div class="title">
      <text>问题</text>
      <div class="question">
        <input type="text" model:value="{{title}}" />
      </div>
    </div>
    <div class="options">
      <text>选项</text>
      <option-input
        for="(index, option) in optionsList"
        tid="{{index}}"
        option="{{option.name}}"
        index="{{index}}"
        @remove="handleRemove"
        @update-option="handleUpdate"
      ></option-input>
      <input
        type="button"
        value="添加选项"
        class="btn"
        @click="addOption"
        disabled="{{optionsList.length >= 5}}"
      />
      <input type="button" value="确定" class="btn" @click="saveOptions" />
    </div>
    <div class="tips">
      <input type="button" value="最少2个选项" class="tip" />
      <input type="button" value="最多5个选项" class="tip" />
    </div>
  </div>
</template>

<script>
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'
import router from '@system.router'
import { showToast, storage } from '@quickapp/utils'

export default {
  // private: {
  //   title: '',
  //   optionsList: [],
  // },
  data: {
    title: '',
    optionsList: [],
  },

  onInit() {
    this.getLocalData()
  },

  back() {
    router.back()
  },

  // 添加选项
  addOption() {
    if (this.optionsList.length < 5) {
      this.optionsList = [...this.optionsList, { name: '' }]
    }
  },

  //删除选项
  handleRemove(evt) {
    console.log(evt.detail.index);
    const index = evt.detail.index
    if (this.optionsList.length > 2) {
      this.optionsList.splice(index, 1);
    }
  },

  //更新选项
  handleUpdate(evt) {
    const { index, value } = evt.detail
    this.optionsList = this.optionsList.map((item, i) =>
      i === index ? { ...item, value } : item
    )
  },

  async getLocalData() {
    await storage.get('options').then(res => {
      this.optionsList = res || [
        { name: '牛肉板面' },
        { name: '黄焖鸡' }
      ]
    })

    await storage.get('title').then(res => {
      this.title = res||'今晚吃什么'
    })
  },

  // 保存到本地
  async saveOptions() {
    const validOptions = this.optionsList
      .map(item => ({ name: item.value.trim() }))
      .filter(item => item.name)
    if (validOptions.length >= 2) {
      await storage.set("options", validOptions)
      await storage.set("title", this.title.trim())
      console.log(this.title);
      showToast("保存成功")
      router.back()
    } else {
      showToast("至少需要2个有效选项")
    }
  }
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 40px;
  .header {
    width: 58px;
    height: 48px;
    margin-bottom: 20px;
  }
  .title {
    display: flex;
    flex-direction: column;
    margin-bottom: 40px;
    text {
      color: #424242;
      font-size: 38px;
      margin-bottom: 20px;
    }
    .question {
      padding: 10px;
      border: 1px solid #979797;
      width: 100%;
      border-radius: 20px;
      font-size: 38px;
      color: #424242;
      input {
        width: 100%;
      }
    }
  }
  .options {
    display: flex;
    flex-direction: column;
    text {
      color: #424242;
      font-size: 38px;
      margin-bottom: 20px;
    }
    .btn {
      width: 100%;
      border: 1px solid #979797;
      border-radius: 20px;
      background-color: #e9e9e9;
      padding: 10px;
      color: #424242;
      margin-top: 20px;
    }
  }
  .tips {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    .tip {
      width: 464px;
      padding: 10px;
      background-color: #1c1c1c;
      color: #fff;
      border-radius: 20px;
      margin-top: 20px;
    }
  }
}
</style>