<template>
  <div class="root">
    <div class="title"><text>智力答题</text></div>
    <div class="bg">
      <div class="answer" for="item in topicList">
        <div class="header">
          <div class="order">
            <text>第{{ order }}题</text>
          </div>
        </div>
        <div class="content">
          <text>{{ item.answer }}</text>
        </div>
        <div class="choose">
          <div
            class="option"
            style="background-color:{{bgcolor1}}"
            @click="choose(item.optionA, 'A', item.id)"
          >
            <text>A.</text>
            <text>{{ item.optionA }}</text>
            <image src="{{imgUrl1}}" class="icon"></image>
          </div>
          <div
            class="option"
            style="background-color:{{bgcolor2}}"
            @click="choose(item.optionB, 'B', item.id)"
          >
            <text>B.</text>
            <text>{{ item.optionB }}</text>
            <image src="{{imgUrl2}}" class="icon"></image>
          </div>
          <div
            class="option"
            style="background-color:{{bgcolor3}}"
            @click="choose(item.optionC, 'C', item.id)"
          >
            <text>C.</text>
            <text>{{ item.optionC }}</text>
            <image src="{{imgUrl3}}" class="icon"></image>
          </div>
          <div
            class="option"
            style="background-color:{{bgcolor4}}"
            @click="choose(item.optionD, 'D', item.id)"
          >
            <text>D.</text>
            <text>{{ item.optionD }}</text>
            <image src="{{imgUrl4}}" class="icon"></image>
          </div>
        </div>
        <div class="btn" @click="nextTopic" disabled="{{disabled}}">
          <text>{{ btnText }}</text>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { shuffleArray, updatedAnswerList } from '@/config/answer'
import adsdk from '@quickapp/business/lib/adsdk'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },
  data() {
    return {
      answerData: [], // 题库
      topicList: [], // 答题数据
      btnText: '下一题',
      order: 1,
      bgcolor1: '',
      bgcolor2: '',
      bgcolor3: '',
      bgcolor4: '',
      imgUrl1: '',
      imgUrl2: '',
      imgUrl3: '',
      imgUrl4: '',
      disabled: true,
      status: true,
      correctAnswer: '',
    }
  },

  onInit() {
    this.getList()
    this.getTopicList()
  },
  onDestroy() {},

  getList() {
    this.answerData = updatedAnswerList
  },

  getTopicList() {
    this.topicList = []
    this.topicList = [this.answerData[this.order - 1]]
    this.correctAnswer = this.topicList[0].success
    this.disabled = true
  },

  choose(selectedOption, position, id) {
    const isCorrect = selectedOption === this.correctAnswer
    if (this.status) {
      this.status = false
    } else {
      return
    }
    if (isCorrect) {
      this.setOptionStyle(position, true)
    } else {
      this.setOptionStyle(position, false)
      this.showCorrectAnswer()
    }
    this.disabled = false
  },

  setOptionStyle(position, isCorrect) {
    const successColor = '#65C93B'
    const errorColor = '#FB636D'
    const successIcon = '/assets/images/ic_success.png'
    const errorIcon = '/assets/images/ic_error.png'

    if (isCorrect) {
      if (position === 'A') {
        this.bgcolor1 = successColor
        this.imgUrl1 = successIcon
      } else if (position === 'B') {
        this.bgcolor2 = successColor
        this.imgUrl2 = successIcon
      } else if (position === 'C') {
        this.bgcolor3 = successColor
        this.imgUrl3 = successIcon
      } else if (position === 'D') {
        this.bgcolor4 = successColor
        this.imgUrl4 = successIcon
      }
    } else {
      if (position === 'A') {
        this.bgcolor1 = errorColor
        this.imgUrl1 = errorIcon
      } else if (position === 'B') {
        this.bgcolor2 = errorColor
        this.imgUrl2 = errorIcon
      } else if (position === 'C') {
        this.bgcolor3 = errorColor
        this.imgUrl3 = errorIcon
      } else if (position === 'D') {
        this.bgcolor4 = errorColor
        this.imgUrl4 = errorIcon
      }
    }
  },

  showCorrectAnswer() {
    if (this.topicList[0].optionA === this.correctAnswer) {
      this.bgcolor1 = '#65C93B'
      this.imgUrl1 = '/assets/images/ic_success.png'
    } else if (this.topicList[0].optionB === this.correctAnswer) {
      this.bgcolor2 = '#65C93B'
      this.imgUrl2 = '/assets/images/ic_success.png'
    } else if (this.topicList[0].optionC === this.correctAnswer) {
      this.bgcolor3 = '#65C93B'
      this.imgUrl3 = '/assets/images/ic_success.png'
    } else if (this.topicList[0].optionD === this.correctAnswer) {
      this.bgcolor4 = '#65C93B'
      this.imgUrl4 = '/assets/images/ic_success.png'
    }
  },

  nextTopic() {
    if (this.order >= this.answerData.length) {
      this.btnText = '答完了'
      return
    }
    this.bgcolor1 = ''
    this.bgcolor2 = ''
    this.bgcolor3 = ''
    this.bgcolor4 = ''
    this.imgUrl1 = ''
    this.imgUrl2 = ''
    this.imgUrl3 = ''
    this.imgUrl4 = ''
    this.status = true
    this.order++
    this.getTopicList()
  },
}
</script>
<style lang="less">
.root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  .title {
    margin-bottom: 40px;
    text {
      color: #ffffff;
      font-size: 160px;
    }
  }
  .bg {
    width: 670px;
    height: 932px;
    padding: 40px;
    background-image: url('/assets/images/topic_bg.png');
    background-size: contain;
    background-repeat: no-repeat;

    .answer {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // justify-content: center;
      align-items: center;
      .header {
        width: 100%;
        height: 60px;
        display: flex;
        justify-content: flex-start;
        .order {
          display: flex;
          background-color: #452cff;
          border-radius: 12px;
          padding: 0 24px;

          text {
            color: #ffffff;
            font-size: 32px;
          }
        }
        .successNum {
          padding: 0 24px;
          display: flex;
          background-color: #fb636d;
          border-radius: 12px;
          border: 2px solid #333333;
          text {
            color: #ffffff;
            font-size: 32px;
          }
        }
      }

      .content {
        width: 100%;
        /* height: 90px; */
        display: flex;
        justify-content: center;
        /* margin-top: 128px; */
        text {
          width: 100%;
          color: #000000;
          font-size: 46px;
          font-weight: 800;
          text-align: center;
        }
        .kong {
          border: 4px solid #65c93b;
          border-radius: 10px;
        }
      }
      .line {
        width: 100%;
        height: 3px;
        background-color: #b9b9b9;
        margin-top: 100px;
      }
      .choose {
        width: 100%;
        display: flex;
        justify-content: space-around;
        /* height: 100px; */
        flex-direction: column;
        padding: 0 40px;
        .option {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20px 40px;
          border-radius: 16px;
          margin-top: 20px;

          text {
            color: #000000;
            font-size: 32px;
          }
          .icon {
            width: 40px;
            height: 40px;
            margin-left: 20px;
          }
        }
      }
      .btn {
        width: 340px;
        height: 72px;
        background-color: #fbc430;
        border-radius: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px;
        // border: 2px solid #333333;
        text {
          color: #000000;
          font-size: 32px;
        }
      }
    }
  }
}
</style>
