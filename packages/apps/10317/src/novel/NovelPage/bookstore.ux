<import name="bookstore-list" src="./bookstore-list.ux"></import>

<template>
  <div class="wrapper">
    <list if="{{hotList && hotList.length}}" class="header-list">
      <list-item
        type="item"
        class="header-list-item"
        for="item in hotList"
        tid="$idx"
        @click="toRead(item)"
      >
        <stack class="stack">
          <div class="item_info">
            <div class="info">
              <text class="text">{{ item.name }}</text>
              <text class="author">{{ item.author }}</text>
              <text class="desc">
                {{ item.introduction }}
              </text>
            </div>
          </div>
          <image class="img" src="{{item.cover}}"></image>
        </stack>

        <!--<div class="header-list-item__bg"></div>-->
      </list-item>
    </list>
    <div class="list-box">
      <bookstore-list
        @see-more="handleSeeMoreEvt"
        category-list="{{categoryList}}"
      ></bookstore-list>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookstoreData } from '@/novel/api/bookstore'

export default {
  data: {
    hotList: [],
    categoryList: [],
  },

  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    showSeeMore: {
      type: Boolean,
      default: true,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  handleChangeIndex() {
    console.log(`novel ${this.index === this.currentIndex}`)
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    }
  },

  handleLifeCycleShow() {
    this.handleChangeIndex()
  },

  handleSeeMoreEvt({ detail }) {
    router.push({
      uri: 'bookstore-detail',
      params: {
        title: detail.category,
        id: detail.id,
      },
    })
  },

  toRead(item) {
    router.push({
      uri: 'novel/introduce',
      params: {
        id: item.id,
      },
    })
  },

  getData() {
    return getBookstoreData().then(res => {
      if (res) {
        if (res.hotRecommend) {
          this.hotList = res.hotRecommend
        }
        if (res.categoryList) {
          this.categoryList = res.categoryList
        }
      }
    })
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 24px;
  padding-top: 80px;
  /* background: linear-gradient(180deg, #F25C62 0%, #ffffff 100%); */
  /* background-color: #fffaf7; */
}

.header-list {
  flex-direction: row;
  height: 344px;
  //height: 320px + 74px;
  padding: 24px 10px 0;
  /* background: linear-gradient(180deg, #F25C62 0%, #ffffff 100%); */
  /* background-color: #000; */
}

.header-list-item {
  margin: 0 16px;
  flex-direction: column;
  align-items: center;
  width: 640px;
  /* height: 400px; */
  border-radius: 24px;
  overflow: visible;
  .stack {
    position: relative;
  }
  .img {
    width: 160px;
    height: 248px;
    position: absolute;
    left: 56px;
    top: 10px;
    border-radius: 16px;
  }
  .item_info {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    height: 328px;
    padding: 10px;
    align-items: flex-end;
    overflow: visible;

    .info {
      display: flex;
      background: linear-gradient(180deg, #d83e94 0%, #facec0 100%);
      flex: 1;
      flex-direction: column;
      padding-bottom: 10px;
      margin-left: 20px;
      height: 248px;
      padding-left: 200px;
      padding-right: 10px;
      border-radius: 16px;

      .text {
        text-align: left;
        font-size: 32px;
        /* text-align: center; */
        font-weight: bold;
        color: #fff;
        line-height: 36px;
        lines: 1;
        margin-top: 16px;
        text-overflow: ellipsis;
      }

      .author {
        margin-top: 16px;
        margin-bottom: 16px;
        /* text-align: center; */
        font-size: 24px;
        color: #fefefe;
        opacity: 0.4;
      }
      .desc {
        lines: 2;
        color: #f4f4f4;
        font-size: 24px;
        text-overflow: ellipsis;
      }
    }
  }
}

.list-box {
  /* background-color: #33b4a9; */
  /* margin-top: 64px; */
  padding: 0 28px;
  /* border-top-left-radius: 48px; */
  /* border-top-right-radius: 48px; */
}
</style>
