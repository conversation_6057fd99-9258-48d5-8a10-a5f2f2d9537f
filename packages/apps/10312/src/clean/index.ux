<template>
  <div class="wrapper">
    <image src="assets/clean-circle-1.webp"></image>
    <div class="text-div">
      <div class="text1">
        <text class="big">{{ num }}</text>
        <text class="small">GB</text>
      </div>
      <text class="text2">垃圾待清理</text>
    </div>
    <div class="clean-button" @click="onButtonClick">
      <text>一键优化</text>
    </div>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data: {
    num: 0,
  },

  onInit() {
    this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  onReady() {},

  onDestroy() {},

  handleStorageChange() {
    this.getInitData()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      this.getInitData()
    }
  },
  async getInitData() {
    this.num = (Math.random() * 5).toFixed(2)
  },
  onButtonClick() {
    router.push({
      uri: 'clean/cleaning',
      params: {
        num: this.num,
      },
    })
  },
}
</script>

<style lang="less">
@import '../assets/styles/style';
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;

  image {
    width: 560px;
    height: 560px;
  }

  .text-div {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .text1 {
      .big {
        font-size: 80px;
        color: #333;
        font-weight: bold;
        margin-top: 20px;
      }

      .small {
        font-size: 20px;
        color: #333;
        font-weight: normal;
      }
    }

    .text2 {
      font-size: 30px;
      color: #333;
      font-weight: normal;
      margin-top: 20px;
    }
  }

  .clean-button {
    width: 560px;
    height: 88px;
    background-color: #3d90ff;
    border-radius: 40px;
    justify-content: center;
    align-items: center;
    margin-top: 100px;

    text {
      font-size: 36px;
      color: white;
      font-weight: bold;
    }

    :active {
      border: 4px solid #fff;
    }
  }
}
</style>
