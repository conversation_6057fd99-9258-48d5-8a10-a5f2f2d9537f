<template>
  <div class="wrapper" style="{{wrapperStyle}}">
    <div class="inner" style="{{innerStyle}}"></div>
  </div>
</template>

<script>
export default {
  props: {
    width: {
      type: Number,
      default: 100  // 修改默认宽度为100
    },
    height: {
      type: Number,
      default: 10    // 修改默认高度为10
    },
    backgroundColor: {
      type: String,
      default: '#ccc'
    },
    activeColor: {
      type: String,
      default: '#F75540'
    },
    containerMin: {
      type: Number,
      default: 0
    },
    containerMax: {
      type: Number,
      default: 100
    },
    currentMin: {
      type: Number,
      default: 30
    },
    currentMax: {
      type: Number,
      default: 100
    }
  },
  computed: {
    wrapperStyle() {
      return {
        width: this.width + 'px',
        height: this.height + 'px',
        backgroundColor: this.backgroundColor,
        borderRadius: this.height / 2 + 'px'  
      }
    },
    innerStyle() {
      const containerCapacity = this.containerMax - this.containerMin
      const left = (
        (this.containerMax - this.currentMax) / containerCapacity
      ) * this.width + 'px'
      const right = (
        (this.currentMin - this.containerMin) / containerCapacity
      ) * this.width + 'px'

      return {
        backgroundColor: this.activeColor,
        borderRadius: this.height / 2 + 'px',
        left: left,
        right: right,
        top: '0'
      }
    }
  },
}
</script>

<style>
.wrapper {
  position: relative;  /* 添加相对定位 */
}

.inner {
  position: absolute;
  height: 100%;     
  top: 0;          
}
</style>