<!-- 
  温度计
 -->
<template>
  <div class="wrapper" style="{{wrapperStyle}}">
    <div class="inner" style="{{innerStyle}}"></div>
  </div>
</template>

<script>

export default {
  props: {
    width: {
      type: Number,
      default: 10
    },
    height: {
      type: Number,
      default: 100
    },
    backgroundColor: {
      type: String,
      default: '#ccc'
    },
    activeColor: {
      type: String,
      default: '#F75540'
    },
    // 容器最小温度
    containerMin: {
      type: Number,
      default: 0
    },
    // 容器最大温度
    containerMax: {
      type: Number,
      default: 100
    },
    // 当前最小温度
    currentMin: {
      type: Number,
      default: 30
    },
    // 当前最大温度
    currentMax: {
      type: Number,
      default: 100
    }
  },
  computed: {
    wrapperStyle() {
      return {
        width: this.width + 'px',
        height: this.height + 'px',
        backgroundColor: this.backgroundColor,
        borderRadius: this.width / 2 + 'px'
      }
    },
    innerStyle() {
      // 容器的最大容量
      const containerCapacity = this.containerMax - this.containerMin
      const top = (
        1 - ((this.currentMax - this.containerMin) / containerCapacity)
      ) * this.height + 'px'
      const bottom = (this.currentMin - this.containerMin) / containerCapacity * this.height + 'px'

      return {
        backgroundColor: this.activeColor,
        borderRadius: this.width / 2 + 'px',
        top: top,
        bottom: bottom,
      }
    }
  },
}
</script>

<style>
.wrapper {
  flex-direction: column;
}

.inner {
  position: absolute;
  width: 100%;
}
</style>
