<template>
  <div class="wrapper">
    <image class="logo" src="/assets/images/logo.png"></image>
    <text class="title">{{ appName }}</text>
    <div class="list">
      <div class="list-item" @click="toPrivacyPage">
        <text class="text">隐私协议</text>
        <image class="icon" src="/assets/images/ic_arrow_right.webp"></image>
      </div>
      <div class="list-item" @click="toUserPage">
        <text class="text">用户协议</text>
        <image class="icon" src="/assets/images/ic_arrow_right.webp"></image>
      </div>
      <div class="list-item" @click="toCustomerService">
        <text class="text">联系客服</text>
        <image class="icon" src="/assets/images/ic_arrow_right.webp"></image>
      </div>
    </div>
  </div>
</template>

<script>
import {
  toGuidelinesForTortClaims,
  toPrivacyPage,
  toQuestionUrl,
  toUserPage,
} from '@quickapp/utils'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    console.log('__MANIFEST__', JSON.stringify(__MANIFEST__))
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
    }
  },

  toPrivacyPage() {
    toPrivacyPage()
  },

  toUserPage() {
    toUserPage()
  },

  toGuide() {
    toGuidelinesForTortClaims()
  },

  toCustomerService() {
    // toQuestionUrl()
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding: 200px 30px 0;
}

.logo {
  width: 200px;
  height: 200px;
}

.title {
  font-size: 40px;
  font-weight: bold;
  color: #292938;
  line-height: 40px;
  margin-top: 24px;
}

.list {
  flex-direction: column;
  width: 100%;
  margin-top: 180px;

  .list-item {
    justify-content: space-between;
    height: 100px;
    align-items: center;
    //border-bottom: 1px solid #eee;
  }

  .text {
    font-size: 32px;
    color: #000000;
  }

  .icon {
    width: 40px;
    height: 40px;
  }
}
</style>
