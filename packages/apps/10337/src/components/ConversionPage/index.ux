<template>
  <div class="unit-converter">
    <text class="title">单位转换器</text>
    <div class="controls">
      <input
        type="number"
        value="{{inputValue}}"
        placeholder="输入数值"
        @change="inputChange"
      />
      <select @change="selectChange">
        <option
          for="(index,item) in units"
          tid="{{index}}"
          value="{{item.unit}}"
        >
          {{ item.label }} ({{ item.unit }})
        </option>
      </select>
    </div>
    <div class="results">
      <text class="title">转换结果：</text>
      <div for="(name, item) in filteredUnits" class="inner">
        <text>{{ item.label }} ({{ item.unit }})</text>
        <text>: {{ formatNumber(convert(item.unit)) }}</text>
      </div>
    </div>
  </div>
</template>

<script>
import { showToast, storage } from '@quickapp/utils'
import router from '@system.router'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },
  data: {
    inputValue: 100,
    inputUnit: 'cm',
    decimalPlaces: 4,
    units: [
      { label: '微米', ratio: 1e-6, unit: "um" },
      { label: '毫米', ratio: 1e-3, unit: "mm" },
      { label: '厘米', ratio: 1e-2, unit: "cm" },
      { label: '分米', ratio: 1e-1, unit: "dm" },
      { label: '米', ratio: 1, unit: "m" },
      { label: '千米', ratio: 1e3, unit: "km" }
    ]
  },

  // onInit() {
  //   this.$watch('currentIndex', 'handleChangeIndex')
  // },

  // handleChangeIndex() {
  //   if (this.index === this.currentIndex) {
  //     // this.getInitData()
  //   }
  // },
  computed: {
    filteredUnits() {
      return this.units.filter(item => item.unit !== this.inputUnit);
    }
  },

  inputChange(e) {
    this.inputValue = e.value
  },
  selectChange(e) {
    this.inputUnit = e.newValue
  },
  convert(targetUnit) {
    if (isNaN(this.inputValue)) return NaN;
    const inputItem = this.units.find(item => item.unit === this.inputUnit);
    const targetItem = this.units.find(item => item.unit === targetUnit);

    if (!inputItem || !targetItem) return NaN;

    return (this.inputValue * inputItem.ratio) / targetItem.ratio;
  },
  formatNumber(value) {
    if (isNaN(value)) return "无效输入";
    if (Math.abs(value) < 1e-6 || Math.abs(value) > 1e6) {
      return value.toExponential(4);
    }
    return value.toFixed(this.decimalPlaces);
  }
}
</script>

<style lang="less">
.unit-converter {
  /* background-color: #e3e456; */
  display: flex;
  flex-direction: column;
  padding: 120px 40px;
  width: 100%;
  .title{
    font-size: 58px;
    color: #00ebbd;
    margin-bottom: 20px;
  }
}
.controls {
  width: 100%;
  display: flex;
  background-color: #fff;
  border-radius: 15px;
  border: 1px solid #333;
  padding: 10px 20px;
  select{
    width: 30%;
    option{
      margin-bottom: 20px;
    }
  }
  input{
    flex: 1;
  }
}
.results{
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #fff;
  border-radius: 16px;
  border: 1px solid #333333;
  margin-top: 30px;
  flex: 1;
  .title{
    font-size: 58px;
    color: #000000;
    margin-bottom: 20px;
  }
  .inner{
    width: 100%;
    padding-bottom: 30px;
    text{
      color: #000;
    }
  }
}
</style>
 