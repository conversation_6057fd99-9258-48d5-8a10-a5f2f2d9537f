import { createRequest } from '@quickapp/utils'
import config from './config'
const weatherRequest = createRequest(config.WEATHER_API_HOST, false)

function fetchWeather(url, data, method = 'POST') {
  return weatherRequest({ url, data, method })
}

/**
 * 天气数据接口
 * <AUTHOR>
 * @date    2021/07/16 14:37
 * @desc    天气-model
 *          https://seniverse.yuque.com/books/share/e52aa43f-8fe9-4ffa-860d-96c0f3cf1c49/gfc5zb
 *          http://spring-yapi.zhuishushenqi.com/project/15/interface/api/9
 *
 * @param location          位置
 * @param weather_now       天气实况
 * @param air_now           空气质量实况
 * @param weather_hourly    24小时逐小时天气预报
 * @param weather_daily     未来15天逐日天气预报和昨日天气
 * @param sun_riseset       日出日落时间
 * @param life_suggest      生活指数
 * @param weather_forecast  视频播报
 * @param alarms            气象灾害预警
 * @param speech_audio      语音播报
 */
function weatherViewNow(province, city, district, street, latitude, longitude) {
  return fetchWeather('/v1/weather/view/now', {
    province,
    city,
    district,
    street,
    latitude,
    longitude,
  })
}

/**
 * "hour_rain_list": [],#24小时降雨时间表
"hour_snow_list": [],#24小时降雪时间表
"day15_rain_list": [#15天降雪时间表
  "2022-05-24",
  "2022-05-25",
  "2022-05-29"
  ],
"day15_snow_list": [],#15天降雪时间表
"day45_rain_num": 1,#45天降水天数
"day45_snow_num": 0,#45天降雪天数
"day45_avg": "25.58",#45天平均温度
"day45_cold_num": 4#45天降温天数
"day15_high_temperature_date": "2022-05-25",#15日最高气温日期
"day15_high_temperature": "35",#15日最高气温
"day15_low_temperature_date": "2022-05-17",#15日最低气温日期
"day15_low_temperature": "12",#15日最低气温
 */
function rainState(province, city, district, street, latitude, longitude) {
  return fetchWeather('/v1/weather/hour/rain_state', {
    province,
    city,
    district,
    street,
    latitude,
    longitude,
  })
}

/**
 * 获取热门城市景区-接口返回
 *
 * @param hot_city      热门城市
 * @param scenic_area   热门景区
 */
function areaRecommend() {
  return fetchWeather('/v1/area/recommend', null, 'GET')
}

/**
 * 城市地理位置搜索
 *
 * @param code          城市code
 * @param street        街道
 * @param district      区
 * @param city          市
 * @param province      省
 * @param level         当前城市层级(1:省/直辖市  2:市区  3:区/县  4:乡镇)
 */
function areaSearch(keyword) {
  return fetchWeather('/v1/area/search', { keyword }, 'GET')
}

/**
 * 上报当前设备位置信息-接口入参
 *
 * @param app_id        应用id
 * @param udid          设备id
 * @param province      省
 * @param city          市
 * @param district      区
 * @param street        街道
 * @param latitude      纬度
 * @param longitude     经度
 */
function clientLocation(param) {
  return fetchWeather('/v1/client/location', param)
}

export default {
  weatherViewNow,
  areaRecommend,
  areaSearch,
  clientLocation,
  rainState,
}
