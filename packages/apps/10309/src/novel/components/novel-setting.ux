<template>
  <div
    class="wrapper"
    style="background-color: {{currentTheme.settingPanel.bgColor}}"
  >
    <div class="row font-box">
      <text
        class="label"
        style="color: {{currentTheme.settingPanel.frontColor}};"
      >
        字体
      </text>
      <div class="content">
        <div class="line"></div>
        <text
          @click="handleChangeFont(item)"
          class="font-item {{currentFont === item ? 'font-item-active' : '' }}"
          for="item in fontList"
          tid="$idx"
        >
          {{ item }}
        </text>
      </div>
    </div>

    <div class="row color-box">
      <text
        class="label"
        style="color: {{currentTheme.settingPanel.frontColor}};"
      >
        背景
      </text>
      <div class="content">
        <div
          @click="handleChangeTheme(item)"
          class="color-item  {{currentTheme.bgColor === item.bgColor ? 'color-item-active' : ''}}"
          for="item in themeList"
          tid="$idx"
          style="background-color: {{item.bgColor}};"
        ></div>
      </div>
    </div>

    <div class="footer">
      <text
        class="text prev"
        @click="handlePrev"
        style="color: {{hasPrev ? currentTheme.settingPanel.frontColor : currentTheme.settingPanel.disabledColor}};"
      >
        上一章
      </text>
      <text
        class="text table"
        @click="handleTableContent"
        style="color: {{currentTheme.settingPanel.frontColor}};"
      >
        目录
      </text>
      <text
        class="text next"
        @click="handleNext"
        style="color: {{hasNext ? currentTheme.settingPanel.frontColor : currentTheme.settingPanel.disabledColor}};"
      >
        下一章
      </text>
    </div>
  </div>
</template>

<script>
import {
  defaultFontSize,
  fontSizeList,
  themeList,
  defaultTheme,
} from '@/novel/helper/business'

export default {
  data: {
    fontList: fontSizeList,
    currentFont: defaultFontSize,
    themeList: themeList,
    currentTheme: defaultTheme,
  },

  // 初始化默认参数
  props: {
    fontSize: {
      type: Number,
    },
    theme: {
      type: Object,
    },
    hasNext: {
      type: Boolean,
      default: true,
    },
    hasPrev: {
      type: Boolean,
      default: true,
    },
  },

  onInit() {
    this.currentFont = this.fontSize
    this.currentTheme = this.theme

    console.log('current theme +++', this.theme)
  },

  handleTableContent() {
    this.$emit('clickTableContent')
  },

  handlePrev() {
    if (this.hasPrev) {
      this.$emit('clickPrev')
    }
  },

  handleNext() {
    if (this.hasNext) {
      this.$emit('clickNext')
    }
  },

  handleChangeFont(item) {
    this.currentFont = item
    this.$emit('changeFont', {
      font: item,
    })
  },

  handleChangeTheme(item) {
    this.currentTheme = item
    this.$emit('changeTheme', {
      theme: item,
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  padding: 0 40px;
  background-color: rgba(232, 224, 209, 1);
}
.title {
  text-align: center;
  color: #212121;
}

.row {
  padding: 33px 0;

  .label {
    font-size: 32px;
    color: #333333;
    margin-right: 64px;
  }
  .content {
    flex: 1;
    align-items: center;

    .font-item {
      width: 64px;
      height: 64px;
      text-align: center;
      background-color: #f6f1e7;
      border-radius: 50%;
      font-size: 28px;
      font-weight: bold;
      color: rgba(56, 39, 12, 0.6);
    }

    //
    .font-item-active {
      width: 72px;
      height: 72px;
      font-size: 36px;
      font-weight: bolder;
      color: #38270c;
      border: 3px solid #a19378;
    }

    .line {
      position: absolute;
      width: 100%;
      height: 28px;
      background-color: #ccc3b4;
      border-radius: 14px;
    }

    .color-item {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      margin-right: 60px;
    }

    .color-item-active {
      border: 3px solid #ccc3b4;
    }
  }
}

.color-box {
  .content {
    justify-content: flex-start;
  }
}

.font-box {
  .content {
    justify-content: space-between;
  }
}

.footer {
  justify-content: space-between;
  height: 128px;
  align-items: center;
  border-top: 2px solid rgba(201, 187, 161, 0.5);

  .text {
    flex: 1;
    font-size: 32px;
    color: #333333;
    height: 100%;
  }

  .table {
    text-align: center;
  }

  .next {
    text-align: right;
  }

  .disabled {
    color: #ccc;
  }
}
</style>
