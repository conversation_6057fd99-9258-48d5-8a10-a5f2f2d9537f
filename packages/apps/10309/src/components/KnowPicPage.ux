<import name="apex-spin" src="@quickapp/apex-ui/components/spin/index"></import>
<import name="lock-dialog" src="./lock-dialog.ux"></import>
<import
  name="nativead"
  src="@quickapp/business/components/nativead.ux"
></import>

<template>
  <div class="wrapper">
    <lock-dialog
      if="{{visibleDialog && adData}}"
      @unlock="handleUnlock"
      @close="hideDialog"
    ></lock-dialog>
    <div class="header">
      <text class="title">全能大师</text>
    </div>
    <div class="list">
      <div class="list__item" @click="toCamera('text')">
        <div class="list__item__imgbox">
          <image
            class="list__item__icon"
            src="/assets/images/ill/ill_scanning.webp"
          ></image>
        </div>

        <text class="list__item__title">文件扫描</text>
        <!-- <text class="list__item__desc">纸质文件转PDF扫描件</text> -->
      </div>
      <div class="list__item list__item--half" @click="toCamera('id')">
        <div class="list__item__imgbox">
          <image
            class="list__item__icon"
            src="/assets/images/ill/ill_id.webp"
          ></image>
        </div>

        <text class="list__item__title">身份证扫描</text>
        <!-- <text class="list__item__desc">精准识别图片文字</text> -->
      </div>
      <div class="list__item list__item--half" @click="toCamera('table')">
        <div class="list__item__imgbox">
          <image
            class="list__item__icon"
            src="/assets/images/ill/ill_table.webp"
          ></image>
        </div>

        <text class="list__item__title">表格识别</text>
        <!-- <text class="list__item__desc">直接生成EXCEL文件</text> -->
      </div>
      <div class="list__item list__item--half" @click="toCamera('text')">
        <div class="list__item__imgbox">
          <image
            class="list__item__icon"
            src="/assets/images/ill/ill_text.webp"
          ></image>
        </div>

        <text class="list__item__title">文字识别</text>
        <!-- <text class="list__item__desc">精准识别图片文字</text> -->
      </div>
      <div
        class="list__item list__item--half"
        @click="toCamera('certificates')"
      >
        <div class="list__item__imgbox">
          <image
            class="list__item__icon"
            src="/assets/images/ill/ill_certificates.webp"
          ></image>
        </div>

        <text class="list__item__title">证件扫描</text>
        <!-- <text class="list__item__desc">直接生成PDF文件</text> -->
      </div>
    </div>
    <div class="ad-box">
      <nativead oppo-id="2228690" honor-id="1915588601965576192"></nativead>
    </div>
    <div if="loading" class="loading-wrap">
      <apex-spin loading="{{loading}}" tip="正在解析..."></apex-spin>
    </div>
  </div>
</template>

<script>
import media from '@system.media'
import file from '@system.file'
import { storage } from '@quickapp/utils'
import image from '@system.image'
import dayjs from 'dayjs'
import { filePath, fileTypeList } from '@/config/files'
import router from '@system.router'
import request from '@system.request'
import { getUserInfo } from '@quickapp/utils/lib/api/ocr/user'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  data() {
    return {
      loading: false,
      list: [...fileTypeList],
      visibleDialog: false,
      currentType: {},
      adData: null,
      userInfo: {},
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')

    // this.toCamera()
    // router.push({
    //   uri: 'pages/Recharge',
    // })
  },

  handleLifeCycleShow() {
    this.getUserInfo()
  },

  toCamera(category) {
    router.push({
      uri: 'pages/Camera',
      params: {
        category,
      },
    })
  },

  loadRewardAd() {},

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getUserInfo()
    }
  },

  getUserInfo() {
    getUserInfo().then(res => {
      if (res) {
        this.userInfo = res
      }
    })
  },

  showLoading() {
    this.loading = true
  },

  hideLoading() {
    this.loading = false
  },

  async takePhoto(item) {
    this.currentType = item

    this.loadRewardAd()

    if (!item.type) {
      return router.push({ uri: 'pages/MyDoc' })
    }

    const hasTakePhoto = await this.getLocalFirstTakePhoto()

    if (!hasTakePhoto) {
      this.setLocalFirstTakePhoto()
      this.handleTakePhoto()
      return
    }

    if (!this.adData) {
      this.handleTakePhoto()
      return
    }

    this.showDialog()
  },

  setLocalFirstTakePhoto() {
    return storage.set('isFirstTakePhoto', true)
  },

  getLocalFirstTakePhoto() {
    return storage.get('isFirstTakePhoto')
  },

  handleUnlock() {
    this.hideDialog()

    this.watchAdAndTakePhoto()
  },

  watchAdAndTakePhoto() {
    this.watchAd().finally(() => {
      this.loadRewardAd()
      this.handleTakePhoto()
    })
  },

  async handleTakePhoto() {
    console.log('------handleTakePhoto')
    return (
      this.mediaTakePhoto()
        .then(({ uri }) => this.compressImage(uri))
        .then(data => {
          this.showLoading()
          return data
        })
        // .then(data => this.requestUpload(data.uri, this.currentType.type))
        // .then(res => (res.data ? res.data.join('\n') : '服务器解析错误'))
        // .then(text => this.writeText(text, this.currentType.id))
        // .then(({ uri }) => {
        //   router.push({
        //     uri: 'pages/PreViewDoc',
        //     params: {
        //       fileUri: uri,
        //     },
        //   })
        // })
        // .catch(() => showToast('解析失败，请重试'))
        .finally(this.hideLoading.bind(this))
    )
  },

  showDialog() {
    this.visibleDialog = true
  },

  hideDialog() {
    this.visibleDialog = false
  },

  // 看广告。。。。
  watchAd() {},

  // 拍照
  mediaTakePhoto() {
    return new Promise((resolve, reject) => {
      media.takePhoto({
        success: resolve,
        fail: () => reject('您可能未开权限'),
      })
    })
  },

  // 压缩图片
  compressImage(uri) {
    return new Promise((resolve, reject) => {
      image.compressImage({
        uri,
        quality: 5,
        success: data => {
          resolve(data)
        },
        fail: () => reject('网络错误'),
      })
    })
  },

  // 写入文件
  writeText(text, id) {
    let extension = '.doc'
    const fileName = dayjs().format('YYYY年MM月DD日HH点mm分ss秒') + '.' + id
    const uri = filePath + fileName + extension

    return new Promise((resolve, reject) => {
      file.writeText({
        uri: uri,
        text: text,
        success: () => {
          resolve({ uri })
        },
        fail: (data, code) => {
          reject('解析失败')
        },
      })
    })
  },

  // 文件上传
  requestUpload(uri, type) {
    return request({
      isUpload: true,
      files: [{ uri: uri }],
      data: {
        type: type,
      },
    })
  },
  handleOss(fileData) {
    console.log(fileData)
  },
  toVipPage() {
    router.push({
      uri: 'pages/Recharge',
    })
  },
}
</script>

<style lang="less">
@import '../assets/styles/style';

.wrapper {
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 140px;
  /* background: linear-gradient(180deg, #e8f2ff 0%, #ffffff 100%); */
  background-color: #fff;
}

.header {
  width: 100%;
  align-items: center;
  /* text-align: center; */
  justify-content: center;
  padding: 30px;
  margin-bottom: 42px;
  background-color: #35c190;
  color: #fff;
  font-size: 38px;
  .title {
    color: #fff;
    font-size: 38px;
  }
  /* &__logo-title {
    height: 36px;
    align-self: flex-start;
  } */

  &__vip-icon {
    width: 96px;
  }
}

.list {
  flex: 1;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 28px;

  &__item {
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
    border-radius: 20px;
    margin-bottom: 24px;
    background-color: #35c190;

    &__imgbox {
      width: 160px;
      height: 160px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    &__icon {
      width: 100px;
    }

    &__title {
      font-size: 42px;
      font-weight: bolder;
      color: #fff;
      margin-top: 6px;
    }

    &__desc {
      font-size: 24px;
      color: rgba(51, 51, 51, 0.6);
      margin-top: 12px;
    }
  }
}

.ad-box {
  width: 100%;
  flex-direction: column;
  padding: 20px 10px;
}

.loading-wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
}
</style>
