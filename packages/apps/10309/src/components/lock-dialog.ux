<import name="short-cut" src="@quickapp/mc-ui/components/short-cut.ux"></import>

<template>
  <div class="wrapper">
    <div class="inner">
      <image
        class="inner__close"
        @click="handleClose"
        src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_close.png"
      ></image>
      <text class="inner__title">观看视频解锁识图功能</text>
      <div class="inner__footer">
        <!--<text-->
        <!--  if="showAddDeskBtn"-->
        <!--  class="inner__footer__btn inner__footer__btn&#45;&#45;add-desk"-->
        <!--  @click="handleAddDesk"-->
        <!--&gt;-->
        <!--  添加到桌面-->
        <!--</text>-->
        <!--<div-->
        <!--  if="showAddDeskBtn"-->
        <!--  class="inner__footer__btn inner__footer__btn&#45;&#45;add-desk"-->
        <!--  @success="addDeskSuccess"-->
        <!--&gt;-->
        <!--</div>-->
        <!--<short-cut width="305px" text="添加到桌面"></short-cut>-->
        <text
          class="inner__footer__btn inner__footer__btn--unlock"
          @click="handleUnlock"
        >
          立即解锁
        </text>
      </div>
    </div>
  </div>
</template>

<script>
import shortcut from '@system.shortcut'
import { showToast } from '@quickapp/utils'

export default {
  data: {
    showAddDeskBtn: true,
  },

  onInit() {
    shortcut.hasInstalled({
      success: result => {
        this.showAddDeskBtn = !result
      },
    })
  },

  handleClose() {
    this.$emit('close')
  },

  handleAddDesk() {
    shortcut.install({
      success: () => {
        this.showAddDeskBtn = false
      },
      fail: () => {
        showToast('添加失败，您之前可能拒绝过创建图标')
      },
    })
  },

  handleUnlock() {
    this.$emit('unlock')
  },

  addDeskSuccess() {
    this.showAddDeskBtn = false
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
}

.inner {
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 610px;
  padding: 40px 15px;
  background-color: #ffffff;
  border-radius: 20px;

  &__close {
    position: absolute;
    right: 20px;
    top: 20px;
  }

  &__title {
    margin-top: 40px;
    font-size: 36px;
    font-weight: bolder;
    color: #333333;
  }

  &__footer {
    margin-top: 60px;
    justify-content: space-between;
    width: 100%;

    &__btn {
      flex: 1;
      text-align: center;
      height: 88px;
      border-radius: 20px;
      margin: 0 15px;

      &--add-desk {
        //border: 2px solid #96876b;
      }

      &--unlock {
        background-color: #fe7954;
        color: white;
      }
    }
  }
}
</style>
