<template>
  <div class="wrapper">
    <list class="tab" flex-direction="row">
      <list-item
        class="tab__item {{currentTabIndex === $idx ? 'tab__item--active' : ''}}"
        type="item"
        for="item in tabList"
        tid="$idx"
        @click="switchTab($idx)"
      >
        <text
          class="tab__item__text {{currentTabIndex === $idx ? 'tab__item__text--active' : ''}}"
        >
          {{ item.text }}
        </text>
      </list-item>
    </list>
    <list if="list && list.length" class="pic-list" columns="3">
      <list-item
        type="item"
        class="pic-list__item"
        for="item in list"
        tid="$idx"
        @click="previewImg(item)"
      >
        <image src="{{item}}"></image>
      </list-item>
    </list>
    <div else style="flex: 1; justify-content: center; align-items: center">
      <text>暂无数据</text>
    </div>
  </div>
</template>

<script>
import { wallpaperList } from '@/api'
import router from '@system.router'
import adsdk from '@quickapp/business/lib/adsdk'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data: {
    tabList: [
      { text: '女生' },
      { text: '男生' },
      { text: '宠物' },
      { text: '动漫' },
    ],
    currentTabIndex: 0,
    allList: [],
  },

  computed: {
    list() {
      const res = this.allList[this.currentTabIndex]
      return res ? res.imgList : []
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      wallpaperList().then(res => {
        if (res) {
          this.allList = res
          this.tabList = res.map(it => ({ text: it.category }))
        }
      })
    }
  },

  switchTab(index) {
    this.currentTabIndex = index
  },

  previewImg(url) {
    router.push({
      uri: 'pages/ImgPreview',
      params: {
        uri: url,
      },
    })
  },
}
</script>

<style lang="less">
@import '../assets/styles/style';

.wrapper {
  width: 100%;
  flex-direction: column;
  padding-top: 138px;
}

.tab {
  height: 72px;
  padding: 0 40px;

  &__item {
    justify-content: center;
    width: 156px;
    height: 72px;
    background-color: #ffffff;
    border-radius: 44px;
    opacity: 0.9;
    margin-right: 24px;

    &--active {
      background-color: @mainColor;
    }

    &__text {
      color: #333;

      &--active {
        color: white;
      }
    }
  }
}

.pic-list {
  flex: 1;
  margin-top: 40px;
  padding: 0 25px;

  &__item {
    width: 204px;
    margin: 0 15px 30px;

    image {
      width: 100%;
      border-radius: 20px;
    }
  }
}
</style>
