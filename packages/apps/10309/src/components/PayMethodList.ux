<template>
  <div class="pay-method">
    <text class="pay-method__title">选择支付方式</text>
    <div class="pay-method__list">
      <div
        class="pay-method__list__item"
        for="item in list"
        tid="$idx"
        @click="handleClick(item, $idx)"
      >
        <image class="pay-method__list__item__icon" src="{{item.icon}}"></image>
        <text class="pay-method__list__item__label">
          {{ item.label }}
        </text>
        <div
          if="{{currentIndex === $idx}}"
          class="pay-method__list__item__dot pay-method__list__item__dot--active"
        ></div>
        <div else class="pay-method__list__item__dot"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    defaultIndex: {
      default: 0,
    },
  },
  data() {
    return {
      currentIndex: 0,
      list: [
        {
          type: 1,
          label: '支付宝',
          icon: '/assets/images/ic_ali.webp',
        },
        // {
        //   type: 2,
        //   label: '微信',
        //   icon: '/assets/images/ic_wx.webp',
        // },
      ],
    }
  },

  onInit() {
    this.currentIndex = this.defaultIndex
  },

  handleClick(item, index) {
    this.currentIndex = index
    this.$emit('change', {
      item,
      index,
    })
  },
}
</script>

<style lang="less">
.pay-method {
  width: 100%;
  border-radius: 24px;
  padding: 40px 30px;
  flex-direction: column;
  background-color: #ffffff;

  &__title {
    font-size: 32px;
    font-weight: bolder;
    color: #333333;
    line-height: 32px;
  }

  &__list {
    flex-direction: column;

    &__item {
      margin-top: 40px;
      align-items: center;

      &__icon {
        width: 48px;
        height: 48px;
      }

      &__label {
        margin-left: 16px;
        font-size: 28px;
        font-weight: bolder;
        color: #333333;
        line-height: 40px;
      }

      &__dot {
        width: 24px;
        height: 24px;
        border-radius: 24px;
        margin-left: auto;
        background-color: RGBA(218, 234, 255, 1);

        &--active {
          background-color: RGBA(66, 123, 243, 1);
        }
      }
    }
  }
}
</style>
