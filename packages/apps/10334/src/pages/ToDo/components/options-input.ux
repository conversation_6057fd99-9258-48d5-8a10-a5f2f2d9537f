<template>
  <div class="options">
    <div class="input">
      <text>问题:</text>
      <input type="text" value="{{childName}}" @change="handleQuestionChange" />
    </div>
    <div class="input">
      <text>答案:</text>
      <input type="text" value="{{childAnswer}}" @change="handleAnswerChange" />
    </div>

    <div class="cha" @click="handleRemove">
      <image class="cha" src="/assets/images/cha.webp"></image>
    </div>
  </div>
</template>

<script>
export default {
  props: ['name', 'answer', 'index'],

  data() {
    return {
      childAnswer: this.answer || '',
      childName: this.name || ''
    }
  },

  handleQuestionChange(e) {
    this.childName = e.value
    this.$emit('updateOption', {
      index: this.index,
      name: this.childName,
      answer: this.childAnswer
    })
  },

  handleAnswerChange(e) {
    this.childAnswer = e.value
    this.$emit('updateOption', {
      index: this.index,
      name: this.childName,
      answer: this.childAnswer
    })
  },

  handleRemove() {
    this.$emit('remove', {
      index: this.index
    })
  }
}
</script>

<style lang="less">
.options {
  padding: 10px;
  padding-right: 20px;
  display: flex;
  /* border: 1px solid #979797; */
  width: 100%;
  border-radius: 20px;
  align-items: center;
  margin-bottom: 20px;
  justify-content: space-between;

  .input {
    border-radius: 10px;
    border: 1px solid #979797;
    width: 45%;
  }
  .cha {
    width: 30px;
    height: 30px;
    cursor: pointer;
  }
}
</style>