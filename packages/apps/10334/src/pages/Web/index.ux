<import name="webview" src="@quickapp/mc-ui/components/webview.ux"></import>

<template>
  <div class="container">
    <webview web-src="{{webSrc}}" title="{{title}}"></webview>
  </div>
</template>

<script>
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  protected: {
    webSrc: '',
    title: '',
  },
})
</script>

<style>
.container {
  flex-direction: column;
  width: 100%;
  height: 100%;
}
</style>
