<template>
  <div class="wrapper">
    <div class="title" @click="toDoPages">
      <image src="/assets/images/edit.webp"></image>
      <text>{{ title }}</text>
    </div>
    <div class="luck">
      <div
        class="luck_inner"
        style="transform: rotate({{rotateDeg}}deg); transition-property: transform;"
      >
        <canvas
          id="canvasid"
          class="myCanvas"
          style="width: 524px; height: 524px; position: absolute"
        ></canvas>
      </div>
      <div class="startBtn" @click="startRotate">
        <image src="/assets/images/start_bg.webp"></image>
      </div>
    </div>
    <div class="reset" @click="resetRotate">
      <text>恢复转盘</text>
    </div>
    <!-- 占位符 -->
    <div if="{{showShortcutBtn}}" style="height: 156px"></div>
  </div>
</template>

<script>
import { showToast, storage, toPrivacyPage, toUserPage } from '@quickapp/utils'
import router from '@system.router'
import shortcut from '@system.shortcut'
import px2vp from '@system.app'
import adsdk from '@quickapp/business/lib/adsdk'
const sysStorage = require('@system.storage')

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    title: {
      type: String,
    },
    list: {
      type: Array,
    },
  },

  data: {
    title: '今晚吃什么',
    optionsList: [{ name: '黄焖鸡' }, { name: '牛肉板面' }],
    length: 0,
    sectorDeg: 60,
    showShortcutBtn: true,
    isRotating: false,
    rotateDeg: 0, // 旋转角度
    duration: 0, // 时间
    result: '',
    NumberTurns: 1,
    stopValue: 0,
    imgUrl: '',
  },

  onInit() {
    this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  onReady() {
    this.darw()
  },

  onDestroy() {},

  handleStorageChange() {
    this.getInitData()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      this.getInitData()
    }

    this.shortcutHasInstall()
  },
  darw() {
    setTimeout(() => {
      const canvasNode = this.$element('canvasid')
      const ctx = canvasNode.getContext('2d')
      const offset = Math.PI / this.optionsList.length
      this.drawSector(ctx, this.optionsList.length, this.optionsList)
    }, 0)
  },
  drawSector(ctx, sections, texts) {
    // 圆心和半径（按原始像素值计算）
    const centerX = 262
    const centerY = 262
    const radius = 262
    const textRadius = radius + 100

    // 计算每个扇形的角度
    const sliceAngle = (2 * Math.PI) / sections

    // 清空画布
    ctx.clearRect(0, 0, 524, 524)

    // 画圆
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)

    // 画扇形
    const colors = ['#a9cee6', '#fa87c2', '#b99dd0', '#d769b6', '#a382c2']
    for (let i = 0; i < sections; i++) {
      ctx.beginPath()
      ctx.moveTo(centerX, centerY)
      ctx.arc(centerX, centerY, radius, sliceAngle * i, sliceAngle * (i + 1))
      ctx.closePath()
      ctx.fillStyle = colors[i % colors.length]
      ctx.fill()

      const textAngle = sliceAngle * i + sliceAngle / 2
      const textX = centerX + (Math.cos(textAngle) * textRadius) / 2
      const textY = centerY + (Math.sin(textAngle) * textRadius) / 2

      const textRotationAngle = textAngle + Math.PI / 2

      ctx.save()
      ctx.translate(textX, textY)
      ctx.rotate(textRotationAngle)

      ctx.fillStyle = 'black'
      ctx.font = '30px Arial'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText(texts[i].name, 0, 0)

      ctx.restore()
    }
  },

  resetRotate() {
    this.rotateDeg = 0
  },

  startRotate() {
    if (this.isRotating) return
    this.isRotating = true

    // this.result = ''
    const base = 1440
    const randomIndex = Math.floor(Math.random() * this.optionsList.length)
    const offset = Math.floor(Math.random() * this.optionsList.length) / 2
    const targetDeg = base + randomIndex * this.sectorDeg - offset

    let timer = null
    // 重置
    this.rotateDeg = offset
    timer = setInterval(() => {
      const finall = base + targetDeg
      // this.result = this.optionsList[randomIndex].name
      if (this.rotateDeg >= finall) {
        clearInterval(timer)
        this.isRotating = false
      }
      // this.rotateDeg = targetDeg
      this.rotateDeg += 30
      console.log(this.rotateDeg)
    }, 30)
  },

  shortcutHasInstall() {
    shortcut.hasInstalled({
      success: result => {
        this.showShortcutBtn = !result
      },
    })
  },

  toDoPages() {
    router.push({
      uri: 'pages/ToDo',
    })
  },
  async getInitData() {
    await storage
      .get('options')
      .then(res => {
        this.optionsList = res || [{ name: '黄焖鸡' }, { name: '牛肉板面' }]
        this.darw()
      })
      .catch(() => {
        this.optionsList = this.darw()
      })

    await storage.get('title').then(res => {
      this.title = res || '今晚吃什么'
    })
    const length = this.optionsList.length
    this.sectorDeg = 360 / length
    // console.log('this.optionsList', this.optionsList)
    // console.log('this.title', this.title)
  },
}
</script>

<style lang="less">
@import '../../assets/styles/style';
.wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 240px;
}
.title {
  margin-bottom: 112px;
  display: flex;
  justify-content: center;
  align-items: center;
  image {
    width: 42px;
    height: 42px;
    margin-right: 10px;
  }
  text {
    color: #424242;
    font-size: 42px;
  }
}
.luck {
  width: 624px;
  height: 624px;
  background-image: url('/assets/images/luck_bg.png');
  background-size: contain;
  padding: 50px;
  position: relative;
  margin: 0 auto;
  .startBtn {
    position: absolute;
    left: 195px;
    top: 195px;
  }
  .luck_inner {
    width: 100%;
    height: 100%;
    position: relative;
    /* transition-duration: 0.5s; */
    transition-timing-function: ease-in;
    .myCanvas {
      position: absolute;
      z-index: 1;
      transform-origin: 250px 250px;
    }
    .luck_inner_container {
      position: absolute;
      width: 50%;
      height: 50%;
      transform-origin: 248px 248px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      text {
        position: relative;
        z-index: 2;
      }
    }
    .luck_inner_own {
      background-color: #ffdcdb;
    }
    .luck_inner_even {
      background-color: #ff6b6b;
    }
  }
}
.reset {
  width: 217px;
  height: 70px;
  border-radius: 10px;
  border: 1px solid #979797;
  margin-top: 114px;
  background-color: #fff;
  text {
    width: 100%;
    color: #424242;
    font-size: 34px;
    text-align: center;
  }
}
</style>
