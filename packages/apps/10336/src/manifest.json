{"id": "10336", "package": "com.yzqn.mypv.edgewise", "configCode": "cf_zhiyamgnge", "name": "知崖", "versionName": "1.0.3", "versionCode": 4, "minPlatformVersion": 1080, "privacyUrl": "https://landing.serveclouds.com/privacy/10336/PrivacyPolicy.html", "userUrl": "https://landing.serveclouds.com/privacy/10336/UserPolicy.html", "guidelinesForTortClaimsUrl": "https://app-h5.springtool.cn/rltq/agreement/guidelines-for-tort-claims.html", "questionUrl": "https://wj.qq.com/s2/9845028/fff1", "serviceUrl": "https://app-h5.springtool.cn/static/views/service/novel.html", "icon": "/assets/images/logo.png", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.canvas"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.geolocation"}, {"name": "system.device"}, {"name": "system.audio"}, {"name": "system.image"}, {"name": "system.network"}, {"name": "system.webview"}, {"name": "system.request"}, {"name": "system.package"}, {"name": "system.storage"}, {"name": "service.account"}, {"name": "system.file"}, {"name": "service.ad"}, {"name": "system.nfc"}, {"name": "system.clipboard"}, {"name": "system.sensor"}], "permissions": [{"origin": "*"}], "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Home": {"component": "index"}, "pages/ToDo": {"component": "index"}, "pages/Web": {"component": "index"}, "pages/Flash": {"component": "index"}, "pages/Account": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "pages/Service": {"component": "index"}, "clean/cleaning": {"component": "index"}, "weather/City": {"component": "index"}, "UnionAd/AdLanding": {"component": "index"}, "UnionAd/AdReward": {"component": "index"}}}, "display": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "themeMode": 0, "menuBarData": {"menuBar": false}, "pages": {"pages/Home": {"titleBarText": "首页", "titleBar": false, "themeMode": 0, "menuBarData": {"menuBar": false}, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0}, "pages/ToDo": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Web": {"titleBarText": ""}, "pages/Service": {"titleBarText": "在线客服"}, "pages/Flash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}, "UnionAd/AdReward": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}, "UnionAd/AdLanding": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}}}}