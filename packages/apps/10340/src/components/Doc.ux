<import name="file-list" src="./FileList.ux"></import>

<template>
  <div class="page">
    <div class="header">
      <text class="title">所有文档({{ allLen }})</text>
    </div>
    <div class="main">
      <div class="certificates" @click="toCertificatesListPage">
        <image
          class="certificates__icon"
          src="/assets/images/ill/ill_certificates.webp"
        ></image>
        <text class="certificates__text">证件扫描</text>
        <text class="certificates__num">{{ certificatesLen }}个</text>
      </div>
      <file-list id="fileList" @init="handleFileListInit"></file-list>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import file from '@system.file'
import { filePath } from '@/config/files'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  data() {
    return {
      allLen: 0,
      certificatesLen: 0,
    }
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  onReady() {
    // this.getDocList()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getDocList()
    }
  },

  handleLifeCycleShow() {
    this.getDocList()
  },

  getDocList() {
    this.$child('fileList').getDocList()
  },

  toCertificatesListPage() {
    router.push({
      uri: 'pages/CertificatesList',
    })
  },

  handleFileListInit({ detail }) {
    console.log('handleFileListInit', JSON.stringify(detail))
    this.allLen = detail.length

    file.list({
      uri: filePath,
      success: data => {
        // this.allLen
        this.certificatesLen = data.fileList.length - this.allLen
      },
    })
  },
}
</script>

<style lang="less">
.page {
  flex-direction: column;
  width: 100%;
  background: linear-gradient(180deg, #e8f2ff 0%, #ffffff 100%);
  .header {
    width: 100%;
    align-items: center;
    /* text-align: center; */
    justify-content: center;
    padding: 30px;
    background-color: #35c190;
    color: #fff;
    height: 140px;
    margin-top: 148px;
    .title {
      color: #fff;
      font-size: 38px;
    }
  }
}

.main {
  padding: 0 28px;
  flex-direction: column;
}
.certificates {
  width: 694px;
  height: 120px;
  background-color: #ffffff;
  border-radius: 12px;
  align-items: center;
  padding-right: 44px;
  margin-top: 48px;
  background-color: #35c190;

  &__icon {
    width: 94px;
    height: 72px;
    margin-left: 24px;
  }

  &__text {
    margin-left: 20px;
    font-size: 32px;
    color: #fff;
  }

  &__num {
    font-size: 32px;
    color: #fff;
    margin-left: auto;
  }
}
</style>
