<import
  name="novel-reader"
  src="@quickapp/mc-ui/components/novel/novel-reader.ux"
></import>

<template>
  <div class="page">
    <novel-reader
      id="novel-reader"
      book-id="{{bookId}}"
      chapter-id="{{chapterId}}"
      name="{{name}}"
    ></novel-reader>
  </div>
</template>

<script>
import novelReaderPageConfig from '@quickapp/mc-ui/components/novel/novelReaderPageConfig'

export default novelReaderPageConfig
</script>

<style lang="less">
.page {
  width: 100%;
  height: 100%;
}
</style>
