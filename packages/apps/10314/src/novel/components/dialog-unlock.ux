<!--<import name="shortcut-btn" src="./shortcut-btn"></import>-->
<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->

<template>
  <div class="wrapper">
    <div class="inner">
      <image
        class="close"
        @click="handleClose"
        src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_close.png"
      ></image>
      <text class="title">观看视频解锁更多章节</text>
      <div class="ad-box">
        <!-- <nativead event-name="unlockChapterNative"></nativead> -->
      </div>
      <div class="footer">
        <div if="{{!hasShortcut}}" class="btn add-desk" @click="addDesk">
          <text>添加到桌面</text>
        </div>
        <text class="btn unlock" @click="handleUnlock">立即解锁</text>
      </div>
    </div>
  </div>
</template>

<script>
import shortcut from '@system.shortcut'
import { showToast } from '@quickapp/utils'

export default {
  props: {
    hasShortcut: {
      type: Boolean,
      default: false,
    },
  },

  handleUnlock() {
    this.$emit('unlock')
  },

  handleClose() {
    this.$emit('close')
  },

  addDesk() {
    shortcut.install({
      success: () => {
        this.$emit('shortcut', true)
      },
      fail: () => {
        showToast('添加桌面失败')
        this.$emit('shortcut', false)
      },
    })
  },
}
</script>

<style lang="less">
.wrapper {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.inner {
  align-items: center;
  flex-direction: column;
  width: 610px;
  background-color: #ffffff;
  border-radius: 20px;
  padding: 30px 30px 60px;

  .close {
    font-size: 24px;
    align-self: flex-end;
  }

  .title {
    margin-top: 32px;
    font-size: 36px;
    font-weight: bold;
    color: #333333;
    line-height: 36px;
  }

  .ad-box {
    margin-top: 40px;
    width: 100%;
    max-height: 600px;
    border-radius: 20px;
  }

  .footer {
    margin-top: 40px;
    margin-right: -30px;
    width: 100%;
    justify-content: space-between;
  }

  .btn {
    flex: 1;
    margin-right: 30px;
    text-align: center;
    height: 88px;
    border-radius: 16px;
    font-size: 32px;
    color: #38270c;
  }

  .add-desk {
    justify-content: center;
    border: 2px solid #96876b;
  }

  .unlock {
    background-color: #ffed5b3b;
    color: white;
  }
}
</style>
