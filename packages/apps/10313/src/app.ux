<script>
import { mixinsApp } from '@quickapp/business'

import globalData from '@quickapp/utils/lib/globalData'

globalData.ad = {
  oppo: {
    native_all: [
      '2229137',
      '2229140',
      '2229142',
      '2229147',
      '2229159',
      '2229160',
      '2229162',
      '2229164',
    ],
  },
  honor: {
    native_all: [
      '1915585781300330496',
      '1916751456762986496',
      '1916751598983839744',
      '1916751811460595712',
      '1916752080128835584',
      '1916752221393387520',
      '1916752349311270912',
      '1916752461980368896',
    ],
  },
}

export default mixinsApp({
  onCreate() {},
})
</script>
