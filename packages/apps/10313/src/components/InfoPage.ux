<import name="news-list" src="@quickapp/mc-ui/components/news-list.ux"></import>

<template>
  <div class="wrapper">
    <news-list></news-list>
  </div>
</template>

<script>
export default {
  name: 'InfoPage.ux',
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data: {
    webSrc: 'https://www.baidu.com/',
    title: '',
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
    }
  },
}
</script>

<style scoped lang="less">
.wrapper {
  flex-direction: column;
  padding-top: 80px;
  width: 100%;
  height: 100%;
}
</style>
