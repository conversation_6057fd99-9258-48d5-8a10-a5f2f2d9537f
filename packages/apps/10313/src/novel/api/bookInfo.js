/**
 * 详情
 */
import { request } from '@quickapp/business/lib/config'

/**
 * 小说介绍页
 * @returns {Promise | Promise<unknown>}
 */
export function getBookInfoData({ id }) {
  return request({
    method: 'GET',
    url: '/novel/book/info',
    data: {
      id,
    },
  })
}

/**
 * 小说目录
 * @returns {Promise | Promise<unknown>}
 */
export function getBookCatalogData({ id, pageNum, size }) {
  return request({
    method: 'GET',
    url: '/novel/book/catalog',
    data: { id, pageNum, size },
  })
}

/**
 * 章节内容
 * @returns {Promise | Promise<unknown>}
 */
export function getChapterContentData({ bookId, chapterId }) {
  return request({
    method: 'GET',
    url: '/novel/book/chapterContent',
    data: { bookId, chapterId },
  })
}

/**
 * 解锁
 * @param bookId {number}
 * @param chapterId {number}
 * @returns {Promise | Promise<unknown>}
 */
export function unlockChapter({ bookId, chapterId }) {
  return request({
    method: 'POST',
    url: '/novel/book/unlockChapter',
    data: { bookId, chapterId },
  })
}
