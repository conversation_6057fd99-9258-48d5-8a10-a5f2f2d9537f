<template>
  <div class="root">
    <div class="title"><text>成语游戏</text></div>
    <div class="bg">
      <div class="answer" for="item in topicList">
        <div class="header">
          <div class="order">
            <text>第{{ order }}题</text>
          </div>
          <div class="successNum">
            <text>答对{{ successNum }}</text>
          </div>
        </div>
        <div class="content">
          <div>
            <text>{{ item.answer[0] }}</text>
          </div>
          <div class="kong">
            <text>{{ answer }}</text>
          </div>
          <div>
            <text>{{ item.answer[1] }}</text>
          </div>
          <div>
            <text>{{ item.answer[2] }}</text>
          </div>
        </div>
        <div class="line"></div>
        <div class="choose">
          <div
            class="left"
            style="background-color:{{bgcolor1}}"
            @click="choose(item.success, 'left', item.id)"
          >
            <text>A.{{ item.success }}</text>
            <image src="{{imgUrl1}}" class="icon"></image>
          </div>
          <div
            class="right"
            style="background-color:{{bgcolor2}}"
            @click="choose(item.finally, 'right', item.id)"
          >
            <text>B.{{ item.finally }}</text>
            <image src="{{imgUrl2}}" class="icon"></image>
          </div>
        </div>
        <div class="btn" @click="nestTopic" disabled="{{disabled}}">
          <text>{{ btnText }}</text>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { answerArr, updatedAnswerList } from './answer'
export default {
  data() {
    return {
      answerData: [], //题库
      topicList: [], //答题数据
      btnText: '下一题',
      successNum: 0,
      order: 0,
      bgcolor1: '',
      bgcolor2: '',
      imgUrl1: '',
      imgUrl2: '',
      disabled: true,
      answer: '',
      status: true,
    }
  },

  onInit() {
    this.getList()
    this.getTopicList()
  },

  getList() {
    this.answerData = updatedAnswerList
  },

  getTopicList() {
    this.topicList = []
    this.topicList = [this.answerData[this.order]]
    this.order++
    this.disabled = true
  },

  choose(choose, position, id) {
    const right = answerArr.find(item => item.id === id).answer[1] === choose
    if (right && this.status) {
      this.successNum++
      this.status = false
    }
    if (right) {
      this.answer = choose
    } else {
      this.answer = answerArr.find(item => item.id === id).answer[1]
    }
    if (right) {
      if (position === 'left') {
        this.imgUrl1 = '/chengyu/assets/ic_success.png'
        this.imgUrl2 = '/chengyu/assets/ic_error.png'
        this.bgcolor2 = '#FB636D'
        this.bgcolor1 = '#65C93B'
      } else {
        this.imgUrl1 = '/chengyu/assets/ic_error.png'
        this.imgUrl2 = '/chengyu/assets/ic_success.png'
        this.bgcolor1 = '#FB636D'
        this.bgcolor2 = '#65C93B'
      }
    } else {
      if (position === 'left') {
        this.imgUrl1 = '/chengyu/assets/ic_error.png'
        this.imgUrl2 = '/chengyu/assets/ic_success.png'
        this.bgcolor1 = '#FB636D'
        this.bgcolor2 = '#65C93B'
      } else {
        this.imgUrl1 = '/chengyu/assets/ic_success.png'
        this.imgUrl2 = '/chengyu/assets/ic_error.png'
        this.bgcolor2 = '#FB636D'
        this.bgcolor1 = '#65C93B'
      }
    }
    this.disabled = false
  },

  nestTopic() {
    if (this.order >= this.answerData.length) {
      this.btnText = '答完了'
      return
    }
    this.bgcolor1 = ''
    this.bgcolor2 = ''
    this.imgUrl1 = ''
    this.imgUrl2 = ''
    this.status = true
    this.answer = ''
    this.getTopicList()
  },

  // getImage(flag) {
  //   if (flag) {
  //     this.imgUrl1 = '/assets/images/ic_error.png'
  //     this.imgUrl2 = '/assets/images/ic_success.png'
  //     this.bgcolor1 = '#FB636D'
  //     this.bgcolor2 = '#65C93B'
  //   } else {
  //     this.imgUrl1 = '/assets/images/ic_success.png'
  //     this.imgUrl2 = '/assets/images/ic_error.png'
  //     this.bgcolor2 = '#FB636D'
  //     this.bgcolor1 = '#65C93B'
  //   }
  // },
}
</script>
<style lang="less">
.root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: linear-gradient(180deg, #685df4 0%, #5d8ff0 100%);
  .title {
    margin-bottom: 40px;
    text {
      color: #ffffff;
      font-size: 46px;
    }
  }
  .bg {
    width: 670px;
    height: 932px;
    padding: 100px 80px;
    background-image: url('/chengyu/assets/topic_bg.png');
    background-size: contain;
    background-repeat: no-repeat;

    .answer {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // justify-content: center;
      align-items: center;
      .header {
        width: 100%;
        height: 60px;
        display: flex;
        justify-content: space-between;
        .order {
          display: flex;
          background-color: #ffffff;
          border-radius: 12px;
          border: 2px solid #333333;
          padding: 0 24px;

          text {
            color: #000000;
            font-size: 32px;
          }
        }
        .successNum {
          padding: 0 24px;
          display: flex;
          background-color: #fb636d;
          border-radius: 12px;
          border: 2px solid #333333;
          text {
            color: #ffffff;
            font-size: 32px;
          }
        }
      }

      .content {
        width: 100%;
        height: 90px;
        display: flex;
        justify-content: center;
        margin-top: 128px;
        div {
          width: 90px;
          height: 90px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 20px;
          text {
            color: #000000;
            font-size: 46px;
            font-weight: 800;
          }
        }
        .kong {
          border: 4px solid #65c93b;
          border-radius: 10px;
        }
      }
      .line {
        width: 100%;
        height: 3px;
        background-color: #b9b9b9;
        margin-top: 100px;
      }
      .choose {
        width: 100%;
        display: flex;
        justify-content: space-around;
        height: 100px;
        margin-top: 40px;
        .left .right {
          width: 220px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .left {
          display: flex;
          align-items: center;
          padding: 0 40px;
          border-radius: 16px;

          text {
            color: #000000;
            font-size: 42px;
          }
          .icon {
            width: 40px;
            height: 40px;
            margin-left: 20px;
          }
        }

        .right {
          display: flex;
          align-items: center;
          padding: 0 40px;
          border-radius: 16px;

          text {
            color: #000000;
            font-size: 42px;
          }
          .icon {
            width: 40px;
            height: 40px;
            margin-left: 20px;
          }
        }
      }
      .btn {
        width: 340px;
        height: 72px;
        background-color: #fbc430;
        border-radius: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px;
        border: 2px solid #333333;
        text {
          color: #000000;
          font-size: 48px;
        }
      }
    }
  }
}
</style>
