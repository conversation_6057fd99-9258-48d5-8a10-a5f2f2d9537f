<import name="bookshelf" src="../../components/bookshelf.ux"></import>
<import name="bookstore" src="../../components/bookstore.ux"></import>
<!-- <import name="intad" src="@quickapp/business/lib/intad.ux"></import> -->
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>
<import name="mine" src="@quickapp/mc-ui/components/setting-page.ux"></import>

<template>
  <div class="container">
    <!-- <intad
      id="intad"
      event-name="ad_home_insert"
      @close="handleAdClose"
    ></intad> -->
    <div class="header">
      <!-- <text
        class="header__title"
        style="color: {{currentHeaderStyle.textColor}}"
      >
        {{ currentTitle }}
      </text> -->
    </div>
    <tabs class="tabs" onchange="changeTabActive" index="{{tabIndex}}">
      <tab-content class="tab-content" scrollable="{{scrollable}}">
        <div for="item in tabbarList" tid="$idx" class="item-container">
          <div class="item-content">
            <component
              is="{{item.pageComponent}}"
              index="{{$idx}}"
              current-index="{{tabIndex}}"
              life-cycle-show="{{lifeCycleShow}}"
            ></component>
          </div>
        </div>
      </tab-content>
      <tab-bar class="tab-bar">
        <div for="item in tabbarList" tid="$idx" class="tab-bar-item">
          <image
            if="{{tabIndex === $idx}}"
            class="tab-bar-icon"
            src="{{item.selectedIconPath}}"
          />
          <image else class="tab-bar-icon" src="{{item.iconPath}}" />
          <text class="tab-text">{{ item.text }}</text>
        </div>
      </tab-bar>
    </tabs>
    <service></service>
  </div>
</template>

<script>
import { trackEvent } from '@quickapp/business'
import router from '@system.router'
import device from '@quickapp/business/lib/device'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    scrollable: true,
    tabIndex: 1,
    tabbarList: [
      {
        iconPath: '/assets/images/ic_bookshelf_normal.webp',
        selectedIconPath: '/assets/images/ic_bookshelf_active.webp',
        pageComponent: 'bookshelf',
        text: '书架',
        trackEvt: [
          {
            category: 'page',
            action: 'show',
            opt_label: 'bookrack_show',
          },
          {
            category: 'page',
            action: 'click',
            opt_label: 'bookrack_click',
          },
        ],
        bgColor: '#33B4A9',
        textColor: '#FFFFFF',
      },
      {
        iconPath: '/assets/images/ic_bookstore_normal.webp',
        selectedIconPath: '/assets/images/ic_bookstore_active.webp',
        pageComponent: 'bookstore',
        text: '书城',
        trackEvt: [
          {
            category: 'page',
            action: 'show',
            opt_label: 'book_town_show',
          },
          {
            category: 'page',
            action: 'click',
            opt_label: 'book_town_click',
          },
        ],
        bgColor: '#FFFFFF',
        textColor: '#333333',
      },
      {
        iconPath: '/assets/images/ic_mine_normal.webp',
        selectedIconPath: '/assets/images/ic_mine_active.webp',
        pageComponent: 'mine',
        text: '我的',
        bgColor: '#FFFFFF',
        textColor: '#333333',
      },
    ],

    lifeCycleShow: 0,
  },

  computed: {
    currentHeaderStyle() {
      return {
        bgColor: this.tabbarList[this.tabIndex].bgColor,
        textColor: this.tabbarList[this.tabIndex].textColor,
      }
    },
    currentTitle() {
      return this.tabbarList[this.tabIndex].text
    },
  },

  protected: {
    bookId: null, // 小说id
    chapterId: null, // 章节id
  },

  onShow() {
    this.lifeCycleShow++
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'main',
    })
  },
  changeTabActive(e) {
    this.tabIndex = e.index
    // this.setTitleBar()

    this.handleTrackEvt()
  },
  setTitleBar() {},
  handleTrackEvt() {
    const trackEvtList = this.tabbarList[this.tabIndex].trackEvt
    if (trackEvtList && trackEvtList.length) {
      trackEvtList.forEach(it => {
        trackEvent(it)
      })
    }
  },
  onBackPress() {
    return false
  },
  handleAdClose() {},
  onInit() {
    if (
      this.bookId &&
      this.chapterId &&
      this.bookId !== 'null' &&
      this.chapterId !== 'null'
    ) {
      router.push({
        uri: 'pages/novel/introduce',
        params: {
          bookId: this.bookId,
          chapterId: this.chapterId,
          ___PARAM_PAGE_ANIMATION___: {
            openEnter: `none`,
            closeEnter: `slide`,
            openExit: `slide`,
            closeExit: `slide`,
          },
        },
      })
    }
  },
})
</script>

<style lang="less">
@import '../../assets/styles/style';

.container {
  flex-direction: column;
}

.header {
  @ptHeight: 34px;
  height: 88px + @ptHeight;
  padding: @ptHeight 48px 0;

  &__title {
    font-size: 40px;
    font-weight: bolder;
    color: #333333;
  }
}

.tabs {
  flex: 1;
}

.tab-content {
  flex: 1;
}

.tab-bar {
  height: 124px;
  color: #292938;
  //border-bottom: 1px solid #eee;

  .tab-bar-item {
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .tab-bar-icon {
      width: 64px;
      height: 64px;
    }

    .tab-text {
      width: 300px;
      text-align: center;
      color: #999999;
    }

    .tab-text:active {
      color: #333;
    }
  }
}

.item-container {
  //padding-left: 30px;
  //padding-right: 30px;
  flex-direction: column;
}
.item-content {
  flex-direction: column;
  padding-bottom: 30px;
}

.service-box {
  flex-direction: column;
  position: absolute;
  left: 48px;
  bottom: 150px;

  image {
    width: 132px;
    height: 132px;
  }
}
</style>
