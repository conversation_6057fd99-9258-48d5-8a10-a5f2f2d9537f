<import name="no-data" src="@quickapp/mc-ui/components/no-data.ux"></import>
<import name="my-navbar" src="@quickapp/mc-ui/components/navbar.ux"></import>
<import name="short-cut" src="@quickapp/mc-ui/components/short-cut.ux"></import>

<template>
  <div class="wrapper">
    <swiper
      class="swiper"
      autoplay="true"
      index="{{swiperOpt.index}}"
      interval="{{swiperOpt.interval}}"
      indicator="{{swiperOpt.indicator}}"
    >
      <div class="swiper__item" for="item in recommend" tid="$idx">
        <div class="swiper__item__container">
          <image
            class="swiper__item__container__cover"
            src="{{item.cover}}"
          ></image>
          <div class="swiper__item__container__info">
            <text class="swiper__item__container__info__title">
              {{ item.name }}
            </text>
            <text class="swiper__item__container__info__desc">
              {{ item.introduction }}
            </text>
          </div>
        </div>
        <text class="swiper__item__btn" @click="toIntroduce(item.id)">
          继续阅读
        </text>
      </div>
    </swiper>

    <div class="list">
      <div class="list__header">
        <!-- <image
          class="list__header__icon"
          src="/assets/images/ic_book.png"
        ></image> -->
        <!-- <text class="list__header__title">我的书架</text> -->
        <text class="list__header__title"></text>
        <text if="{{!isEdit}}" class="list__header__btn" @click="handleEdit">
          编辑
        </text>
        <block else>
          <text class="list__header__btn" @click="handleDelete">删除</text>
          <text class="list__header__btn" @click="handleComplete">完成</text>
        </block>
      </div>
      <div class="list__content">
        <div
          class="list__content__item"
          for="item in list"
          itd="$idx"
          @click="handleItemClick(item)"
          style="max-width:180px;min-width:180px;margin-right: {{($idx + 1) % 3 === 0 ? '0px' : '63px'}}"
        >
          <image
            class="list__content__item__cover"
            src="{{item.cover}}"
          ></image>
          <div style="align-items: flex-start; height: 100px; width: 180px">
            <text class="list__content__item__name">{{ item.name }}</text>
          </div>
          <text class="list__content__item__chapter">
            {{ item.chapterCount }}章
          </text>
          <block if="{{isEdit}}">
            <image
              if="{{item.isChecked}}"
              class="list__content__item__icon"
              src="/assets/images/ic_checked.webp"
            ></image>
            <image
              else
              class="list__content__item__icon"
              src="/assets/images/ic_not_checked.webp"
            ></image>
          </block>
        </div>
      </div>
    </div>
    <div class="add-desk">
      <!--<short-cut-->
      <!--  if="{{isCurrentPage}}"-->
      <!--  text="添加到桌面"-->
      <!--  width="100%"-->
      <!--  height="100px"-->
      <!--  bg-color="#008BFF"-->
      <!--&gt;</short-cut>-->
    </div>
  </div>
</template>
<script>
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import { getBookshelfData, removeBookShelf } from '@/api/bookshelf'
import shortcut from '@system.shortcut'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data() {
    return {
      swiperOpt: {
        index: 0,
        interval: 5000,
        indicator: false,
      },
      recommend: [
        {
          id: 1,
          cover: '/assets/images/logo.png',
          name: '开局地摊卖大力1',
          introduction:
            '地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代',
        },
      ],
      list: [],
      isEdit: false,
      removeList: new Set(),
      hasShortCutInstall: false,
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
    isCurrentPage() {
      return this.index === this.currentIndex
    },
  },

  onInit() {
    this.checkedInstalled()
    this.getData()
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('isEdit', 'handleChangeIsEdit')
  },

  handleChangeIndex() {
    this.checkedInstalled()
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    } else {
      this.isEdit = false
    }
  },
  handleChangeIsEdit(isEdit) {
    if (!isEdit) {
      this.list.forEach(it => (it.isChecked = false))
      this.removeList.clear()
    }
  },
  handleEdit() {
    this.isEdit = true
  },
  handleComplete() {
    this.isEdit = false
  },

  checkedInstalled() {
    shortcut.hasInstalled({
      success: res => {
        this.hasShortCutInstall = res
      },
    })
  },

  getData() {
    getBookshelfData().then(res => {
      if (res && res.myBookshelfRecommend) {
        this.list = res.myBookshelfRecommend.map(it => ({
          ...it,
          isChecked: false,
        }))
        this.recommend = res.recommendList
      }
    })
  },
  handleItemClick(item) {
    if (this.isEdit) {
      item.isChecked = !item.isChecked
      if (item.isChecked) {
        this.removeList.add(item.id)
      } else {
        this.removeList.delete(item.id)
      }
    } else {
      this.toIntroduce(item.id)
    }
  },
  removeBookShelf() {
    removeBookShelf({ idList: [...this.removeList] })
      .then(() => {
        this.getData()
        this.removeList.clear()
      })
      .catch(() => {
        showToast('删除失败')
      })
  },
  handleDelete() {
    if (this.removeList.size === 0) {
      showToast('请先选择要删除的书')
    } else if (this.removeList.size === this.list.length) {
      showToast('至少保留一本书')
    } else {
      this.removeBookShelf()
    }
  },
  addDesk() {
    shortcut.install({
      success: () => {
        this.hasShortCutInstall = true
      },
    })
  },
  toIntroduce(id) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id,
      },
    })
  },
}
</script>

<style lang="less">
@import '../assets/styles/style';

.wrapper {
  position: relative;
  width: 100%;
  flex-direction: column;
  background-color: #2a2a2a;
  background-image: url('/assets/images/background-bg.png');
  /* background-image: url('/assets/images/a.png'); */
  background-size: 100% auto;
  background-repeat: no-repeat;
}

.swiper {
  height: 330px;
  indicator-color: white;
  indicator-selected-color: @mainLightColor;
  indicator-size: 10px;
  margin: 136px 40px 40px;
  box-sizing: border-box;

  &__item {
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    border-radius: 32px;
    background-color: rgba(255, 255, 255, 0.2);
    &__container {
      width: 100%;
      border-radius: 32px;
      padding: 24px;

      &__cover {
        width: 128px;
        height: 156px;
        border-radius: 16px;
      }

      &__info {
        flex-direction: column;
        justify-content: center;
        box-sizing: border-box;
        margin-left: 24px;

        &__title {
          font-size: 32px;
          font-weight: bold;
          lines: 1;
          text-overflow: ellipsis;
          color: #fff;
        }

        &__desc {
          font-size: 24px;
          font-weight: 400;
          line-height: 34px;
          lines: 2;
          text-overflow: ellipsis;
          margin-top: 16px;
          color: #fff;
        }
      }
    }

    &__btn {
      width: 376px;
      text-align: center;
      font-size: 28px;
      font-weight: bolder;
      color: white;
      height: 88px;
      border-radius: 44px;
      border: 2px solid #fff;
    }
  }
}

.list {
  flex-direction: column;
  /* background-color: #2A2A2A; */
  border-top-right-radius: 40px;
  border-top-left-radius: 40px;
  /* padding-top: 40px; */

  &__header {
    width: 100%;
    align-items: center;
    padding: 0 32px;

    &__icon {
      width: 40px;
      height: 40px;
    }

    &__title {
      flex: 1;
      margin-left: 16px;
      font-size: 36px;
      font-weight: bold;
      color: #fff;
      line-height: 50px;
    }

    &__btn {
      margin-left: 20px;
      font-size: 28px;
      color: #fff;
      line-height: 40px;
    }
  }

  &__content {
    flex-wrap: wrap;
    /* justify-content: space-between; */
    padding: 40px 40px 100px;

    &__item {
      position: relative;
      flex-direction: column;
      width: 180px;
      margin-bottom: 48px;

      &__cover {
        width: 180px;
        height: 240px;
        border-radius: 24px;
      }

      &__name {
        align-self: flex-start;
        font-size: 28px;
        lines: 2;
        text-overflow: ellipsis;
        margin-top: 24px;
        color: #fff;
        font-weight: bolder;
      }

      &__chapter {
        font-size: 24px;
        color: rgba(255, 255, 255, 0.4);
        margin-top: 8px;
      }

      &__icon {
        position: absolute;
        right: 20px;
        top: 10px;
        width: 40px;
        height: 40px;
      }
    }
  }
}

.add-desk {
  position: fixed;
  bottom: 168px;
  padding: 0 32px;
}
</style>
