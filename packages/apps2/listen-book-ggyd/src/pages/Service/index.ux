<import
  name="service-page"
  src="@quickapp/mc-ui/components/service-page.ux"
></import>

<template>
  <div class="wrapper">
    <service-page></service-page>
  </div>
</template>

<script>
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

const pageConfig = {}

// const m_global = global.__proto__ || global
//
// if (m_global.is_from_playable) {
//   pageConfig.onMenuPress = function () {}
// }

export default setPageMenuConfig(pageConfig)
</script>
