<template>
  <div class="content">
    <div class="hot-header">
      <div class="mid">
        <image class="left-icon" src="/assets/images/ic_hander.webp"></image>
        <text class="title">热门推荐</text>
      </div>
      <div class="right" @click="handleSeeMore">
        <text class="more">更多</text>
        <image class="icon" src="/assets/images/ic_arrow_right.webp"></image>
      </div>
    </div>
    <list class="content-header">
      <list-item
        class="type-item"
        for="(index, item) in typeList"
        tid="{{index}}"
        type="item"
      >
        <div show="{{currentIndex === index}}" class="active-bg"></div>
        <text
          class="text {{currentIndex === index ? 'active' : ''}}"
          @click="handleChangeTab(item, index)"
        >
          {{ item.category }}
        </text>
      </list-item>
    </list>
    <div class="content-list">
      <div class="item" for="item in list" tid="$idx" @click="toRead(item)">
        <image class="img" src="{{item.cover}}"></image>
        <div class="text-content">
          <text class="title">{{ item.name }}</text>
          <text class="introduction">{{ item.introduction }}</text>
          <text class="type">{{ currentType.category }}</text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { trackEvent } from '@quickapp/business'

export default {
  data: {
    currentIndex: 0,
  },

  props: {
    defaultIndex: {
      type: Number,
    },
    categoryList: {
      type: Array,
      default: () =>
        new Array(4).fill({
          id: 2,
          category: '都市',
          bookList: new Array(10).fill({
            cover:
              'http://img.1391.com/api/v1/bookcenter/cover/1/3516087/3516087_37d6592bfb0747c4b99768183cdd8bb2.jpg',
            id: 6,
            introduction:
              '一觉醒来，萧君临穿越进了自己看的小说里，可他不是主角，而是男配他爸，这男配还是个舔狗。“老子一世英名都让你毁了，今日起解除我儿子在集团的一切职务。”「叮，宿主与儿子断绝关系，获得年轻二十岁奖励。」“别叫我伯父，臭丫头只知道利用我儿子，今天开始你降职去楼下扫厕所。”「叮，宿主成功打压女主，获得神级投资技能、神级格斗术。」杀舔狗、压女主、夺主角造化，萧君临发现，自己正逐渐走向人生巅峰。',
            name: '穿越成舔狗他爸，我才是天命主角',
          }),
        }),
    },
  },

  computed: {
    currentType() {
      return this.typeList[this.currentIndex]
    },
    typeList() {
      return this.categoryList.map(it => ({
        id: it.id,
        category: it.category,
      }))
    },
    list() {
      const res = this.categoryList[this.currentIndex]
      return res && res.bookList ? res.bookList : []
    },
  },

  onInit() {
    this.currentIndex = Number(this.defaultIndex || 0)
    this.tabTrackEvt()
  },

  handleChangeTab(item, index) {
    this.currentIndex = index
    this.$emit('changeTab', {
      type: item,
      index: this.currentIndex,
    })

    this.tabTrackEvt()
  },

  tabTrackEvt(opt_value = this.currentIndex + 1) {
    trackEvent({
      category: 'page',
      action: 'click',
      opt_label: 'book_town_classify_click',
      opt_value: opt_value,
    })
  },

  handleSeeMore() {
    this.$emit('seeMore', this.currentType)
  },

  toRead(item) {
    router.push({
      uri: 'pages/novel/introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.hot-header {
  align-items: center;
  margin-bottom: 34px;
  position: relative;
  justify-content: center;
  .mid {
    align-items: center;
    image {
      margin-top: 5px;
    }
  }

  .left-icon {
    width: 44px;
    height: 44px;
    margin-right: 16px;
  }

  .title {
    font-size: 32px;
    font-weight: bolder;
    color: #333333;
  }

  .right {
    position: absolute;
    right: 0;
  }

  .more {
    font-size: 28px;
    color: #333333;
  }

  .icon {
    width: 40px;
    height: 40px;
  }
}

.content {
  width: 100%;
  flex-direction: column;
  margin-top: 60px;
}

.content-header {
  flex-wrap: wrap;
  flex-direction: row;
  height: 80px;

  .type-item {
    height: 60px;
    margin-bottom: 20px;
    margin-right: 20px;

    .active-bg {
      position: absolute;
      top: 50px;
      left: 0;
      width: 100%;
      height: 10px;
      background-image: url("/assets/images/line.webp");
      background-repeat: no-repeat;
      background-size: 100%;
    }

    .text {
      width: 100%;
      text-align: center;
      border-radius: 12px;
      font-size: 32px;
      font-weight: 500;
      color: #333;
      padding: 0 20px;
    }

    .active {
      font-weight: bold;
      font-size: 32px;
      /* font-size: 32px; */
      color: #333;
      /* background-color: #3DC88C; */
      border-radius: 30px;
    }
  }
}

.content-list {
  flex-direction: column;
  width: 100%;
  margin-top: 20px;

  .item {
    height: 240px;
    margin-bottom: 40px;
    background-color: #FFF;
    border-radius: 8px;
    .img {
      width: 180px;
      height: 240px;
      border-radius: 10px;
    }

    .text-content {
      flex: 1;
      flex-direction: column;
      justify-content: space-between;
      padding-top: 24px;
      padding-bottom: 10px;
      margin-left: 24px;
      padding-right: 20px;

      .title {
        font-size: 32px;
        font-weight: bolder;
        color: #292939;
        line-height: 45px;
        lines: 2;
        text-overflow: ellipsis;
      }

      .introduction {
        width: 470px;
        height: 84px;
        font-size: 28px;
        color: #29293B;
        line-height: 42px;
        opacity: 0.7;
        lines: 2;
        text-overflow: ellipsis;
      }

      .type {
        font-size: 24px;
        font-weight: 400;
        color: #B2B0B6;
        lines: 1;
        text-overflow: ellipsis;
        width: 126px;
        height: 40px;
        background-color: #F2F2F2;
        border-radius: 9px;
        text-align: center;
      }
    }
  }
}

.see-more {
  position: fixed;
  right: 20px;
  bottom: 150px;
  padding: 15px;
  height: 70px;
  width: 240px;
  border-radius: 20px;
  background-color: #ffff00;
  font-size: 24px;
}
</style>
