{"id": "10141", "package": "com.hxyd.listen", "configCode": "cf_20230109144506", "name": "红霞阅读", "versionName": "1.0.12", "versionCode": 13, "privacyUrl": "https://app-h5.springtool.cn/hxyd/agreement/privacy.html", "userUrl": "https://app-h5.springtool.cn/hxyd/agreement/user.html", "guidelinesForTortClaimsUrl": "https://app-h5.springtool.cn/hxyd/agreement/guidelines-for-tort-claims.html", "questionUrl": "https://wj.qq.com/s2/********/436e", "minPlatformVersion": 1090, "icon": "/assets/images/logo.png", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.storage"}, {"name": "system.network"}, {"name": "system.webview"}, {"name": "system.request"}, {"name": "system.device"}, {"name": "system.package"}, {"name": "service.account"}, {"name": "system.file"}, {"name": "system.animate"}, {"name": "system.nfc"}, {"name": "system.clipboard"}, {"name": "system.sensor"}], "permissions": [{"origin": "*"}], "template/official": "demo-template", "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Flash": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "pages/Home": {"component": "index"}, "pages/bookstore-detail": {"component": "index"}, "pages/novel/introduce": {"component": "index"}, "pages/novel/detail": {"component": "index"}, "pages/novel/reader": {"component": "index"}, "pages/Web": {"component": "index"}, "UnionAd/AdLanding": {"component": "index"}, "UnionAd/AdReward": {"component": "index"}, "pages/Service": {"component": "index"}}}, "display": {"themeMode": 0, "menuBarData": {"menuBar": false}, "menu": true, "pages": {"pages/Home": {"titleBarText": "小说", "titleBar": false, "titleBarTextColor": "#333333", "menuBarData": {"menuBar": false}, "titleBarBackgroundColor": "#FFF", "menu": true}, "pages/bookstore-detail": {"titleBarText": "都市", "titleBarBackgroundColor": "#FFF", "titleBarTextColor": "#333333", "menu": true}, "pages/novel/introduce": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "titleBarBackgroundColor": "#F6F1E7", "menu": true}, "pages/novel/detail": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/novel/reader": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": true}, "pages/Web": {"titleBarText": "", "titleBarBackgroundColor": "#FFF", "titleBarTextColor": "#333333", "menu": true}, "UnionAd/AdReward": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}, "UnionAd/AdLanding": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}, "pages/Flash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}, "pages/Service": {"titleBarText": "在线客服", "menu": true}}}}