{"name": "novel-ldyd", "version": "0.0.0", "description": "兰多阅读", "scripts": {"start": "hap server --watch", "server": "hap server", "build": "hap build", "release": "cross-env NODE_ENV=production hap release --split-chunks-mode=smart", "watch": "hap watch", "debug": "hap debug", "gen": "node ./scripts/gen/index.js", "precommit-msg": "echo '🚧 start pre-commit checks ...' && exit 0", "prettier": "node ./scripts/selfCloseInputTag.js && prettier --write \"src/**/*.{ux,js,json,less,scss,css,pcss,md,vue}\"", "prettier-watcher": "onchange '**/*.md' \"src/**/**/*.{ux,js,json,less,scss,css,pcss,md,vue}\" -- prettier --write {{changed}}"}, "dependencies": {"@quickapp/business": "^0.0.0", "@quickapp/mc-ui": "^0.0.0", "@quickapp/utils": "^0.0.0", "@quickapp/apex-ui": "^1.9.5", "union-quick-app-ad": "^1.0.27"}, "devDependencies": {"@babel/runtime": "^7.12.5", "@types/quickapp": "npm:quickapp-interface@1.0.0", "colors": "^1.4.0", "husky": "^7.0.1", "less": "^4.1.1", "less-loader": "^10.0.1", "lint-staged": "^11.0.1", "onchange": "^5.2.0", "prettier": "^2.3.2", "prettier-plugin-ux": "^0.3.0"}, "prettier": {"printWidth": 80, "tabWidth": 2, "semi": false, "singleQuote": true, "bracketSpacing": true, "trailingComma": "es5", "arrowParens": "avoid", "htmlWhitespaceSensitivity": "ignore"}, "husky": {"hooks": {"pre-commit": "yarn run precommit-msg && lint-staged"}}, "lint-staged": {"**/**.{ux,js,json,less,scss,css,pcss,md,vue}": ["prettier --write", "git add"]}, "keywords": ["快应用", "快应用示例", "快应用模版"], "browserslist": ["chrome 65"]}