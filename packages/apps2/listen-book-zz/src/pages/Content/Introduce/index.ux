<import
  name="table-content"
  src="../../../components/table-content.ux"
></import>
<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<import
  name="apex-popup"
  src="@quickapp/apex-ui/components/popup/index"
></import>

<template>
  <div id="drawer" class="page">
    <list class="wrapper">
      <list-item type="list-header" class="header">
        <image class="cover" src="{{info.cover}}"></image>
      </list-item>

      <list-item type="container" class="container">
        <div class="info">
          <text class="title">{{ info.name }}</text>
          <text class="author">{{ info.author }}</text>
        </div>

        <div class="introduction">
          <text class="title">简介</text>
          <text class="content">
            {{ info.introduction }}
          </text>
        </div>

        <div class="ad-box">
          <!-- <nativead id="nativead" event-name="ad_essay_synopsis"></nativead> -->
        </div>

        <div class="table-contents" @click="openDrawer">
          <text class="title">目录</text>
          <div class="table-contents__chapter">
            <text class="content">已完结 共{{ info.chapterCount }}章</text>
            <image
              class="icon"
              src="/assets/images/ic_arrow_right.webp"
            ></image>
          </div>
        </div>

        <div class="btn-box">
          <div class="read-btn" @click="toDetail">
            <image src="/assets/images/ic_listen.webp"></image>
            <text>开始听书</text>
          </div>
        </div>
      </list-item>
    </list>
    <apex-popup id="popup" position="left">
      <table-content
        id="table-content"
        @click-item="handleClickItem"
      ></table-content>
    </apex-popup>
  </div>
</template>

<script>
import { getBookInfoData } from '@/api/bookInfo'
import router from '@system.router'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

const pageConfig = {
  private: {
    info: {
      author: '悦忱',
      category: '热血兵王',
      chapterCount: 1460,
      cover:
        'http://img.1391.com/api/v1/bookcenter/cover/1/3369879/3369879_750203615b7045148a9db83cecc3e002.jpg',
      id: 1,
      introduction:
        '「都市+爽文+热血」狂尊归来，我为主宰，恩仇必报，逆者必杀，天逆弑天，神逆弑神！狂尊归来战都市，了却恩怨情仇事！四年腥风血雨路，绝弑狂尊为谁戮？我自狂傲逆天行，吾名景昊弑神冥！',
      name: '都市绝弑狂尊',
      tags: '兵王,城市,异术超能,异能,热血兵王,爽文,美女,都市',
      words: 3358878,
      firstChapterId: null,
    },
    showDraw: false,
  },

  protected: {
    id: 2, // 小说id
  },

  onInit() {},

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) {}
    getBookInfoData({ id: this.id }).then(res => {
      if (res) {
        this.info = res
      }
    })
  },

  openDrawer() {
    this.$child('popup').show()
    this.$child('table-content').initData(this.id)
    this.showDraw = true
  },

  closeDrawer() {
    this.$child('popup').hide()
    this.showDraw = false
  },

  handleClickItem({ detail }) {
    this.closeDrawer()

    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: detail.data.bookId,
        chapterId: detail.data.id,
        name: this.info.name,
      },
    })
  },

  toDetail() {
    const toPage = params => {
      router.push({
        uri: 'pages/Content/Detail',
        params,
      })
    }

    const params = {
      bookId: this.info.id,
      chapterId: this.info.chapterId || this.info.firstChapterId,
      name: this.info.name,
    }

    toPage(params)
  },
}

// const m_global = global.__proto__ || global
//
// if (m_global.is_from_playable) {
//   pageConfig.onMenuPress = function () {}
// }

export default setPageMenuConfig(pageConfig)
</script>

<style lang="less">
@import '../../../assets/styles/style';
.page {
  flex-direction: column;
  justify-content: center;
  background-color: #f8f8f8;
}

.wrapper {
  flex-direction: column;
  height: 100%;
}

.header {
  width: 100%;
  justify-content: center;

  .cover {
    width: 340px;
    height: 488px;
    border-radius: 32px;
  }
}

.container {
  flex-direction: column;
  background-color: white;
  margin-top: 120px;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
}

.info {
  flex-direction: column;
  padding-bottom: 40px;
  margin: 40px 40px 0;
  background-color: white;

  .title {
    font-size: 40px;
    font-weight: bold;
    color: #333333;
    line-height: 56px;
  }

  .author {
    font-size: 28px;
    color: #999999;
    line-height: 40px;
    margin-top: 16px;
  }
}

.introduction {
  flex-direction: column;
  margin: 0 40px;
  padding: 32px;
  background-color: #f7f7f7;
  border-radius: 24px;

  .title {
    font-size: 32px;
    font-weight: bold;
  }

  .content {
    text-align: justify;
    margin-top: 20px;
    font-size: 28px;
    color: #999;
    line-height: 48px;
  }
}

.table-contents {
  margin: 0 40px;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;

  .title {
    font-size: 32px;
    font-weight: bold;
    color: #333333;
  }

  &__chapter {
    .content {
      height: 28px;
      color: #999;
    }
    .icon {
      width: 32px;
    }
  }
}

.ad-box {
  align-items: center;
  justify-content: center;
  width: 100%;
  border-radius: 20px;
  //margin: 40px auto 0;
}

.btn-box {
  justify-content: center;
  padding-bottom: 80px;
  margin-top: 40px;

  .read-btn {
    align-items: center;
    justify-content: center;
    width: 670px;
    height: 108px;
    background-color: #00b19f;
    border-radius: 16px;

    text {
      color: white;
      margin-left: 18px;
    }

    image {
      width: 44px;
    }
  }
}
</style>
