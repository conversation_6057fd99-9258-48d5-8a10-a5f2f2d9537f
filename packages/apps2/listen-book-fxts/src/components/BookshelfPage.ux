<import name="no-data" src="@quickapp/mc-ui/components/no-data.ux"></import>
<import name="my-navbar" src="@quickapp/mc-ui/components/navbar.ux"></import>
<import name="short-cut" src="@quickapp/mc-ui/components/short-cut.ux"></import>

<template>
  <div class="wrapper">
    <swiper
      class="swiper"
      autoplay="true"
      index="{{swiperOpt.index}}"
      interval="{{swiperOpt.interval}}"
      indicator="{{swiperOpt.indicator}}"
    >
      <div class="swiper__item" for="item in recommend" tid="$idx">
        <div class="swiper__item__container">
          <image
            class="swiper__item__container__cover"
            src="{{item.cover}}"
          ></image>
          <div class="swiper__item__container__info">
            <text class="swiper__item__container__info__title">
              {{ item.name }}
            </text>
            <text class="swiper__item__container__info__desc">
              {{ item.introduction }}
            </text>
          </div>
        </div>
        <text class="swiper__item__btn" @click="toIntroduce(item.id)">
          继续阅读
        </text>
      </div>
    </swiper>

    <div class="list">
      <div class="list__header">
        <!--<image-->
        <!--  class="list__header__icon"-->
        <!--  src="/assets/images/ic_book.png"-->
        <!--&gt;</image>-->
        <text class="list__header__title">我的书架</text>
        <text if="{{!isEdit}}" class="list__header__btn" @click="handleEdit">
          编辑
        </text>
        <block else>
          <text class="list__header__btn" @click="handleDelete">删除</text>
          <text class="list__header__btn" @click="handleComplete">完成</text>
        </block>
      </div>
      <div class="list__content">
        <div
          class="list__content__item"
          for="item in list"
          itd="$idx"
          @click="handleItemClick(item)"
        >
          <image
            class="list__content__item__cover"
            src="{{item.cover}}"
          ></image>
          <div style="align-items: flex-start; height: 100px">
            <text class="list__content__item__name">{{ item.name }}</text>
          </div>
          <text class="list__content__item__chapter">
            {{ item.chapterCount }}章
          </text>
          <block if="{{isEdit}}">
            <image
              if="{{item.isChecked}}"
              class="list__content__item__icon"
              src="/assets/images/ic_checked.webp"
            ></image>
            <image
              else
              class="list__content__item__icon"
              src="/assets/images/ic_not_checked.webp"
            ></image>
          </block>
        </div>
      </div>
    </div>
    <div class="add-desk">
      <!--<short-cut-->
      <!--  if="{{isCurrentPage}}"-->
      <!--  text="添加到桌面"-->
      <!--  width="100%"-->
      <!--  height="100px"-->
      <!--  bg-color="#7082F0"-->
      <!--&gt;</short-cut>-->
    </div>
  </div>
</template>
<script>
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import { getBookshelfData, removeBookShelf } from '@/api/bookshelf'
import shortcut from '@system.shortcut'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data() {
    return {
      swiperOpt: {
        index: 0,
        interval: 5000,
        indicator: false,
      },
      recommend: [
        {
          id: 1,
          cover: '/assets/images/logo.png',
          name: '开局地摊卖大力1',
          introduction: '1',
        },
      ],
      list: [],
      isEdit: false,
      removeList: new Set(),
      hasShortCutInstall: false,
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
    isCurrentPage() {
      return this.index === this.currentIndex
    },
  },

  onInit() {
    this.checkedInstalled()
    this.getData()
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('isEdit', 'handleChangeIsEdit')
  },

  handleChangeIndex() {
    this.checkedInstalled()
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    } else {
      this.isEdit = false
    }
  },
  handleChangeIsEdit(isEdit) {
    if (!isEdit) {
      this.list.forEach(it => (it.isChecked = false))
      this.removeList.clear()
    }
  },
  handleEdit() {
    this.isEdit = true
  },
  handleComplete() {
    this.isEdit = false
  },

  checkedInstalled() {
    shortcut.hasInstalled({
      success: res => {
        this.hasShortCutInstall = res
      },
    })
  },

  getData() {
    getBookshelfData().then(res => {
      if (res && res.myBookshelfRecommend) {
        this.list = res.myBookshelfRecommend.map(it => ({
          ...it,
          isChecked: false,
        }))
        this.recommend = res.recommendList
      }
    })
  },
  handleItemClick(item) {
    if (this.isEdit) {
      item.isChecked = !item.isChecked
      if (item.isChecked) {
        this.removeList.add(item.id)
      } else {
        this.removeList.delete(item.id)
      }
    } else {
      this.toIntroduce(item.id)
    }
  },
  removeBookShelf() {
    removeBookShelf({ idList: [...this.removeList] })
      .then(() => {
        this.getData()
        this.removeList.clear()
      })
      .catch(() => {
        showToast('删除失败')
      })
  },
  handleDelete() {
    if (this.removeList.size === 0) {
      showToast('请先选择要删除的书')
    } else if (this.removeList.size === this.list.length) {
      showToast('至少保留一本书')
    } else {
      this.removeBookShelf()
    }
  },
  addDesk() {
    shortcut.install({
      success: () => {
        this.hasShortCutInstall = true
      },
    })
  },
  toIntroduce(id) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id,
      },
    })
  },
}
</script>

<style lang="less">
@import '../assets/styles/style';

.wrapper {
  position: relative;
  width: 100%;
  flex-direction: column;
  background-color: @mainColor;
  //background-image: url('/assets/images/bookshelf_bg.webp');
  //background-size: 100%;
}

.swiper {
  height: 376px;
  indicator-color: white;
  indicator-selected-color: @mainLightColor;
  indicator-size: 10px;
  margin: 136px 40px 40px;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0.2);

  &__item {
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    border-radius: 32px;

    &__container {
      width: 100%;
      border-radius: 32px;
      padding: 32px;

      &__cover {
        width: 144px;
        height: 192px;
        border-radius: 4px;
      }

      &__info {
        flex-direction: column;
        justify-content: center;
        box-sizing: border-box;
        margin-left: 24px;

        &__title {
          font-size: 32px;
          font-weight: bold;
          lines: 1;
          color: rgba(255, 255, 255, 1);
          text-overflow: ellipsis;
        }

        &__desc {
          font-size: 24px;
          font-weight: 400;
          line-height: 34px;
          lines: 2;
          color: rgba(255, 255, 255, 0.8);
          text-overflow: ellipsis;
          margin-top: 16px;
        }
      }
    }

    &__btn {
      width: 590px;
      //width: 100%;
      text-align: center;
      font-size: 28px;
      font-weight: bolder;
      color: rgba(96, 130, 170, 1);
      height: 88px;
      background-color: #ffffff;
      border-radius: 8px;
    }
  }
}

.list {
  flex-direction: column;
  //background-color: white;
  //border-top-right-radius: 40px;
  //border-top-left-radius: 40px;
  padding-top: 40px;

  &__header {
    width: 100%;
    align-items: center;
    padding: 0 32px;

    &__icon {
      width: 40px;
      height: 40px;
    }

    &__title {
      flex: 1;
      margin-left: 16px;
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      line-height: 50px;
    }

    &__btn {
      margin-left: 20px;
      font-size: 28px;
      color: rgba(255, 255, 255, 0.7);
      line-height: 40px;
    }
  }

  &__content {
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 40px 40px 100px;

    &__item {
      @w: 180px;
      position: relative;
      flex-direction: column;
      width: @w;
      margin-bottom: 48px;

      &__cover {
        width: @w;
        height: 240px;
        //border-radius: 24px;
      }

      &__name {
        width: @w;
        align-self: flex-start;
        font-size: 32px;
        lines: 2;
        text-overflow: ellipsis;
        margin-top: 24px;
        color: #ffffff;
        font-weight: bolder;
      }

      &__chapter {
        font-size: 24px;
        color: rgba(255, 255, 255, 0.8);
        margin-top: 8px;
      }

      &__icon {
        position: absolute;
        right: 60px;
        top: 10px;
        width: 40px;
        height: 40px;
      }
    }
  }
}

.add-desk {
  position: fixed;
  bottom: 168px;
  left: 0;
  padding: 0 30px;
  //width: 686px;
  //height: 100px;
  //background-color: @mainLightColor;
  //border-radius: 50px;
  //color: white;
  //font-size: 32px;
  //font-weight: bold;
}
</style>
