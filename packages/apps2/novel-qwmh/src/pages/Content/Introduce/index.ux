<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<import name="apex-popup" src="@quickapp/apex-ui/components/popup/index"></import>
<import name="chapter-list" src="../../../components/chapter-list"></import>

<template>
  <div id="drawer" class="page">
    <image
      class="back-btn"
      src="/assets/images/ic_back2.webp"
      @click="back"
    ></image>
    <stack class="top-box">
      <image class="cover" src="{{info.cover}}"></image>
      <div class="top-box__mark"></div>
      <div class="top-box__info">
        <image class="top-box__info__cover" src="{{info.cover}}"></image>
        <div class="top-box__info__right">
          <text class="top-box__info__right__title">{{ info.name }}</text>
          <text class="top-box__info__right__category">
            {{ info.category }} {{ info.chapterCount }}卷
          </text>
          <input
            show="{{showAddBookShelf}}"
            class="top-box__info__right__add-btn"
            type="button"
            value="加入书架"
            @click="addBookShelf"
          />
        </div>
      </div>
    </stack>

    <div class="introduction">
      <text class="introduction__title">简介</text>
      <text class="introduction__content">{{ info.introduction }}</text>
    </div>

    <div class="ad-box">
      <!-- <nativead id="nativead" event-name="ad_essay_synopsis"></nativead> -->
    </div>

    <div class="chapter-box">
      <chapter-list
        book-id="{{id}}"
        @click-item="handleClickItem"
      ></chapter-list>
    </div>
    <div class="footer">
      <!--<input-->
      <!--  show="{{showAddBookShelf}}"-->
      <!--  class="add-bookshelf"-->
      <!--  type="button"-->
      <!--  value="加入书架"-->
      <!--  @click="addBookShelf"-->
      <!--/>-->
      <input class="read" type="button" value="马上阅读" @click="toDetail" />
    </div>
  </div>
</template>

<script>
import { addBookShelf, getBookInfoData } from '@/api/comics'
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    info: {
      author: '悦忱',
      category: '热血兵王',
      chapterCount: 1460,
      cover:
        'http://img.1391.com/api/v1/bookcenter/cover/1/3369879/3369879_750203615b7045148a9db83cecc3e002.jpg',
      id: 1,
      introduction:
        '「都市+爽文+热血」狂尊归来，我为主宰，恩仇必报，逆者必杀，天逆弑天，神逆弑神！狂尊归来战都市，了却恩怨情仇事！四年腥风血雨路，绝弑狂尊为谁戮？我自狂傲逆天行，吾名景昊弑神冥！',
      name: '都市绝弑狂尊',
      tags: '兵王,城市,异术超能,异能,热血兵王,爽文,美女,都市',
      words: 3358878,
      firstChapterId: null,
      isInShelf: false,
      chapterId: null, // 读到到章节
    },
    showDraw: false,
    showAddBookShelf: true,
  },

  protected: {
    id: 1, // 小说id
  },

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) {}
    getBookInfoData({ id: this.id }).then(res => {
      if (res) {
        this.info = res
        this.showAddBookShelf = !res.isInShelf
      }
    })
  },

  toDetail() {
    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: this.id,
        chapterId: this.info.chapterId || this.info.firstChapterId,
      },
    })
  },

  handleClickItem({ detail }) {
    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: detail.bookId,
        chapterId: detail.id,
      },
    })
  },

  addBookShelf() {
    addBookShelf([this.info.id])
      .then(() => {
        showToast('成功加入书架')
        this.showAddBookShelf = false
      })
      .catch(() => {
        showToast('失败加入书架')
      })
  },

  back() {
    router.back()
  },
})
</script>

<style lang="less">
.page {
  flex-direction: column;
  padding: 0 0 188px;
}

.top-box {
  height: 520px;
  .cover {
    width: 100%;
    height: 100%;
  }

  &__mark {
    width: 100%;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.9) 0%,
      #ffffff 100%
    );
  }

  &__info {
    margin-top: 200px;
    padding: 0 48px;

    &__cover {
      width: 240px;
      height: 320px;
      border: 2px solid #000000;
    }

    &__right {
      flex-direction: column;
      margin-left: 48px;

      &__title {
        font-size: 40px;
        font-weight: bolder;
        color: #000000;
        line-height: 56px;
      }

      &__category {
        font-size: 28px;
        margin-top: 8px;
        color: #666666;
        line-height: 40px;
      }

      &__add-btn {
        width: 192px;
        height: 80px;
        margin-top: auto;
        font-size: 28px;
        font-weight: bolder;
        color: #ffffff;
        background-color: #7b5cff;
        border-radius: 8px;
        border: 4px solid #000000;
      }
    }
  }
}

.back-btn {
  position: fixed;
  left: 40px;
  top: 118px;
  width: 44px;
}

.ad-box {
  border-radius: 20px;
  margin-top: 40px;
  justify-content: center;
}

.introduction {
  flex-direction: column;
  background-color: white;
  padding: 24px;
  margin: 40px 40px 0;
  border: 2px solid #000;

  &__title {
    font-size: 32px;
    font-weight: bolder;
    color: #000000;
    margin-bottom: 24px;
  }

  &__content {
    font-size: 28px;
    color: #565656;
    line-height: 42px;
  }
}

.chapter-box {
  background-color: white;
  padding: 0 32px;
  border: 2px solid #000;
  margin: 0 48px;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750px;
  height: 168px;
  padding-top: 32px;
  padding-right: 32px;
  padding-left: 32px;
  justify-content: flex-end;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px -2px 6px 0px #e8e8e8;
  backdrop-filter: blur(10px);
  border-top: 1px solid #eee;

  input {
    text-align: center;
    height: 84px;
    font-size: 32px;
    color: white;
    border-radius: 12px;
  }

  .add-bookshelf {
    width: 218px;
    background: linear-gradient(270deg, #ff9c07 0%, #ff7010 100%);
    border-radius: 24px;
    padding: 0 24px;
    margin-right: 24px;
  }

  .read {
    flex: 1;
    height: 112px;
    background-color: #7b5cff;
    border-radius: 8px;
    border: 4px solid #000000;
    font-size: 32px;
    font-weight: bolder;
    color: #ffffff;
  }
}
</style>
