<import name="chapter-list" src="../../../components/chapter-list"></import>
<!-- <import name="intad" src="@quickapp/business/lib/intad.ux"></import>
<import name="nativead" src="@quickapp/business/lib/nativead.ux"></import>
<import
  name="nativebanner"
  src="@quickapp/business/lib/nativebanner.ux"
></import> -->

<template>
  <div class="wrapper" @click="handleClick">
    <!-- <intad
      if="showIntAd"
      @close="onIntAdClose"
      event-name="ad_essay_insert"
    ></intad> -->
    <div class="header" @click="stopPropagation">
      <image src="/assets/images/ic_back.webp" @click="toBack"></image>
      <text class="header__title">{{ content.chapterName }}</text>
    </div>
    <list id="imglist" class="list-img-box">
      <list-item type="img-item" for="item in content.imgList" tid="$idx">
        <div class="text-ad-container" if="item === 'ad'">
          <!-- <nativead
            id="nativead"
            event-name="ad_essay"
            show-ad-free="{{true}}"
          ></nativead> -->
        </div>
        <image class="img" src="{{item}}" else></image>
      </list-item>
      <list-item type="adspace">
        <div style="height: 150px"></div>
      </list-item>
    </list>
    <div if="showSetting" class="operate" @click="stopPropagation">
      <text
        class="prev"
        @click="switchChapter(content.preChapterId, '没有上一章了')"
      >
        上一章
      </text>
      <text class="chapter" @click="handleClickChapter">目录</text>
      <text
        class="next"
        @click="switchChapter(content.nextChapterId, '没有下一章了')"
      >
        下一章
      </text>
    </div>

    <div
      show="{{chapterVisible}}"
      @click="stopPropagation"
      class="chapter-wrapper"
    >
      <div class="chapter-wrapper__header" @click="hildeChapter"></div>
      <list>
        <list-item type="item">
          <chapter-list
            book-id="{{bookId}}"
            @click-item="handleClickItem"
          ></chapter-list>
        </list-item>
      </list>
    </div>
    <div if="{{false}}" class="ad-container">
      <!-- <nativebanner id="banner" event-name="ad_essay_bottom"></nativebanner> -->
    </div>
  </div>
</template>

<script>
import { getChapterContentData } from '@/api/comics'
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    content: {
      bookId: null,
      chapterId: null,
      chapterName: '01话 我在婚配所摇到了世界首富？',
      cover: null,
      imgList: [
        'http://cooperator.1391.com/api/v1/bookcenter/scenethumbnail/0/176415/U_0_0_758a9736-2eca-4f69-866a-993cc6437979.jpg',
        'http://cooperator.1391.com/api/v1/bookcenter/scenethumbnail/0/176415/U_0_0_758a9736-2eca-4f69-866a-993cc6437979.jpg',
        'http://cooperator.1391.com/api/v1/bookcenter/scenethumbnail/0/176415/U_0_0_758a9736-2eca-4f69-866a-993cc6437979.jpg',
        'http://cooperator.1391.com/api/v1/bookcenter/scenethumbnail/0/176415/U_0_0_758a9736-2eca-4f69-866a-993cc6437979.jpg',
        'http://cooperator.1391.com/api/v1/bookcenter/scenethumbnail/0/176415/U_0_0_758a9736-2eca-4f69-866a-993cc6437979.jpg',
        'http://cooperator.1391.com/api/v1/bookcenter/scenethumbnail/0/176415/U_0_0_758a9736-2eca-4f69-866a-993cc6437979.jpg',
      ],
      introduction: null,
      isLock: null,
      nextChapterId: null,
      preChapterId: null,
    },
    showIntAd: false,
    showIntCount: 0,
    showSetting: false,
    chapterVisible: false,
  },

  protected: {
    bookId: null,
    chapterId: null,
  },

  onInit() {
    this.getData()
  },

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) {}
    try {
      this.$child('banner').reportNativeShow()
    } catch (e) {}
  },

  toBack() {
    router.back()
  },

  getData(chapterId = this.chapterId) {
    if (this.bookId && this.chapterId) {
      getChapterContentData({
        bookId: this.bookId,
        chapterId,
      }).then(res => {
        if (res) {
          res.imgList.splice(1, 0, 'ad')
          this.content = res

          this.$element('imglist').scrollTo({
            index: 0,
          })
        }
      })
    }
  },

  handleClick(evt) {
    const windowWidth = 750
    const clientX = evt.clientX
    const efficientAreaWidth = windowWidth / 3

    if (clientX > efficientAreaWidth && clientX < efficientAreaWidth * 2) {
      this.showSetting = !this.showSetting
    } else {
      this.showSetting = false
    }

    this.hildeChapter()
  },

  switchChapter(chapterId, message) {
    if (chapterId) {
      this.getData(chapterId)
      if (this.showIntCount % 2 === 0) {
        this.showIntAd = true
      }
      this.showIntCount++
    } else {
      showToast(message)
    }
  },

  onIntAdClose() {
    this.showIntAd = false
  },

  stopPropagation(evt) {
    evt.stopPropagation()
  },

  hildeChapter() {
    this.chapterVisible = false
  },

  showChapter() {
    this.chapterVisible = true
  },

  handleClickChapter() {
    this.showChapter()
    this.showSetting = false
  },

  handleClickItem({ detail }) {
    this.hildeChapter()
    this.getData(detail.id)
  },
})
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
}

.list-img-box {
  flex: 1;
  .img {
    width: 100%;
  }
}

.header {
  position: fixed;
  top: 102px;
  left: 0;
  width: 100%;
  padding: 0 30px;

  text {
    font-size: 32px;
    font-weight: bold;
  }

  image {
    width: 60px;
    height: 60px;
  }
}

.ad-container {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 150px;
}

.text-ad-container {
  flex-direction: column;
}

.operate {
  position: fixed;
  left: 50px;
  bottom: 55px;
  width: 650px;
  height: 140px;
  padding: 0 40px;
  justify-content: space-between;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  backdrop-filter: blur(10px);

  text {
    color: white;
  }

  .prev,
  .next {
    font-size: 32px;
    color: #ffffff;
  }

  .chapter {
    font-size: 36px;
    font-weight: bold;
    color: #ffffff;
  }
}

.chapter-wrapper {
  flex-direction: column;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 800px;
  //background-color: #141624;
  background-color: #fefefe;
  padding: 50px;
  border-top: 1px solid #eee;

  &__header {
    width: 64px;
    height: 12px;
    margin-bottom: 20px;
    background-color: #141624;
    border-radius: 6px;
  }
}
</style>
