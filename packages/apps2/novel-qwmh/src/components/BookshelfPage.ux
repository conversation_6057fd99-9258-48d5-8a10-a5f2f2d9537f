<import name="apex-icon" src="@quickapp/apex-ui/components/icon/index"></import>
<import name="no-data" src="./no-data.ux"></import>

<template>
  <div class="wrapper">
    <div class="header">
      <text class="header__title">我的书架</text>
    </div>
    <block if="{{list && list.length}}">
      <div class="operate">
        <text if="{{!isEdit}}" class="operate__edit" @click="handleEdit">
          编辑
        </text>
        <text else class="operate__complete" @click="handleComplete">完成</text>
      </div>
      <list class="container" columns="3" layout-type="grid">
        <list-item
          type="item"
          class="item"
          for="item in list"
          tid="$idx"
          @click="handleItemClick(item)"
        >
          <image class="item__cover" src="{{item.cover}}"></image>
          <text class="item__name">{{ item.name }}</text>
          <text class="item__desc">1话/{{ item.chapterCount }}话</text>
          <block if="{{isEdit}}">
            <image
              if="{{item.isChecked}}"
              class="item__icon"
              src="/assets/images/ic_checked.webp"
            ></image>
            <image
              else
              class="item__icon"
              src="/assets/images/ic_not_checked.webp"
            ></image>
          </block>
        </list-item>
      </list>
      <div if="{{isEdit}}" class="delete-btn" @click="handleDelete">
        <apex-icon type="trash" size="36" color="#fff"></apex-icon>
        <text>删除</text>
      </div>
    </block>
    <div class="no-data-wrapper" else>
      <no-data></no-data>
    </div>
  </div>
</template>

<script>
import { getBookShelf, removeBookShelf } from '@/api/comics'
import { showToast } from '@quickapp/utils'
import router from '@system.router'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data() {
    return {
      list: [],
      isEdit: false,
      removeList: new Set(),
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    this.getData()
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('isEdit', 'handleChangeIsEdit')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    } else {
      this.isEdit = false
    }
  },
  handleChangeIsEdit(isEdit) {
    if (!isEdit) {
      this.list.forEach(it => (it.isChecked = false))
      this.removeList.clear()
    }
  },
  handleEdit() {
    this.isEdit = true
  },
  handleComplete() {
    this.isEdit = false
  },
  getData() {
    getBookShelf().then(res => {
      if (res && res.myBookshelf) {
        this.list = res.myBookshelf.map(it => ({
          ...it,
          isChecked: false,
        }))
      }
    })
  },
  handleItemClick(item) {
    if (this.isEdit) {
      item.isChecked = !item.isChecked
      if (item.isChecked) {
        this.removeList.add(item.id)
      } else {
        this.removeList.delete(item.id)
      }
    } else {
      router.push({
        uri: 'pages/Content/Introduce',
        params: {
          id: item.id,
        },
      })
    }
  },
  removeBookShelf() {
    removeBookShelf([...this.removeList])
      .then(() => {
        this.getData()
        this.removeList.clear()
      })
      .catch(() => {
        showToast('删除失败')
      })
  },
  handleDelete() {
    if (this.removeList.size === 0) {
      showToast('请先选择要删除的书')
    } else if (this.removeList.size === this.list.length) {
      showToast('至少保留一本书')
    } else {
      this.removeBookShelf()
    }
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px 40px;
}

.header {
  position: relative;
  width: 100%;
  height: 88px;
  justify-content: center;
  align-items: center;

  text {
    font-size: 36px;
    font-weight: bold;
    color: #333333;
  }
}

.operate {
  width: 100%;
  justify-content: flex-end;
}

.container {
  flex: 1;
  width: 100%;
  justify-content: space-between;
  padding-bottom: 20px;

  .item {
    flex-direction: column;
    margin-top: 40px;
    padding: 0 20px;

    &__cover {
      width: 100%;
      height: 280px;
      border-radius: 12px;
    }

    &__name {
      margin-top: 24px;
      font-size: 32px;
      font-weight: bold;
      color: #333333;
      lines: 1;
      text-overflow: ellipsis;
    }

    &__desc {
      margin-top: 16px;
      font-size: 24px;
      color: #77292938;
      lines: 1;
      text-overflow: ellipsis;
    }

    &__icon {
      position: absolute;
      right: 36px;
      top: 16px;
      width: 40px;
      height: 40px;
    }
  }
}

.delete-btn {
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 84px;
  background-color: #ff6565;
  border-radius: 12px;
  text {
    font-size: 32px;
    color: white;
  }
}

.no-data-wrapper {
  flex: 1;
  align-items: center;
  justify-content: center;
}
</style>
