<import name="list-view" src="./ListView.ux"></import>

<template>
  <div class="wrapper">
    <swiper
      class="swiper"
      autoplay="true"
      index="{{swiperOpt.index}}"
      interval="{{swiperOpt.interval}}"
      indicator="{{swiperOpt.indicator}}"
    >
      <block if="{{hotRecommend && hotRecommend.length}}">
        <image
          @click="handleSwiperClick(item)"
          class="header-img"
          src="{{item.cover}}"
          for="item in hotRecommend"
          tid="$idx"
        ></image>
      </block>
      <text else class="swiper__default">{{ appName }}</text>
    </swiper>
    <div class="top-box">
      <!--<div class="top-box__header">-->
      <!--  <text class="top-box__header__title">精选漫画</text>-->
      <!--</div>-->
      <list class="top-list">
        <list-item
          type="item"
          class="top-list__item"
          for="item in recommend"
          tid="$idx"
          @click="toIntroduce(item)"
        >
          <image src="{{item.cover}}"></image>
          <text class="top-list__item__name">{{ item.name }}</text>
          <text class="top-list__item__category">{{ item.category }}</text>
        </list-item>
      </list>
    </div>

    <div class="hot-list">
      <div class="hot-list__header">
        <text class="hot-list__header__title">热门推荐</text>
        <div @click="toListPage">
          <text class="hot-list__header__more">更多</text>
          <image
            class="hot-list__header__icon-arrow"
            src="/assets/images/ic_arrow_right.webp"
          ></image>
        </div>
      </div>
      <list-view list="{{comicList}}"></list-view>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookStore } from '@/api/comics'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data() {
    return {
      recommend: [
        {
          id: 1,
          cover: '/assets/images/logo.png',
          name: '开局地摊卖大力1',
          introduction: '简介',
        },
      ],
      hotRecommend: [],
      comicList: [],
      swiperOpt: {
        index: 0,
        interval: 5000,
        indicator: false,
      },
      appName: __MANIFEST__.name,
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    this.handleChangeIndex()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      getBookStore().then(res => {
        this.recommend = res.recommend
        this.hotRecommend = res.hotRecommend
        this.comicList = res.comicList
      })
    }
  },
  toListPage() {
    router.push({
      uri: 'pages/List',
    })
  },
  toIntroduce(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
  handleSwiperClick(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding-bottom: 40px;
}

.swiper {
  width: 100%;
  height: 640px;
  background-color: #ffffff;

  .header-img {
    width: 100%;
  }

  &__default {
    font-size: 66px;
    text-align: center;
    font-weight: bold;
  }
}

.top-box {
  width: 690px;
  flex-direction: column;
  border-radius: 32px;
  margin-top: -48px;

  &__header {
    &__title {
      font-size: 40px;
      font-weight: bolder;
      color: #ffffff;
      line-height: 56px;
      margin-bottom: 32px;
    }
  }

  .top-list {
    width: 100%;
    flex-direction: row;
    height: 450px;

    &__item {
      width: 230px;
      flex-direction: column;
      box-sizing: border-box;
      margin: 0 8px;
      border-radius: 16px;

      image {
        width: 100%;
        height: 300px;
      }

      &__name {
        width: 100%;
        margin-top: 24px;
        font-size: 32px;
        font-weight: bold;
        color: white;
        line-height: 44px;
        text-overflow: ellipsis;
        lines: 2;
      }

      &__category {
        font-size: 24px;
        color: #ffd819;
        line-height: 34px;
        margin-top: auto;
      }
    }
  }
}

.hot-list {
  width: 690px;
  flex-direction: column;
  border-radius: 20px;
  margin-top: 64px;

  &__header {
    justify-content: space-between;
    margin-bottom: 34px;
    align-items: center;

    &__title {
      font-size: 40px;
      font-weight: bold;
      color: #fff;
    }

    &__more {
      margin-right: -15px;
      color: white;
    }

    &__icon-arrow {
      width: 40px;
      height: 40px;
    }
  }
}
</style>
