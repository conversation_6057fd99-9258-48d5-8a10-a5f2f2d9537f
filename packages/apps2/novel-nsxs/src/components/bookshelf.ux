<import name="bookshelf-list" src="./bookshelf-list.ux"></import>

<template>
  <div class="wrapper">
    <div if="{{swiperList && swiperList.length}}" class="swiper-box">
      <swiper
        class="swiper"
        autoplay="true"
        index="{{swiperOpt.index}}"
        interval="{{swiperOpt.interval}}"
        indicator="{{swiperOpt.indicator}}"
      >
        <block for="value in swiperList">
          <div if='{{value.dataType === "ad"}}'>
            <text>广告容器</text>
          </div>
          <stack class="item__wrapper" else @click="toRead(value)">
            <div class="item__wrapper__top">
              <div class="item__wrapper__top__info">
                <text class="item__wrapper__top__info__title">
                  {{ value.name }}
                </text>
                <!-- <text class="item__wrapper__top__info__auth">
                  {{ value.author }}
                </text> -->
                <text class="item__wrapper__top__info__desc">
                  {{ value.introduction }}
                </text>
              </div>
            </div>
            <image class="item__wrapper__cover" src="{{value.cover}}"></image>
          </stack>
        </block>
      </swiper>
    </div>
    <div class="bookshelf-box">
      <bookshelf-list
        @delete-success="handleDeleteSuccess"
        novel-list="{{novelList}}"
      ></bookshelf-list>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookshelfData } from '@/api/bookshelf'

export default {
  data: {
    title: '书架.',
    swiperOpt: {
      index: 0,
      interval: 3000,
      indicator: false,
    },
    swiperList: [],
    novelList: [],
  },

  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    }
  },

  handleLifeCycleShow() {
    this.handleChangeIndex()
  },

  getData() {
    return getBookshelfData().then(res => {
      console.log('res', res)
      if (res) {
        if (res.recommendList) {
          // 插入广告
          // this.swiperList = res.recommendList
          //   .map(it => [{ ...it, dataType: 'novel' }, { dataType: 'ad' }])
          //   .flat()
          this.swiperList = res.recommendList
        }
        if (res.myBookshelfRecommend) {
          this.novelList = res.myBookshelfRecommend.map(it => ({
            ...it,
            isChecked: false,
          }))
        }
        console.log(
          'this.swiperList',
          JSON.stringify(Object.keys(this.swiperList[0]))
        )
      }
    })
  },

  toRead(item) {
    console.log('read....')
    router.push({
      uri: 'pages/novel/introduce',
      params: {
        id: item.id,
      },
    })
  },

  handleDeleteSuccess() {
    this.$emit('deleteSuccess')
    this.getData()
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* background-color: #33b4a9; */
}

.header {
  justify-content: center;
  width: 100%;
  height: 88px;
  padding: 0 30px;

  .text {
    font-size: 36px;
    text-align: center;
    font-weight: bolder;
    color: #333333;
  }
}

.swiper-box {
  margin-top: 20px;
  padding: 0 20px;

  .swiper {
    width: 100%;
    height: 234px;
    border-radius: 20px;
  }

  .item__wrapper {
    width: 670px;
    height: 100%;

    &__cover {
      width: 132px;
      height: 186px;
      border-radius: 12px;
      margin-left: 32px;
      margin-top: 24px;
    }

    &__top {
      height: 100%;
      padding-left: 180px;
      background-color: #fff;
      padding-right: 32px;
      border-radius: 16px;

      &__info {
        flex-direction: column;
        padding-top: 24px;

        &__title {
          font-size: 32px;
          font-weight: bolder;
          color: #292938;
        }

        &__auth {
          font-size: 24px;
          color: #999999;
          margin-top: 12px;
        }

        &__desc {
          lines: 3;
          text-overflow: ellipsis;
          margin-top: 24px;
          font-size: 24px;
          color: #666666;
          line-height: 34px;
        }
      }
    }
  }
}

.bookshelf-box {
  margin-top: 32px;
  background-color: white;
  //padding: 40px;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px;
}
</style>
