<template>
  <div class="container">
    <web
      id="web"
      class="web-page"
      src="{{webSrc}}"
      trustedurl="{{list}}"
      onpagestart="onPageStart"
      onpagefinish="onPageFinish"
      ontitlereceive="onTitleReceive"
      onerror="onError"
      onmessage="onMessage"
    ></web>
  </div>
</template>
<style>
.container {
  flex-direction: column;
}
.web-page {
  width: 100%;
  height: 100%;
}
</style>
<script>
import router from '@system.router'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    arrowRightShow: true,
    isRefreshing: false,
    list: [/.*/, "new RegExp('https?.*')"],
    msg: '',
  },
  protected: {
    webSrc: '',
    title: '',
  },
  onInit() {
    this.$on('arrowLeft', this.arrowLeftIcon)
    this.$on('arrowRight', this.arrowRightIcon)
    console.log('webSrc ==>', this.webSrc)
  },
  onPageStart() {
    this.isRefreshing = false
  },
  // 每次页面的切换会触发
  onPageFinish(e) {
    // console.info('pagefinish: '+e.url, e.canBack, e.canForward)
    // 根据数据是否可以前进历史页面，隐藏右侧图标
    this.arrowRightShow = e.canForward
  },
  onTitleReceive(e) {
    console.error('onTitleReceive', e.title)
    this.title = e.title
    this.$page.setTitleBar({ text: this.title })
  },
  onError() {
    console.info('pageError')
  },
  onMessage(e) {
    this.msg = e.message
  },
  arrowLeftIcon() {
    this.isCanBack()
  },
  arrowRightIcon() {
    this.isCanForward()
  },
  isCanForward() {
    this.$element('web').canForward({
      callback: function (e) {
        if (e) {
          this.$element('web').forward()
        }
      }.bind(this),
    })
  },
  isCanBack() {
    this.$element('web').canBack({
      callback: function (e) {
        if (e) {
          this.$element('web').back()
        } else {
          router.back()
        }
      }.bind(this),
    })
  },
  back() {
    this.$dispatch('arrowLeft')
  },
  next() {
    this.$dispatch('arrowRight')
  },
  refresh: function (e) {
    this.$element('web').reload()
  },
  sendMessage: function () {
    this.$element('web').postMessage({ message: 'message to Web page' })
  },
})
</script>
