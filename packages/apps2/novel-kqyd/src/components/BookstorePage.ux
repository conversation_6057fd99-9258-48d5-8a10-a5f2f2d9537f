<import name="list-view" src="./ListView.ux"></import>

<template>
  <div class="wrapper">
    <swiper
      class="swiper"
      autoplay="true"
      index="{{swiperOpt.index}}"
      interval="{{swiperOpt.interval}}"
      indicator="{{swiperOpt.indicator}}"
    >
      <block if="{{hotRecommend && hotRecommend.length}}">
        <div class="header" for="item in hotRecommend" tid="$idx">
          <image
            @click="handleSwiperClick(item)"
            class="header__img"
            src="{{item.cover}}"
          ></image>
          <div class="header__content">
            <text class="header__content__title">{{ item.name }}</text>
            <text class="header__content__desc">
              更新至{{ item.chapterCount }}话
            </text>
          </div>
        </div>
      </block>
      <text else class="swiper__default">{{ appName }}</text>
    </swiper>
    <div class="top-box">
      <div class="top-list">
        <div
          class="top-list__item"
          if="$idx < 3"
          for="item in recommend"
          tid="$idx"
          @click="toIntroduce(item)"
        >
          <image src="{{item.cover}}"></image>
          <text>{{ item.name }}</text>
          <text class="type">{{ item.category }}</text>
        </div>
      </div>
    </div>

    <div class="hot-list">
      <div class="hot-list__header">
        <div>
          <text class="hot-list__header__title">热门推荐</text>
          <image
            class="hot-list__header__img"
            src="/assets/images/hot.png"
          ></image>
        </div>
        <div @click="toListPage">
          <text class="hot-list__header__more">更多</text>
          <image
            class="hot-list__header__icon-arrow"
            src="/assets/images/ic_arrow_right.webp"
          ></image>
        </div>
      </div>
      <list-view list="{{comicList}}"></list-view>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookStore } from '@/api/comics'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data() {
    return {
      recommend: [
        {
          id: 1,
          cover: '/assets/images/logo.png',
          name: '开局地摊卖大力1',
          introduction:
            '地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代',
        },
      ],
      hotRecommend: [],
      comicList: [],
      swiperOpt: {
        index: 0,
        interval: 5000,
        indicator: false,
      },
      appName: __MANIFEST__.name,
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    this.handleChangeIndex()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      getBookStore().then(res => {
        this.recommend = res.recommend
        this.hotRecommend = res.hotRecommend
        this.comicList = res.comicList
      })
    }
  },
  toListPage() {
    router.push({
      uri: 'pages/List',
    })
  },
  toIntroduce(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
  handleSwiperClick(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding-bottom: 40px;
}

.swiper {
  height: 520px;

  .header {
    position: relative;

    &__img {
      width: 100%;
      height: 520px;
    }

    &__content {
      position: absolute;
      left: 0;
      top: 380px;
      margin-left: 30px;
      margin-right: 30px;
      //height: 236px;
      flex-direction: column;

      text {
        margin: 5px 20px 0 0;
      }

      &__title {
        font-size: 38px;
        font-weight: bold;
        color: white;
        lines: 1;
      }

      &__desc {
        font-size: 26px;
        color: rgba(255, 255, 255, 0.5);
        /* lines: 2; */
      }
    }
  }

  &__default {
    font-size: 66px;
    text-align: center;
    font-weight: bold;
  }
}

.top-box {
  margin-top: -40px;
  background-color: #ffffff;
  width: 100%;
  border-radius: 64px;

  .top-list {
    width: 100%;
    margin-top: 40px;
    justify-content: space-between;
    padding: 32px 17px;
    box-sizing: border-box;

    &__item {
      flex: 1;
      flex-direction: column;
      /* align-items: center; */
      box-sizing: border-box;
      margin: 0 20px;
      border-radius: 32px;

      image {
        width: 100%;
        height: 296px;
        border-radius: 20px;
      }

      text {
        margin-top: 10px;
        font-size: 28px;
        font-weight: bold;
        color: black;
        line-height: 36px;
        text-overflow: ellipsis;
        lines: 2;
      }

      .type {
        color: #ffa600;
        font-size: 24px;
      }
    }
  }
}

.hot-list {
  width: 690px;
  flex-direction: column;
  border-radius: 20px;
  background-color: #ffffff;
  margin-top: 20px;
  padding: 34px 30px;

  &__header {
    justify-content: space-between;
    margin-bottom: 34px;
    align-items: center;

    &__title {
      font-size: 40px;
      font-weight: bold;
      color: black;
      line-height: 40px;
    }

    &__img {
      width: 22px;
      height: 28px;
      margin-left: 12px;
      align-self: center;
    }

    &__more {
      margin-right: -15px;
    }

    &__icon-arrow {
      width: 40px;
      height: 40px;
    }
  }
}
</style>
