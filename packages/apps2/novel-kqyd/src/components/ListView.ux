<template>
  <div class="list">
    <div class="item" for="item in list" tid="$idx" @click="toIntroduce(item)">
      <image class="item__cover" src="{{item.cover}}"></image>
      <div class="item__info">
        <div class="item__info__header">
          <text class="item__info__title">{{ item.name }}</text>
          <text class="item__info__header__chapter">
            {{ item.chapterCount }}卷
          </text>
        </div>
        <text class="item__info__introduction">{{ item.introduction }}</text>
        <div class="item__info__other">
          <text class="item__info__other__type">{{ item.category }}</text>
          <!-- <text class="item__info__other__chapter">
            {{ item.chapterCount }}卷
          </text> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  props: {
    list: {
      type: Array,
      default: () => [
        {
          id: 4,
          cover: '/assets/images/2.webp',
          introduction:
            '海鸥学园流传着七大不可思议的奇妙传言。旧校舍3楼的女生厕所的第...',
          name: '九星霸体2222221九星霸体诀1222222',
          category: '热血',
          chapterCount: 12,
        },
      ],
    },
  },
  toIntroduce(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.list {
  flex-direction: column;

  .item {
    margin-bottom: 30px;

    &__cover {
      width: 196px;
      height: 264px;
      border-radius: 24px;
    }

    &__info {
      flex: 1;
      flex-direction: column;
      margin-left: 24px;
      padding-top: 12px;
      padding-bottom: 14px;
      box-sizing: border-box;
      &__header {
        display: flex;
        justify-content: space-between;
        &__chapter {
          color: #b4b4b4;
        }
      }

      &__title {
        font-size: 32px;
        font-weight: bold;
        color: #333;
        lines: 1;
        text-overflow: ellipsis;
      }

      &__introduction {
        font-size: 28px;
        margin-top: 12px;
        color: #999999;
        line-height: 42px;
        lines: 2;
        text-overflow: ellipsis;
      }

      &__other {
        margin-top: auto;

        text {
          padding: 8px 16px;
          font-size: 24px;
          color: #ffa600;
          border-radius: 16px;
        }

        &__type {
          margin-right: 20px;
          //background-color: #E5DEFF;
        }

        &__chapter {
          //background-color: #DEE9FF;
        }
      }
    }
  }
}
</style>
