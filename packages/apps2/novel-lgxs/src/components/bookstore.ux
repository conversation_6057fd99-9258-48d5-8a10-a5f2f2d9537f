<import name="bookstore-list" src="./bookstore-list.ux"></import>

<template>
  <div class="wrapper">
    <list if="{{hotList && hotList.length}}" class="header-list">
      <list-item
        type="item"
        class="header-list-item"
        for="item in hotList"
        tid="$idx"
        @click="toRead(item)"
      >
        <!--<div class="header-list-item__bg"></div>-->
        <image class="img" src="{{item.cover}}"></image>
        <div class="info">
          <text class="text">{{ item.name }}</text>
          <text class="author">{{ item.author }}</text>
        </div>
      </list-item>
    </list>
    <div class="list-box">
      <bookstore-list
        @see-more="handleSeeMoreEvt"
        category-list="{{categoryList}}"
      ></bookstore-list>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookstoreData } from '@/api/bookstore'

export default {
  data: {
    hotList: [],
    categoryList: [],
  },

  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    showSeeMore: {
      type: Boolean,
      default: true,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    }
  },

  handleLifeCycleShow() {
    this.handleChangeIndex()
  },

  handleSeeMoreEvt({ detail }) {
    router.push({
      uri: 'pages/bookstore-detail',
      params: {
        title: detail.category,
        id: detail.id,
      },
    })
  },

  toRead(item) {
    router.push({
      uri: 'pages/novel/introduce',
      params: {
        id: item.id,
      },
    })
  },

  getData() {
    return getBookstoreData().then(res => {
      console.log('categoryList')
      console.log(JSON.stringify(res.categoryList))
      console.log(JSON.stringify(res.categoryList[0].bookList))
      if (res) {
        if (res.hotRecommend) {
          this.hotList = res.hotRecommend
        }
        if (res.categoryList) {
          this.categoryList = res.categoryList
        }
      }
    })
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 24px;
  /* background: linear-gradient(180deg, #F25C62 0%, #ffffff 100%); */
  background-color: #fffaf7;
}

.header-list {
  flex-direction: row;
  height: 424px;
  //height: 320px + 74px;
  padding: 24px 20px 0;
  /* background: linear-gradient(180deg, #F25C62 0%, #ffffff 100%); */
}

.header-list-item {
  margin: 0 16px;
  flex-direction: column;
  align-items: center;
  width: 240px;
  height: 400px;
  background-color: #fff;
  border-radius: 12px;

  .img {
    width: 100%;
    height: 320px;
    border-radius: 16px;
  }
  .info {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-bottom: 10px;

    .text {
      text-align: left;
      width: 100%;
      font-size: 32px;
      text-align: center;
      font-weight: bold;
      color: #292938;
      line-height: 36px;
      lines: 1;
      margin-top: 16px;
      text-overflow: ellipsis;
    }

    .author {
      margin-top: 24px;
      text-align: center;
      width: 100%;
      font-size: 24px;
      color: #292938;
      opacity: 0.4;
    }
  }
}

.list-box {
  /* background-color: #33b4a9; */
  /* margin-top: 64px; */
  padding: 0 28px;
  /* border-top-left-radius: 48px; */
  /* border-top-right-radius: 48px; */
}
</style>
