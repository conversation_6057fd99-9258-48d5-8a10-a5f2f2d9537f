<template>
  <div class="c-shujia-list">
    <div class="box"></div>
    <div class="header">
      <image src="/assets/images/ic_bookshelf2.webp"></image>
      <text class="title-txt">我的书架</text>
      <div class="operation">
        <block if="{{isEdit}}">
          <text class="btn btn-delete" @click="handleDelete">删除</text>
          <text class="btn btn-complete" @click="compelet">完成</text>
        </block>
        <text else class="btn btn-edit" @click="edit">编辑</text>
      </div>
    </div>
    <div if="{{novelList && novelList.length > 0}}" class="content">
      <div class="list">
        <div
          class="item"
          for="value in novelList"
          tid="$idx"
          @click="toRead(value)"
        >
          <image class="cover" src="{{value.cover}}"></image>
          <block if="{{isEdit}}">
            <image
              if="{{!value.isChecked}}"
              class="ic-checked"
              src="/assets/images/ic_not_checked.webp"
            ></image>
            <image
              else
              class="ic-checked"
              src="/assets/images/ic_checked.webp"
            ></image>
          </block>
          <div class="info">
            <text class="title">{{ value.name }}</text>
            <text class="desc">{{ value.introduction }}</text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { removeBookShelf } from '@/api/bookshelf'
import utils from '@/helper/utils'

export default {
  data: {
    isEdit: false,
  },
  props: {
    novelList: {
      type: Array,
      default: () => [
        {
          id: 2,
          cover: '/assets/images/1.webp',
          name: '开局地摊卖大力2222',
          introduction:
            '地球进入灵气复苏时代，人类开启异能觉醒1,地球进入灵气复苏时代，人类开启异能觉醒1,地球进入灵气复苏时代，人类开启异能觉醒1,地球进入灵气复苏时代，人类开启异能觉醒1',
          isChecked: false,
        },
      ],
    },
  },
  edit() {
    this.isEdit = true
  },
  compelet() {
    this.isEdit = false
    this.novelList.forEach(it => {
      it.isChecked = false
    })
  },
  handleDelete() {
    const ids = this.novelList.filter(it => it.isChecked).map(it => it.id)

    if (!ids || !ids.length) {
      return utils.showToast('请先选择要删除的书')
    }

    if (ids.length === this.novelList.length) {
      return utils.showToast('请至少保留一本小说')
    }

    removeBookShelf({ idList: ids })
      .then(res => {
        if (res.ok) {
          this.$emit('deleteSuccess')
        } else {
          utils.showToast('删除失败')
        }
      })
      .catch(e => {
        utils.showToast('删除失败')
      })
  },
  toRead(item) {
    if (this.isEdit) {
      item.isChecked = !item.isChecked
      return
    }

    router.push({
      uri: 'pages/novel/introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.c-shujia-list {
  flex-direction: column;
  width: 100%;
  padding-left: 40px;
  padding-top: 40px;
}
.header {
  align-items: center;
  padding-right: 40px;

  .title-txt {
    color: #292938;
    font-size: 36px;
    font-weight: bolder;
    margin-left: 12px;
  }

  .operation {
    font-size: 28px;
    margin-left: auto;

    .btn {
      margin-left: 40px;

      &-delete {
        color: #ed5b3b;
      }
    }
  }
}

.content {
  width: 100%;

  .list {
    width: 100%;
    //justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 42px;

    .item {
      flex-direction: column;
      width: 212px;
      padding-right: 40px;
      margin-bottom: 40px;
      margin-right: 18px;
      box-sizing: border-box;

      .ic-checked {
        position: absolute;
        top: 10px;
        left: 10px;
        width: 40px;
        height: 40px;
        z-index: 2;
      }

      .cover {
        width: 212px;
        height: 280px;
        border-radius: 16px;
      }

      .info {
        flex: 1;
        flex-direction: column;
        justify-content: space-between;
        padding: 18px;

        .title {
          color: #292938;
          font-size: 30px;
          font-weight: bolder;
          line-height: 45px;
          lines: 1;
          text-overflow: ellipsis;
        }

        .desc {
          font-size: 24px;
          color: rgba(41, 41, 56, 0.4);
          line-height: 24px;
          margin-top: 16px;
          lines: 1;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
